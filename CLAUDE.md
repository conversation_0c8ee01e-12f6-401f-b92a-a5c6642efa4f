# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build Commands

- `npm run build` - Build for development environment
- `npm run build:labs` - Build for labs environment
- `npm run build:prod` - Build for production environment
- `npm run build:flow-editor` - Build TypeScript for flow editor with custom tsconfig

### Development Server

- `npm start` or `npm run start` - Start development server (ng serve)
- `npm run watch` - Build and watch for changes in development mode

### Testing

- `npm test` - Run unit tests with <PERSON><PERSON> and Jasmine

## Project Architecture

### Core Structure

This is an Angular 19 application with a hybrid approach that integrates React components for flow editors. The application uses:

- **Angular 19** with standalone components and new control flow syntax
- **Angular Signals** for state management (input/output signals, computed, effect)
- **Tailwind CSS** for styling with DaisyUI integration
- **@dx-ui/ui** custom component library
- **React/TypeScript** for flow editor components
- **Zustand** for React state management in flow editors
- **Socket.io** for real-time communication
- **Monaco Editor** for code editing

### Key Directory Structure

```
src/app/
├── core/                    # Core application services, guards, stores
│   ├── constants/          # Global constants
│   ├── guards/             # Route guards (auth, role-based)
│   ├── interceptors/       # HTTP interceptors
│   ├── models/             # Core data models
│   ├── services/           # Core services (auth, etc.)
│   └── stores/             # Global state stores using @ngrx/signals
├── layouts/                # Application layout components
├── shared/                 # Reusable components, services, utils
│   ├── components/         # Shared UI components
│   ├── directives/         # Custom directives
│   ├── models/             # Feature-specific models
│   ├── services/           # Feature-specific services
│   └── utils/              # Utility functions
└── views/                  # Feature modules/pages
    ├── flow-editor/        # React-based flow editor
    ├── flow-editor-v1/     # Legacy React flow editor
    ├── studio/             # Studio management pages
    ├── inbox/              # Chat/messaging interface
    └── [other features]/
```

### Development Conventions

The project follows strict coding conventions defined in `CONVENTIONS.md`:

- **Angular Components**: Use standalone components with signals, computed, and effects
- **Dependency Injection**: Use `inject()` function instead of constructor injection
- **Import Organization**: Use barrel exports from `index.ts` files
- **State Management**: Prefer Angular Signals over traditional observables
- **File Structure**: Public properties/methods before private ones
- **Branch Naming**: `feature/`, `fixbug/`, `refactor/` prefixes with kebab-case

### Environment Configuration

- `development` - Default development environment
- `labs` - Labs environment for testing
- `production` - Production environment

### Build Configurations

- Development builds include source maps and are unoptimized
- Production builds are optimized with output hashing
- Custom TypeScript configuration for flow editor at `tsconfig.flow-editor.json`

### Key Features

- **Flow Editors**: Two React-based visual flow builders (v1 and current)
- **Real-time Chat**: Socket.io integration for messaging
- **Multi-tenant AI**: Support for multiple AI instances
- **Responsive Design**: Mobile-first approach with breakpoint service
- **Internationalization**: English and Vietnamese language support
- **Theme Support**: Light/dark mode with system preference detection

### Testing

- Unit tests run with Karma and Jasmine
- Test files follow `.spec.ts` naming convention
- Tests are configured to run in headless Chrome

### React Integration

The flow editors use React components within Angular:

- State management with Zustand
- Styled components for theming
- React Flow for visual flow building
- Monaco Editor integration for code editing

## Important Notes

- The application uses Angular 19's new control flow syntax (`@if`, `@for`, `@switch`) instead of structural directives
- Socket connections are established per AI instance with retry logic
- The project includes both current and legacy flow editor versions
- All components should be standalone and use the new Angular APIs
- Authentication uses JWT tokens with role-based access control

## Best Practices

- Always use dx-ui components for new stuff

## Coding Guidelines

- Do not include explanatory comments within the code
