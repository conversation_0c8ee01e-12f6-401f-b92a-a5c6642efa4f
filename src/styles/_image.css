.image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000000;
}

.image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000000;
}

img.zoom {
  margin-top: 1.2em;
  cursor: pointer;
  transition: transform 0.3s;
}

img.zoom.n-margin {
  cursor: pointer;
  transition: transform 0.3s;
}

.image-loader {
  width: 160px; /* Chiều rộng của khung ảnh */
  height: 160px; /* <PERSON><PERSON>u cao của khung ảnh */
  background: linear-gradient(
      90deg,
      #f0f0f0 25%,
      #e0e0e0 50%,
      #f0f0f0 75%
  );
  background-size: 200% 100%;
  border-radius: 8px; /* <PERSON><PERSON><PERSON> b<PERSON> tròn, thay đ<PERSON>i theo ý thích */
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.overlay {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.zoomed-image-container {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.zoomed-image {
  max-width: 100%;
  max-height: 100%;
  transition: transform 0.3s;
  display: block;
}