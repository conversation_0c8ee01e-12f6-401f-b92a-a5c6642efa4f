/// <reference types="@angular/localize" />

import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';

(window as any).MonacoEnvironment = {
  getWorkerUrl: function (moduleId: any, label: string) {
    return './assets/monaco/min/vs/base/worker/workerMain.js';
  }
};

bootstrapApplication(AppComponent, appConfig)
  .catch((err) => console.error(err));
