{"version": 3, "sources": ["out-editor/nls.messages.es.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\nglobalThis._VSCODE_NLS_MESSAGES=[\"{0} ({1})\",\"entrada\",\"Coincidir mayúsculas y minúsculas\",\"Solo palabras completas\",\"Usar expresión regular\",\"entrada\",\"Conservar may/min\",\"Inspeccione esto en la vista accesible con {0}.\",\"Inspeccione esto en la vista accesible mediante el comando Abrir vista accesible, que actualmente no se puede desencadenar mediante el enlace de teclado.\",\"Error: {0}\",\"Advertencia: {0}\",\"Información: {0}\",\" o {0} para el historial\",\" ({0} para el historial)\",\"Entrada borrada\",\"Sin enlazar\",\"Seleccionar cuadro\",\"Más Acciones...\",\"Filtrar\",\"Coincidencia aproximada\",\"Escriba texto para filtrar\",\"Escriba texto para buscar\",\"Escriba texto para buscar\",\"Cerrar\",\"No hay resultados\",\"No se encontraron resultados.\",null,\"(vacío)\",\"{0}: {1}\",\"Error del sistema ({0})\",\"Se ha producido un error desconocido. Consulte el registro para obtener más detalles.\",\"Se ha producido un error desconocido. Consulte el registro para obtener más detalles.\",\"{0} ({1} errores en total)\",\"Se ha producido un error desconocido. Consulte el registro para obtener más detalles.\",\"Ctrl\",\"Mayús\",\"Alt\",\"Windows\",\"Ctrl\",\"Mayús\",\"Alt\",\"Super\",\"Control\",\"Mayús\",\"Opción\",\"Comando\",\"Control\",\"Mayús\",\"Alt\",\"Windows\",\"Control\",\"Mayús\",\"Alt\",\"Super\",null,null,null,null,null,\"Anclar al final incluso cuando se vayan a líneas más largas\",\"Anclar al final incluso cuando se vayan a líneas más largas\",\"Cursores secundarios quitados\",\"&&Deshacer\",\"Deshacer\",\"&&Rehacer\",\"Rehacer\",\"&&Seleccionar todo\",\"Seleccionar todo\",\"Mantenga presionada la tecla {0} para pasar el mouse\",\"Cargando...\",\"El número de cursores se ha limitado a {0}. Considere la posibilidad de usar [buscar y reemplazar](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) para realizar cambios mayores o aumentar la configuración del límite de varios cursores del editor.\",\"Aumentar el límite de varios cursores\",\"Alternar contraer regiones sin cambios\",\"Alternar Mostrar bloques de código movidos\",\"Alternar el uso de la vista insertada cuando el espacio es limitado\",\"Editor de diferencias\",\"Lado del conmutador\",\"Salir de la comparación de movimientos\",\"Contraer todas las regiones sin cambios\",\"Mostrar todas las regiones sin cambios\",\"Revertir\",\"Visor de diferencias accesibles\",\"Ir a la siguiente diferencia\",\"Ir a la diferencia anterior\",\"Icono de \\\"Insertar\\\" en el visor de diferencias accesible.\",\"Icono de \\\"Quitar\\\" en el visor de diferencias accesible.\",\"Icono de \\\"Cerrar\\\" en el visor de diferencias accesible.\",\"Cerrar\",\"Visor de diferencias accesible. Utilice la flecha hacia arriba y hacia abajo para navegar.\",\"no se han cambiado líneas\",\"1 línea cambiada\",\"{0} líneas cambiadas\",\"Diferencia {0} de {1}: línea original {2}, {3}, línea modificada {4}, {5}\",\"vacío\",\"{0} línea sin cambios {1}\",\"{0} línea original {1} línea modificada {2}\",\"+ {0} línea modificada {1}\",\"- {0} línea original {1}\",\" use {0} para abrir la ayuda de accesibilidad.\",\"Copiar líneas eliminadas\",\"Copiar línea eliminada\",\"Copiar líneas cambiadas\",\"Copiar línea cambiada\",\"Copiar la línea eliminada ({0})\",\"Copiar línea cambiada ({0})\",\"Revertir este cambio\",\"Uso de la vista insertada cuando el espacio es limitado\",\"Mostrar bloques de código movidos\",\"Revertir bloque\",\"Revertir selección\",\"Abrir visor de diferencias accesibles\",\"Plegar la región sin cambios\",\"{0} líneas ocultas\",\"Haga clic o arrastre para mostrar más arriba\",\"Mostrar región sin cambios\",\"Hacer clic o arrastrar para mostrar más abajo\",\"{0} líneas ocultas\",\"Doble clic para desplegar\",\"Código movido con cambios en la línea {0}-{1}\",\"Código movido con cambios de la línea {0}-{1}\",\"Código movido a la línea {0}-{1}\",\"Código movido de la línea {0}-{1}\",\"Revertir los cambios seleccionados\",\"Revertir el cambio\",\"Color del borde del texto que se movió en el editor de diferencias.\",\"Color del borde de texto activo que se movió en el editor de diferencias.\",\"Color de la sombra paralela en torno a los widgets de región sin cambios.\",\"Decoración de línea para las inserciones en el editor de diferencias.\",\"Decoración de línea para las eliminaciones en el editor de diferencias.\",\"Color de fondo del encabezado del editor de diferencias\",\"Color de fondo del editor de diferencias de varios archivos\",\"Color de borde del editor de diferencias de varios archivos\",\"No hay archivos modificados\",\"Editor\",\"El número de espacios a los que equivale una tabulación. Este valor se invalida en función del contenido del archivo cuando {0} está activado.\",\"Número de espacios usados para la sangría o \\\"tabSize\\\" para usar el valor de \\\"#editor.tabSize#\\\". Esta configuración se invalida en función del contenido del archivo cuando \\\"#editor.detectIndentation#\\\" está activado.\",\"Insertar espacios al presionar \\\"TAB\\\". Este valor se invalida en función del contenido del archivo cuando {0} está activado.\",\"Controla si {0} y {1} se detectan automáticamente al abrir un archivo en función del contenido de este.\",\"Quitar el espacio en blanco final autoinsertado.\",\"Manejo especial para archivos grandes para desactivar ciertas funciones de memoria intensiva.\",\"Desactivar sugerencias basadas en Word.\",\"Sugerir palabras solo del documento activo.\",\"Sugerir palabras de todos los documentos abiertos del mismo idioma.\",\"Sugerir palabras de todos los documentos abiertos.\",\"Controla si las finalizaciones se deben calcular en función de las palabras del documento y desde qué documentos se calculan.\",\"El resaltado semántico está habilitado para todos los temas de color.\",\"El resaltado semántico está deshabilitado para todos los temas de color.\",\"El resaltado semántico está configurado con el valor \\\"semanticHighlighting\\\" del tema de color actual.\",\"Controla si se muestra semanticHighlighting para los idiomas que lo admiten.\",\"Mantiene abiertos los editores interactivos, incluso al hacer doble clic en su contenido o presionar \\\"Escape\\\".\",\"Las lineas por encima de esta longitud no se tokenizarán por razones de rendimiento.\",\"Controla si la tokenización debe producirse de forma asincrónica en un rol de trabajo.\",\"Controla si se debe registrar la tokenización asincrónica. Solo para depuración.\",\"Controla si se debe comprobar la tokenización asincrónica con la tokenización en segundo plano heredada. Puede ralentizar la tokenización. Solo para depuración.\",\"Controla si se debe activar el análisis del establecedor de árbol y recopilar telemetría. Tendrá prioridad establecer \\\"editor.experimental.preferTreeSitter\\\" para idiomas específicos.\",\"Define los corchetes que aumentan o reducen la sangría.\",\"Secuencia de cadena o corchete de apertura.\",\"Secuencia de cadena o corchete de cierre.\",\"Define los pares de corchetes coloreados por su nivel de anidamiento si está habilitada la coloración de par de corchetes.\",\"Secuencia de cadena o corchete de apertura.\",\"Secuencia de cadena o corchete de cierre.\",\"Tiempo de espera en milisegundos después del cual se cancela el cálculo de diferencias. Utilice 0 para no usar tiempo de espera.\",\"Tamaño máximo de archivo en MB para el que calcular diferencias. Use 0 para no limitar.\",\"Controla si el editor de diferencias muestra las diferencias en paralelo o alineadas.\",\"Si el ancho del editor de diferencias es menor que este valor, se usa la vista insertada.\",\"Si está habilitada y el ancho del editor es demasiado pequeño, se usa la vista en línea.\",\"Cuando está habilitado, el editor de diferencias muestra flechas en su margen de glifo para revertir los cambios.\",\"Cuando está habilitado, el editor de diferencias muestra un medianil especial para acciones de reversión y fase.\",\"Cuando está habilitado, el editor de diferencias omite los cambios en los espacios en blanco iniciales o finales.\",\"Controla si el editor de diferencias muestra los indicadores +/- para los cambios agregados o quitados.\",\"Controla si el editor muestra CodeLens.\",\"Las líneas no se ajustarán nunca.\",\"Las líneas se ajustarán en el ancho de la ventanilla.\",\"Las líneas se ajustarán en función de la configuración de {0}.\",\"Usa el algoritmo de diferenciación heredado.\",\"Usa el algoritmo de diferenciación avanzada.\",\"Controla si el editor de diferencias muestra las regiones sin cambios.\",\"Controla cuántas líneas se usan para las regiones sin cambios.\",\"Controla cuántas líneas se usan como mínimo para las regiones sin cambios.\",\"Controla cuántas líneas se usan como contexto al comparar regiones sin cambios.\",\"Controlar si el editor de diferencias debe mostrar los movimientos de código detectados.\",\"Controla si el editor de diferencias muestra decoraciones vacías para ver dónde se insertan o eliminan los caracteres.\",\"Si está habilitado y el editor usa la vista insertada, los cambios de palabra se representan en línea.\",\"Usar las API de la plataforma para detectar cuándo se conecta un lector de pantalla.\",\"Optimizar para usar con un lector de pantalla.\",\"Supongamos que no hay un lector de pantalla conectado.\",\"Controla si la interfaz de usuario debe ejecutarse en un modo en el que esté optimizada para lectores de pantalla.\",\"Controla si se inserta un carácter de espacio al comentar.\",\"Controla si las líneas vacías deben ignorarse con la opción de alternar, agregar o quitar acciones para los comentarios de línea.\",\"Controla si al copiar sin selección se copia la línea actual.\",\"Controla si el cursor debe saltar para buscar coincidencias mientras se escribe.\",\"Nunca inicializar la cadena de búsqueda desde la selección del editor.\",\"Siempre inicializar la cadena de búsqueda desde la selección del editor, incluida la palabra en la posición del cursor.\",\"Solo inicializar la cadena de búsqueda desde la selección del editor.\",\"Controla si la cadena de búsqueda del widget de búsqueda se inicializa desde la selección del editor.\",\"No activar nunca Buscar en selección automáticamente (predeterminado).\",\"Activar siempre Buscar en selección automáticamente.\",\"Activar Buscar en la selección automáticamente cuando se seleccionen varias líneas de contenido.\",\"Controla la condición para activar la búsqueda en la selección de forma automática.\",\"Controla si el widget de búsqueda debe leer o modificar el Portapapeles de búsqueda compartido en macOS.\",\"Controla si Encontrar widget debe agregar más líneas en la parte superior del editor. Si es true, puede desplazarse más allá de la primera línea cuando Encontrar widget está visible.\",\"Controla si la búsqueda se reinicia automáticamente desde el principio (o el final) cuando no se encuentran más coincidencias.\",\"Habilita o deshabilita las ligaduras tipográficas (características de fuente \\\"calt\\\" y \\\"liga\\\"). Cámbielo a una cadena para el control específico de la propiedad de CSS \\\"font-feature-settings\\\".\",\"Propiedad de CSS \\\"font-feature-settings\\\" explícita. En su lugar, puede pasarse un valor booleano si solo es necesario activar o desactivar las ligaduras.\",\"Configura las ligaduras tipográficas o las características de fuente. Puede ser un valor booleano para habilitar o deshabilitar las ligaduras o bien una cadena para el valor de la propiedad \\\"font-feature-settings\\\" de CSS.\",\"Habilita o deshabilita la traducción del grosor de font-weight a font-variation-settings. Cambie esto a una cadena para el control específico de la propiedad CSS 'font-variation-settings'.\",\"Propiedad CSS explícita 'font-variation-settings'. En su lugar, se puede pasar un valor booleano si solo es necesario traducir font-weight a font-variation-settings.\",\"Configura variaciones de fuente. Puede ser un booleano para habilitar o deshabilitar la traducción de font-weight a font-variation-settings o una cadena para el valor de la propiedad CSS 'font-variation-settings'.\",\"Controla el tamaño de fuente en píxeles.\",\"Solo se permiten las palabras clave \\\"normal\\\" y \\\"negrita\\\" o los números entre 1 y 1000.\",\"Controla el grosor de la fuente. Acepta las palabras clave \\\"normal\\\" y \\\"negrita\\\" o los números entre 1 y 1000.\",\"Mostrar vista de inspección de los resultados (predeterminado)\",\"Ir al resultado principal y mostrar una vista de inspección\",\"Vaya al resultado principal y habilite la navegación sin peek para otros\",\"Esta configuración está en desuso. Use configuraciones separadas como \\\"editor.editor.gotoLocation.multipleDefinitions\\\" o \\\"editor.editor.gotoLocation.multipleImplementations\\\" en su lugar.\",\"Controla el comportamiento del comando \\\"Ir a definición\\\" cuando existen varias ubicaciones de destino.\",\"Controla el comportamiento del comando \\\"Ir a definición de tipo\\\" cuando existen varias ubicaciones de destino.\",\"Controla el comportamiento del comando \\\"Ir a declaración\\\" cuando existen varias ubicaciones de destino.\",\"Controla el comportamiento del comando \\\"Ir a implementaciones\\\" cuando existen varias ubicaciones de destino.\",\"Controla el comportamiento del comando \\\"Ir a referencias\\\" cuando existen varias ubicaciones de destino.\",\"Identificador de comando alternativo que se ejecuta cuando el resultado de \\\"Ir a definición\\\" es la ubicación actual.\",\"Id. de comando alternativo que se está ejecutando cuando el resultado de \\\"Ir a definición de tipo\\\" es la ubicación actual.\",\"Id. de comando alternativo que se está ejecutando cuando el resultado de \\\"Ir a declaración\\\" es la ubicación actual.\",\"Id. de comando alternativo que se está ejecutando cuando el resultado de \\\"Ir a implementación\\\" es la ubicación actual.\",\"Identificador de comando alternativo que se ejecuta cuando el resultado de \\\"Ir a referencia\\\" es la ubicación actual.\",\"Controla si se muestra la información al mantener el puntero sobre un elemento.\",\"Controla el retardo en milisegundos después del cual se muestra la información al mantener el puntero sobre un elemento.\",\"Controla si la información que aparece al mantener el puntero sobre un elemento permanece visible al mover el mouse sobre este.\",\"Controla el retraso en milisegundos después del cual se oculta el desplazamiento. Requiere que se habilite `editor.hover.sticky`.\",\"Preferir mostrar los desplazamientos por encima de la línea, si hay espacio.\",\"Se supone que todos los caracteres son del mismo ancho. Este es un algoritmo rápido que funciona correctamente para fuentes monoespaciales y ciertos scripts (como caracteres latinos) donde los glifos tienen el mismo ancho.\",\"Delega el cálculo de puntos de ajuste en el explorador. Es un algoritmo lento, que podría causar bloqueos para archivos grandes, pero funciona correctamente en todos los casos.\",\"Controla el algoritmo que calcula los puntos de ajuste. Tenga en cuenta que, en el modo de accesibilidad, se usará el modo avanzado para obtener la mejor experiencia.\",\"Deshabilite el menú de acción de código.\",\"Muestra el menú de acción del código cuando el cursor está en líneas con código.\",\"Muestra el menú de acción de código cuando el cursor está en líneas con código o en líneas vacías.\",\"Habilita la bombilla de acción de código en el editor.\",\"Muestra los ámbitos actuales anidados durante el desplazamiento en la parte superior del editor.\",\"Define el número máximo de líneas rápidas que se mostrarán.\",\"Define el modelo que se va a usar para determinar qué líneas se van a pegar. Si el modelo de esquema no existe, recurrirá al modelo del proveedor de plegado que recurre al modelo de sangría. Este orden se respeta en los tres casos.\",\"Habilite el desplazamiento de desplazamiento rápido con la barra de desplazamiento horizontal del editor.\",\"Habilita las sugerencias de incrustación en el editor.\",\"Las sugerencias de incrustación están habilitadas\",\"Las sugerencias de incrustación se muestran de forma predeterminada y se ocultan cuando se mantiene presionado {0}\",\"Las sugerencias de incrustación están ocultas de forma predeterminada y se muestran al mantener presionado {0}\",\"Las sugerencias de incrustación están deshabilitadas\",\"Controla el tamaño de fuente de las sugerencias de incrustación en el editor. Como valor predeterminado, se usa {0} cuando el valor configurado es menor que {1} o mayor que el tamaño de fuente del editor.\",\"Controla la familia de fuentes de sugerencias de incrustación en el editor. Cuando se establece en vacío, se usa el {0}.\",\"Habilita el relleno alrededor de las sugerencias de incrustación en el editor.\",\"Controla el alto de línea. \\r\\n - Use 0 para calcular automáticamente el alto de línea a partir del tamaño de la fuente.\\r\\n - Los valores entre 0 y 8 se usarán como multiplicador con el tamaño de fuente.\\r\\n - Los valores mayores o igual que 8 se usarán como valores efectivos.\",\"Controla si se muestra el minimapa.\",\"Controla si el minimapa se oculta automáticamente.\",\"El minimapa tiene el mismo tamaño que el contenido del editor (y podría desplazarse).\",\"El minimapa se estirará o reducirá según sea necesario para ocupar la altura del editor (sin desplazamiento).\",\"El minimapa se reducirá según sea necesario para no ser nunca más grande que el editor (sin desplazamiento).\",\"Controla el tamaño del minimapa.\",\"Controla en qué lado se muestra el minimapa.\",\"Controla cuándo se muestra el control deslizante del minimapa.\",\"Escala del contenido dibujado en el minimapa: 1, 2 o 3.\",\"Represente los caracteres reales en una línea, por oposición a los bloques de color.\",\"Limite el ancho del minimapa para representar como mucho un número de columnas determinado.\",\"Controla si las regiones con nombre se muestran como encabezados de sección en el minimapa.\",\"Controla si los comentarios MARK: se muestran como encabezados de sección en el minimapa.\",\"Controla el tamaño de fuente de los encabezados de sección en el minimapa.\",\"Controla la cantidad de espacio (en píxeles) entre los caracteres del encabezado de sección. Esto aumenta la legibilidad del encabezado en tamaños de fuente pequeños.\",\"Controla la cantidad de espacio entre el borde superior del editor y la primera línea.\",\"Controla el espacio entre el borde inferior del editor y la última línea.\",\"Habilita un elemento emergente que muestra documentación de los parámetros e información de los tipos mientras escribe.\",\"Controla si el menú de sugerencias de parámetros se cicla o se cierra al llegar al final de la lista.\",\"Las sugerencias rápidas se muestran dentro del widget de sugerencias\",\"Las sugerencias rápidas se muestran como texto fantasma\",\"Las sugerencias rápidas están deshabilitadas\",\"Habilita sugerencias rápidas en las cadenas.\",\"Habilita sugerencias rápidas en los comentarios.\",\"Habilita sugerencias rápidas fuera de las cadenas y los comentarios.\",\"Controla si las sugerencias deben mostrarse automáticamente al escribir. Puede controlarse para la escritura en comentarios, cadenas y otro código. Las sugerencias rápidas pueden configurarse para mostrarse como texto fantasma o con el widget de sugerencias. Tenga en cuenta también la configuración de {0} que controla si los caracteres especiales desencadenan las sugerencias.\",\"Los números de línea no se muestran.\",\"Los números de línea se muestran como un número absoluto.\",\"Los números de línea se muestran como distancia en líneas a la posición del cursor.\",\"Los números de línea se muestran cada 10 líneas.\",\"Controla la visualización de los números de línea.\",\"Número de caracteres monoespaciales en los que se representará esta regla del editor.\",\"Color de esta regla del editor.\",\"Muestra reglas verticales después de un cierto número de caracteres monoespaciados. Usa múltiples valores para mostrar múltiples reglas. Si la matriz está vacía, no se muestran reglas.\",\"La barra de desplazamiento vertical estará visible solo cuando sea necesario.\",\"La barra de desplazamiento vertical estará siempre visible.\",\"La barra de desplazamiento vertical estará siempre oculta.\",\"Controla la visibilidad de la barra de desplazamiento vertical.\",\"La barra de desplazamiento horizontal estará visible solo cuando sea necesario.\",\"La barra de desplazamiento horizontal estará siempre visible.\",\"La barra de desplazamiento horizontal estará siempre oculta.\",\"Controla la visibilidad de la barra de desplazamiento horizontal.\",\"Ancho de la barra de desplazamiento vertical.\",\"Altura de la barra de desplazamiento horizontal.\",\"Controla si al hacer clic se desplaza por página o salta a la posición donde se hace clic.\",\"Cuando se establece, la barra de desplazamiento horizontal no aumentará el tamaño del contenido del editor.\",\"Controla si se resaltan todos los caracteres ASCII no básicos. Solo los caracteres entre U+0020 y U+007E, tabulación, avance de línea y retorno de carro se consideran ASCII básicos.\",\"Controla si se resaltan los caracteres que solo reservan espacio o que no tienen ancho.\",\"Controla si se resaltan caracteres que se pueden confundir con caracteres ASCII básicos, excepto los que son comunes en la configuración regional del usuario actual.\",\"Controla si los caracteres de los comentarios también deben estar sujetos al resaltado Unicode.\",\"Controla si los caracteres de las cadenas también deben estar sujetos al resaltado Unicode.\",\"Define los caracteres permitidos que no se resaltan.\",\"Los caracteres Unicode que son comunes en las configuraciones regionales permitidas no se resaltan.\",\"Controla si se deben mostrar automáticamente las sugerencias alineadas en el editor.\",\"Muestra la barra de herramientas de sugerencias insertadas cada vez que se muestra una sugerencia insertada.\",\"Muestra la barra de herramientas de sugerencias insertadas al mantener el puntero sobre una sugerencia insertada.\",\"No mostrar nunca la barra de herramientas de sugerencias insertadas.\",\"Controla cuándo mostrar la barra de herramientas de sugerencias insertadas.\",\"Controla cómo interactúan las sugerencias insertadas con el widget de sugerencias. Si se habilita, el widget de sugerencias no se muestra automáticamente cuando hay sugerencias insertadas disponibles.\",\"Controla la familia de fuentes de las sugerencias insertadas.\",null,null,null,null,null,null,\"Controla si está habilitada o no la coloración de pares de corchetes. Use {0} para invalidar los colores de resaltado de corchete.\",\"Controla si cada tipo de corchete tiene su propio grupo de colores independiente.\",\"Habilita guías de par de corchetes.\",\"Habilita guías de par de corchetes solo para el par de corchetes activo.\",\"Deshabilita las guías de par de corchetes.\",\"Controla si están habilitadas las guías de pares de corchetes.\",\"Habilita guías horizontales como adición a guías de par de corchetes verticales.\",\"Habilita guías horizontales solo para el par de corchetes activo.\",\"Deshabilita las guías de par de corchetes horizontales.\",\"Controla si están habilitadas las guías de pares de corchetes horizontales.\",\"Controla si el editor debe resaltar el par de corchetes activo.\",\"Controla si el editor debe representar guías de sangría.\",\"Resalta la guía de sangría activa.\",\"Resalta la guía de sangría activa incluso si se resaltan las guías de corchetes.\",\"No resalta la guía de sangría activa.\",\"Controla si el editor debe resaltar la guía de sangría activa.\",\"Inserte la sugerencia sin sobrescribir el texto a la derecha del cursor.\",\"Inserte la sugerencia y sobrescriba el texto a la derecha del cursor.\",\"Controla si las palabras se sobrescriben al aceptar la finalización. Tenga en cuenta que esto depende de las extensiones que participan en esta característica.\",\"Controla si el filtrado y la ordenación de sugerencias se tienen en cuenta para los errores ortográficos pequeños.\",\"Controla si la ordenación mejora las palabras que aparecen cerca del cursor.\",\"Controla si las selecciones de sugerencias recordadas se comparten entre múltiples áreas de trabajo y ventanas (necesita \\\"#editor.suggestSelection#\\\").\",\"Seleccione siempre una sugerencia cuando se desencadene IntelliSense automáticamente.\",\"Nunca seleccione una sugerencia cuando desencadene IntelliSense automáticamente.\",\"Seleccione una sugerencia solo cuando desencadene IntelliSense desde un carácter de desencadenador.\",\"Seleccione una sugerencia solo cuando desencadene IntelliSense mientras escribe.\",\"Controla si se selecciona una sugerencia cuando se muestra el widget. Tenga en cuenta que esto solo se aplica a las sugerencias desencadenadas automáticamente ({0} y {1}) y que siempre se selecciona una sugerencia cuando se invoca explícitamente, por ejemplo, a través de 'Ctrl+Espacio'.\",\"Controla si un fragmento de código activo impide sugerencias rápidas.\",\"Controla si mostrar u ocultar iconos en sugerencias.\",\"Controla la visibilidad de la barra de estado en la parte inferior del widget de sugerencias.\",\"Controla si se puede obtener una vista previa del resultado de la sugerencia en el editor.\",\"Controla si los detalles de sugerencia se muestran incorporados con la etiqueta o solo en el widget de detalles.\",\"La configuración está en desuso. Ahora puede cambiarse el tamaño del widget de sugerencias.\",\"Esta configuración está en desuso. Use configuraciones separadas como \\\"editor.suggest.showKeyword\\\" o \\\"editor.suggest.showSnippets\\\" en su lugar.\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"method\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de \\\"función\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"constructor\\\".\",\"Cuando se activa IntelliSense muestra sugerencias \\\"obsoletas\\\".\",\"Cuando se activa el filtro IntelliSense se requiere que el primer carácter coincida con el inicio de una palabra. Por ejemplo, \\\"c\\\" en \\\"Consola\\\" o \\\"WebContext\\\" but _not_ on \\\"descripción\\\". Si se desactiva, IntelliSense mostrará más resultados, pero los ordenará según la calidad de la coincidencia.\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"field\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"variable\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"class\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"struct\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"interface\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"module\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"property\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"event\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"operator\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"unit\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de \\\"value\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"constant\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"enum\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"enumMember\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"keyword\\\".\",\"Si está habilitado, IntelliSense muestra sugerencias de tipo \\\"text\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de \\\"color\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"file\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"reference\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"customcolor\\\".\",\"Si está habilitado, IntelliSense muestra sugerencias de tipo \\\"folder\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"typeParameter\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias de tipo \\\"snippet\\\".\",\"Cuando está habilitado, IntelliSense muestra sugerencias del usuario.\",\"Cuando está habilitado IntelliSense muestra sugerencias para problemas.\",\"Indica si los espacios en blanco iniciales y finales deben seleccionarse siempre.\",\"Indica si se deben seleccionar las subpalabras (como \\\"foo\\\" en \\\"fooBar\\\" o \\\"foo_bar\\\").\",\"Configuraciones regionales que se usarán para la segmentación de palabras al realizar operaciones o navegaciones relacionadas con palabras. Especifique la etiqueta de idioma BCP 47 de la palabra que desea reconocer (por ejemplo, ja, zh-CN, zh-Hant-TW, etc.).\",\"Configuraciones regionales que se usarán para la segmentación de palabras al realizar operaciones o navegaciones relacionadas con palabras. Especifique la etiqueta de idioma BCP 47 de la palabra que desea reconocer (por ejemplo, ja, zh-CN, zh-Hant-TW, etc.).\",\"No hay sangría. Las líneas ajustadas comienzan en la columna 1.\",\"A las líneas ajustadas se les aplica la misma sangría que al elemento primario.\",\"A las líneas ajustadas se les aplica una sangría de +1 respecto al elemento primario.\",\"A las líneas ajustadas se les aplica una sangría de +2 respecto al elemento primario.\",\"Controla la sangría de las líneas ajustadas.\",\"Controla si puede arrastrar y colocar un archivo en un editor de texto manteniendo presionada la tecla \\\"Mayús\\\" (en lugar de abrir el archivo en un editor).\",\"Controla si se muestra un widget al colocar archivos en el editor. Este widget le permite controlar cómo se coloca el archivo.\",\"Muestra el widget del selector de colocación después de colocar un archivo en el editor.\",\"No mostrar nunca el widget del selector de colocación. En su lugar, siempre se usa el proveedor de colocación predeterminado.\",\"Controla si se puede pegar contenido de distintas formas.\",\"Controla si se muestra un widget al pegar contenido en el editor. Este widget le permite controlar cómo se pega el archivo.\",\"Muestra el widget del selector de pegado después de pegar contenido en el editor.\",\"No mostrar nunca el widget del selector de pegado. En su lugar, siempre se usa el comportamiento de pegado predeterminado.\",\"Controla si se deben aceptar sugerencias en los caracteres de confirmación. Por ejemplo, en Javascript, el punto y coma (\\\";\\\") puede ser un carácter de confirmación que acepta una sugerencia y escribe ese carácter.\",\"Aceptar solo una sugerencia con \\\"Entrar\\\" cuando realiza un cambio textual.\",\"Controla si las sugerencias deben aceptarse con \\\"Entrar\\\", además de \\\"TAB\\\". Ayuda a evitar la ambigüedad entre insertar nuevas líneas o aceptar sugerencias.\",\"Controla el número de líneas del editor que pueden ser leídas por un lector de pantalla a la vez. Cuando detectamos un lector de pantalla, fijamos automáticamente el valor por defecto en 500. Advertencia: esto tiene una implicación de rendimiento para números mayores que el predeterminado.\",\"Contenido del editor\",\"Controlar si un lector de pantalla anuncia sugerencias insertadas.\",\"Utilizar las configuraciones del lenguaje para determinar cuándo cerrar los corchetes automáticamente.\",\"Cerrar automáticamente los corchetes cuando el cursor esté a la izquierda de un espacio en blanco.\",\"Controla si el editor debe cerrar automáticamente los corchetes después de que el usuario agregue un corchete de apertura.\",\"Utilice las configuraciones de idioma para determinar cuándo cerrar los comentarios automáticamente.\",\"Cerrar automáticamente los comentarios solo cuando el cursor esté a la izquierda de un espacio en blanco.\",\"Controla si el editor debe cerrar automáticamente los comentarios después de que el usuario agregue un comentario de apertura.\",\"Quite los corchetes o las comillas de cierre adyacentes solo si se insertaron automáticamente.\",\"Controla si el editor debe quitar los corchetes o las comillas de cierre adyacentes al eliminar.\",\"Escriba en las comillas o los corchetes solo si se insertaron automáticamente.\",\"Controla si el editor debe escribir entre comillas o corchetes.\",\"Utilizar las configuraciones del lenguaje para determinar cuándo cerrar las comillas automáticamente. \",\"Cerrar automáticamente las comillas cuando el cursor esté a la izquierda de un espacio en blanco. \",\"Controla si el editor debe cerrar automáticamente las comillas después de que el usuario agrega uma comilla de apertura.\",\"El editor no insertará la sangría automáticamente.\",\"El editor mantendrá la sangría de la línea actual.\",\"El editor respetará la sangría de la línea actual y los corchetes definidos por el idioma.\",\"El editor mantendrá la sangría de la línea actual, respetará los corchetes definidos por el idioma e invocará onEnterRules especiales definidos por idiomas.\",\"El editor respetará la sangría de la línea actual, los corchetes definidos por idiomas y las reglas indentationRules definidas por idiomas, además de invocar reglas onEnterRules especiales.\",\"Controla si el editor debe ajustar automáticamente la sangría mientras los usuarios escriben, pegan, mueven o sangran líneas.\",\"Use las configuraciones de idioma para determinar cuándo delimitar las selecciones automáticamente.\",\"Envolver con comillas, pero no con corchetes.\",\"Envolver con corchetes, pero no con comillas.\",\"Controla si el editor debe rodear automáticamente las selecciones al escribir comillas o corchetes.\",\"Emula el comportamiento de selección de los caracteres de tabulación al usar espacios para la sangría. La selección se aplicará a las tabulaciones.\",\"Controla si el editor muestra CodeLens.\",\"Controla la familia de fuentes para CodeLens.\",\"Controla el tamaño de fuente de CodeLens en píxeles. Cuando se establece en 0, se usa el 90 % de \\\"#editor.fontSize#\\\".\",\"Controla si el editor debe representar el Selector de colores y los elementos Decorator de color en línea.\",\"Hacer que el selector de colores aparezca tanto al hacer clic como al mantener el puntero sobre el decorador de color\",\"Hacer que el selector de colores aparezca al pasar el puntero sobre el decorador de color\",\"Hacer que el selector de colores aparezca al hacer clic en el decorador de color\",\"Controla la condición para que un selector de colores aparezca de un decorador de color\",\"Controla el número máximo de decoradores de color que se pueden representar en un editor a la vez.\",\"Habilite que la selección con el mouse y las teclas esté realizando la selección de columnas.\",\"Controla si el resaltado de sintaxis debe ser copiado al portapapeles.\",\"Controla el estilo de animación del cursor.\",\"La animación del símbolo de intercalación suave está deshabilitada.\",\"La animación de símbolo de intercalación suave solo se habilita cuando el usuario mueve el cursor con un gesto explícito.\",\"La animación de símbolo de intercalación suave siempre está habilitada.\",\"Controla si la animación suave del cursor debe estar habilitada.\",\"Controla el estilo del cursor en el modo de entrada de inserción.\",\"Controla el número mínimo de líneas iniciales visibles (mínimo 0) y líneas finales (mínimo 1) que rodean el cursor. Se conoce como \\\"scrollOff\\\" o \\\"scrollOffset\\\" en otros editores.\",\"Solo se aplica \\\"cursorSurroundingLines\\\" cuando se desencadena mediante el teclado o la API.\",\"\\\"cursorSurroundingLines\\\" se aplica siempre.\",\"Controla cuándo se debe aplicar '#editor.cursorSurroundingLines#'.\",\"Controla el ancho del cursor cuando \\\"#editor.cursorStyle#\\\" se establece en \\\"line\\\".\",\"Controla si el editor debe permitir mover las selecciones mediante arrastrar y colocar.\",\"Use un nuevo método de representación con svgs.\",\"Use un nuevo método de representación con caracteres de fuente.\",\"Use el método de representación estable.\",\"Controla si los espacios en blanco se representan con un nuevo método experimental.\",\"Multiplicador de la velocidad de desplazamiento al presionar \\\"Alt\\\".\",\"Controla si el editor tiene el plegado de código habilitado.\",\"Utilice una estrategia de plegado específica del idioma, si está disponible, de lo contrario la basada en sangría.\",\"Utilice la estrategia de plegado basada en sangría.\",\"Controla la estrategia para calcular rangos de plegado.\",\"Controla si el editor debe destacar los rangos plegados.\",\"Permite controlar si el editor contrae automáticamente los rangos de importación.\",\"Número máximo de regiones plegables. Si aumenta este valor, es posible que el editor tenga menos capacidad de respuesta cuando el origen actual tiene un gran número de regiones plegables.\",\"Controla si al hacer clic en el contenido vacío después de una línea plegada se desplegará la línea.\",\"Controla la familia de fuentes.\",\"Controla si el editor debe dar formato automáticamente al contenido pegado. Debe haber disponible un formateador capaz de aplicar formato a un rango dentro de un documento. \",\"Controla si el editor debe dar formato a la línea automáticamente después de escribirla.\",\"Controla si el editor debe representar el margen de glifo vertical. El margen de glifo se usa, principalmente, para depuración.\",\"Controla si el cursor debe ocultarse en la regla de información general.\",\"Controla el espacio entre letras en píxeles.\",\"Controla si el editor tiene habilitada la edición vinculada. Dependiendo del lenguaje, los símbolos relacionados (por ejemplo, las etiquetas HTML) se actualizan durante la edición.\",\"Controla si el editor debe detectar vínculos y hacerlos interactivos.\",\"Resaltar paréntesis coincidentes.\",\"Se usará un multiplicador en los eventos de desplazamiento de la rueda del mouse \\\"deltaX\\\" y \\\"deltaY\\\". \",\"Acercar la fuente del editor al usar la rueda del mouse y mantener pulsado 'Cmd'.\",\"Ampliar la fuente del editor cuando se use la rueda del mouse mientras se presiona \\\"Ctrl\\\".\",\"Combinar varios cursores cuando se solapan.\",\"Se asigna a \\\"Control\\\" en Windows y Linux y a \\\"Comando\\\" en macOS.\",\"Se asigna a \\\"Alt\\\" en Windows y Linux y a \\\"Opción\\\" en macOS.\",\"El modificador que se usará para agregar varios cursores con el mouse. Los gestos del mouse Ir a definición y Abrir vínculo se adaptarán de modo que no entren en conflicto con el [modificador multicursor](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).\",\"Cada cursor pega una única línea del texto.\",\"Cada cursor pega el texto completo.\",\"Controla el pegado cuando el recuento de líneas del texto pegado coincide con el recuento de cursores.\",\"Controla el número máximo de cursores que puede haber en un editor activo a la vez.\",\"No resalta las repeticiones.\",\"Resalta las repeticiones solo en el archivo actual.\",\"Experimental: Resalta las repeticiones en todos los archivos abiertos válidos.\",\"Controla si las repeticiones deben resaltarse en los archivos abiertos.\",\"Controla si debe dibujarse un borde alrededor de la regla de información general.\",\"Enfocar el árbol al abrir la inspección\",\"Enfocar el editor al abrir la inspección\",\"Controla si se debe enfocar el editor en línea o el árbol en el widget de vista.\",\"Controla si el gesto del mouse Ir a definición siempre abre el widget interactivo.\",\"Controla el retraso, en milisegundos, tras el cual aparecerán sugerencias rápidas.\",\"Controla si el editor cambia el nombre automáticamente en el tipo.\",\"En desuso. Utilice \\\"editor.linkedEditing\\\" en su lugar.\",\"Controla si el editor debe representar caracteres de control.\",\"Representar el número de la última línea cuando el archivo termina con un salto de línea.\",\"Resalta el medianil y la línea actual.\",\"Controla cómo debe representar el editor el resaltado de línea actual.\",\"Controla si el editor debe representar el resaltado de la línea actual solo cuando el editor está enfocado.\",\"Representa caracteres de espacio en blanco, excepto los espacios individuales entre palabras.\",\"Represente los caracteres de espacio en blanco solo en el texto seleccionado.\",\"Representa solo los caracteres de espacio en blanco al final.\",\"Controla la forma en que el editor debe representar los caracteres de espacio en blanco.\",\"Controla si las selecciones deberían tener las esquinas redondeadas.\",\"Controla el número de caracteres adicionales a partir del cual el editor se desplazará horizontalmente.\",\"Controla si el editor seguirá haciendo scroll después de la última línea.\",\"Desplácese solo a lo largo del eje predominante cuando se desplace vertical y horizontalmente al mismo tiempo. Evita la deriva horizontal cuando se desplaza verticalmente en un trackpad.\",\"Controla si el portapapeles principal de Linux debe admitirse.\",\"Controla si el editor debe destacar las coincidencias similares a la selección.\",\"Mostrar siempre los controles de plegado.\",\"No mostrar nunca los controles de plegado y reducir el tamaño del medianil.\",\"Mostrar solo los controles de plegado cuando el mouse está sobre el medianil.\",\"Controla cuándo se muestran los controles de plegado en el medianil.\",\"Controla el fundido de salida del código no usado.\",\"Controla las variables en desuso tachadas.\",\"Mostrar sugerencias de fragmentos de código por encima de otras sugerencias.\",\"Mostrar sugerencias de fragmentos de código por debajo de otras sugerencias.\",\"Mostrar sugerencias de fragmentos de código con otras sugerencias.\",\"No mostrar sugerencias de fragmentos de código.\",\"Controla si se muestran los fragmentos de código con otras sugerencias y cómo se ordenan.\",\"Controla si el editor se desplazará con una animación.\",\"Controla si se debe proporcionar la sugerencia de accesibilidad a los usuarios del lector de pantalla cuando se muestra una finalización insertada.\",\"Tamaño de fuente del widget de sugerencias. Cuando se establece en {0}, se usa el valor de {1}.\",\"Alto de línea para el widget de sugerencias. Cuando se establece en {0}, se usa el valor de {1}. El valor mínimo es 8.\",\"Controla si deben aparecer sugerencias de forma automática al escribir caracteres desencadenadores.\",\"Seleccionar siempre la primera sugerencia.\",\"Seleccione sugerencias recientes a menos que al escribir más se seleccione una, por ejemplo, \\\"console.| -> console.log\\\" porque \\\"log\\\" se ha completado recientemente.\",\"Seleccione sugerencias basadas en prefijos anteriores que han completado esas sugerencias, por ejemplo, \\\"co -> console\\\" y \\\"con -> const\\\".\",\"Controla cómo se preseleccionan las sugerencias cuando se muestra la lista,\",\"La pestaña se completará insertando la mejor sugerencia de coincidencia encontrada al presionar la pestaña\",\"Deshabilitar los complementos para pestañas.\",\"La pestaña se completa con fragmentos de código cuando su prefijo coincide. Funciona mejor cuando las 'quickSuggestions' no están habilitadas.\",\"Habilita completar pestañas.\",\"Los terminadores de línea no habituales se quitan automáticamente.\",\"Los terminadores de línea no habituales se omiten.\",\"Advertencia de terminadores de línea inusuales que se quitarán.\",\"Quite los terminadores de línea inusuales que podrían provocar problemas.\",\"Los espacios y tabulaciones se insertan y eliminan en alineación con tabulaciones.\",\"Use la regla de salto de línea predeterminada.\",\"Los saltos de palabra no deben usarse para texto chino, japonés o coreano (CJK). El comportamiento del texto distinto a CJK es el mismo que el normal.\",\"Controla las reglas de salto de palabra usadas para texto chino, japonés o coreano (CJK).\",\"Caracteres que se usarán como separadores de palabras al realizar operaciones o navegaciones relacionadas con palabras.\",\"Las líneas no se ajustarán nunca.\",\"Las líneas se ajustarán en el ancho de la ventanilla.\",\"Las líneas se ajustarán al valor de \\\"#editor.wordWrapColumn#\\\". \",\"Las líneas se ajustarán al valor que sea inferior: el tamaño de la ventanilla o el valor de \\\"#editor.wordWrapColumn#\\\".\",\"Controla cómo deben ajustarse las líneas.\",\"Controla la columna de ajuste del editor cuando \\\"#editor.wordWrap#\\\" es \\\"wordWrapColumn\\\" o \\\"bounded\\\".\",\"Controla si las decoraciones de color en línea deben mostrarse con el proveedor de colores del documento predeterminado.\",\"Controla si el editor recibe las pestañas o las aplaza al área de trabajo para la navegación.\",\"Color de fondo para la línea resaltada en la posición del cursor.\",\"Color de fondo del borde alrededor de la línea en la posición del cursor.\",\"Color de fondo de rangos resaltados, como en abrir rápido y encontrar características. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Color de fondo del borde alrededor de los intervalos resaltados.\",\"Color de fondo del símbolo destacado, como Ir a definición o Ir al siguiente/anterior símbolo. El color no debe ser opaco para no ocultar la decoración subyacente.\",\"Color de fondo del borde alrededor de los símbolos resaltados.\",\"Color del cursor del editor.\",\"Color de fondo del cursor de edición. Permite personalizar el color del caracter solapado por el bloque del cursor.\",\"Color de los cursores del editor principal cuando hay varios cursores presentes.\",\"Color de fondo de los cursores del editor principal cuando hay varios cursores presentes. Permite personalizar el color del carácter solapado por el bloque del cursor.\",\"Color de los cursores del editor secundario cuando hay varios cursores presentes.\",\"Color de fondo de los cursores del editor secundario cuando hay varios cursores presentes. Permite personalizar el color del carácter solapado por el bloque del cursor.\",\"Color de los caracteres de espacio en blanco del editor.\",\"Color de números de línea del editor.\",\"Color de las guías de sangría del editor.\",\"\\\"editorIndentGuide.background\\\" está en desuso. Use \\\"editorIndentGuide.background1\\\" en su lugar.\",\"Color de las guías de sangría activas del editor.\",\"\\\"editorIndentGuide.activeBackground\\\" está en desuso. Use \\\"editorIndentGuide.activeBackground1\\\" en su lugar.\",\"Color de las guías de sangría del editor (1).\",\"Color de las guías de sangría del editor (2).\",\"Color de las guías de sangría del editor (3).\",\"Color de las guías de sangría del editor (4).\",\"Color de las guías de sangría del editor (5).\",\"Color de las guías de sangría del editor (6).\",\"Color de las guías de sangría del editor activo (1).\",\"Color de las guías de sangría del editor activo (2).\",\"Color de las guías de sangría del editor activo (3).\",\"Color de las guías de sangría del editor activo (4).\",\"Color de las guías de sangría del editor activo (5).\",\"Color de las guías de sangría del editor activo (6).\",\"Color del número de línea activa en el editor\",\"ID es obsoleto. Usar en lugar 'editorLineNumber.activeForeground'. \",\"Color del número de línea activa en el editor\",\"Color de la línea final del editor cuando editor.renderFinalNewline se establece en atenuado.\",\"Color de las reglas del editor\",\"Color principal de lentes de código en el editor\",\"Color de fondo tras corchetes coincidentes\",\"Color de bloques con corchetes coincidentes\",\"Color del borde de la regla de visión general.\",\"Color de fondo de la regla de información general del editor.\",\"Color de fondo del margen del editor. Este espacio contiene los márgenes de glifos y los números de línea.\",\"Color del borde de código fuente innecesario (sin usar) en el editor.\",\"Opacidad de código fuente innecesario (sin usar) en el editor. Por ejemplo, \\\"#000000c0\\\" representará el código con un 75 % de opacidad. Para temas de alto contraste, utilice el color del tema 'editorUnnecessaryCode.border' para resaltar el código innecesario en vez de atenuarlo.\",\"Color del borde del texto fantasma en el editor.\",\"Color de primer plano del texto fantasma en el editor.\",\"Color de fondo del texto fantasma en el editor.\",\"Color de marcador de regla general para los destacados de rango. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de marcador de regla de información general para errores. \",\"Color de marcador de regla de información general para advertencias.\",\"Color de marcador de regla de información general para mensajes informativos. \",\"Color de primer plano de los corchetes (1). Requiere que se habilite la coloración del par de corchetes.\",\"Color de primer plano de los corchetes (2). Requiere que se habilite la coloración del par de corchetes.\",\"Color de primer plano de los corchetes (3). Requiere que se habilite la coloración del par de corchetes.\",\"Color de primer plano de los corchetes (4). Requiere que se habilite la coloración del par de corchetes.\",\"Color de primer plano de los corchetes (5). Requiere que se habilite la coloración del par de corchetes.\",\"Color de primer plano de los corchetes (6). Requiere que se habilite la coloración del par de corchetes.\",\"Color de primer plano de corchetes inesperados.\",\"Color de fondo de las guías de par de corchetes inactivos (1). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes inactivos (2). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes inactivos (3). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes inactivos (4). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes inactivos (5). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes inactivos (6). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de pares de corchetes activos (1). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes activos (2). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de pares de corchetes activos (3). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes activos (4). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes activos (5). Requiere habilitar guías de par de corchetes.\",\"Color de fondo de las guías de par de corchetes activos (6). Requiere habilitar guías de par de corchetes.\",\"Color de borde usado para resaltar caracteres Unicode.\",\"Color de borde usado para resaltar caracteres unicode.\",\"Si el texto del editor tiene el foco (el cursor parpadea)\",\"Si el editor o un widget del editor tiene el foco (por ejemplo, el foco está en el widget de búsqueda)\",\"Si un editor o una entrada de texto enriquecido tienen el foco (el cursor parpadea)\",\"Si el editor es de solo lectura\",\"Si el contexto es un editor de diferencias\",\"Si el contexto es un editor de diferencias incrustado\",null,\"Si todos los archivos del editor de diferencias múltiples están contraídos\",\"Si el editor de diferencias tiene cambios\",\"Indica si se selecciona un bloque de código movido para la comparación\",\"Si el visor de diferencias accesible está visible\",\"Indica si se alcanza el punto de interrupción insertado en paralelo del editor de diferencias\",\"Si el modo insertado está activo\",\"Indica si la modificación se puede escribir en el editor de diferencias\",\"Indica si la modificación se puede escribir en el editor de diferencias\",\"URI del documento original\",\"URI del documento modificado\",\"Si \\\"editor.columnSelection\\\" se ha habilitado\",\"Si el editor tiene texto seleccionado\",\"Si el editor tiene varias selecciones\",\"Si \\\"Tabulación\\\" moverá el foco fuera del editor\",\"Si el mantenimiento del puntero del editor es visible\",\"Si se centra el desplazamiento del editor\",\"Si el desplazamiento permanente está centrado\",\"Si el desplazamiento permanente está visible\",\"Si el selector de colores independiente está visible\",\"Si el selector de colores independiente está centrado\",\"Si el editor forma parte de otro más grande (por ejemplo, blocs de notas)\",\"Identificador de idioma del editor\",\"Si el editor tiene un proveedor de elementos de finalización\",\"Si el editor tiene un proveedor de acciones de código\",\"Si el editor tiene un proveedor de CodeLens\",\"Si el editor tiene un proveedor de definiciones\",\"Si el editor tiene un proveedor de declaraciones\",\"Si el editor tiene un proveedor de implementación\",\"Si el editor tiene un proveedor de definiciones de tipo\",\"Si el editor tiene un proveedor de contenido con mantenimiento del puntero\",\"Si el editor tiene un proveedor de resaltado de documentos\",\"Si el editor tiene un proveedor de símbolos de documentos\",\"Si el editor tiene un proveedor de referencia\",\"Si el editor tiene un proveedor de cambio de nombre\",\"Si el editor tiene un proveedor de ayuda de signatura\",\"Si el editor tiene un proveedor de sugerencias insertadas\",\"Si el editor tiene un proveedor de formatos de documento\",\"Si el editor tiene un proveedor de formatos de selección de documentos\",\"Si el editor tiene varios proveedores de formatos del documento\",\"Si el editor tiene varios proveedores de formato de la selección de documentos\",\"matriz\",\"booleano\",\"clase\",\"constante\",\"constructor\",\"enumeración\",\"miembro de la enumeración\",\"evento\",\"campo\",\"archivo\",\"función\",\"interfaz\",\"clave\",\"método\",\"módulo\",\"espacio de nombres\",\"NULL\",\"número\",\"objeto\",\"operador\",\"paquete\",\"propiedad\",\"cadena\",\"estructura\",\"parámetro de tipo\",\"variable\",\"{0} ({1})\",\"Texto sin formato\",\"Escribiendo\",\"Desarrollador: inspeccionar tokens\",\"Vaya a Línea/Columna...\",\"Mostrar todos los proveedores de acceso rápido\",\"Paleta de comandos\",\"Mostrar y ejecutar comandos\",\"Ir a símbolo...\",\"Ir a símbolo por categoría...\",\"Contenido del editor\",\"Alternar tema de contraste alto\",\"{0} ediciones realizadas en {1} archivos\",\"Mostrar más ({0})\",\"{0} caracteres\",\"Delimitador de la selección\",\"Delimitador establecido en {0}:{1}\",\"Establecer el delimitador de la selección\",\"Ir al delimitador de la selección\",\"Seleccionar desde el delimitador hasta el cursor\",\"Cancelar el delimitador de la selección\",\"Resumen color de marcador de regla para corchetes.\",\"Ir al corchete\",\"Seleccionar para corchete\",\"Quitar corchetes\",\"Ir al &&corchete\",\"Se selecciona el texto que está dentro, incluyendo los corchetes o las llaves\",\"Mover el texto seleccionado a la izquierda\",\"Mover el texto seleccionado a la derecha\",\"Transponer letras\",\"Cor&&tar\",\"Cortar\",\"Cortar\",\"Cortar\",\"&&Copiar\",\"Copiar\",\"Copiar\",\"Copiar\",\"&&Pegar\",\"Pegar\",\"Pegar\",\"Pegar\",\"Copiar con resaltado de sintaxis\",\"Copiar como\",\"Copiar como\",\"Compartir\",\"Compartir\",\"Se ha producido un error desconocido al aplicar la acción de código\",\"Tipo de la acción de código que se va a ejecutar.\",\"Controla cuándo se aplican las acciones devueltas.\",\"Aplicar siempre la primera acción de código devuelto.\",\"Aplicar la primera acción de código devuelta si solo hay una.\",\"No aplique las acciones de código devuelto.\",\"Controla si solo se deben devolver las acciones de código preferidas.\",\"Corrección Rápida\",\"No hay acciones de código disponibles\",\"No hay acciones de código preferidas para \\\"{0}\\\" disponibles\",\"No hay ninguna acción de código para \\\"{0}\\\" disponible.\",\"No hay acciones de código preferidas disponibles\",\"No hay acciones de código disponibles\",\"Refactorizar...\",\"No hay refactorizaciones preferidas de \\\"{0}\\\" disponibles\",\"No hay refactorizaciones de \\\"{0}\\\" disponibles\",\"No hay ninguna refactorización favorita disponible.\",\"No hay refactorizaciones disponibles\",\"Acción de código fuente...\",\"No hay acciones de origen preferidas para \\\"{0}\\\" disponibles\",\"No hay ninguna acción de código fuente para \\\"{0}\\\" disponible.\",\"No hay ninguna acción de código fuente favorita disponible.\",\"No hay acciones de origen disponibles\",\"Organizar Importaciones\",\"No hay acciones de importación disponibles\",\"Corregir todo\",\"No está disponible la acción de corregir todo\",\"Corregir automáticamente...\",\"No hay autocorrecciones disponibles\",\"Activar/desactivar la visualización de los encabezados de los grupos en el menú de Acción de código.\",\"Habilita o deshabilita la visualización de la corrección rápida más cercana dentro de una línea cuando no está actualmente en un diagnóstico.\",\"Habilitar el desencadenamiento {0} cuando {1} se establece en {2}. Las acciones de código deben establecerse en {3} para que se desencadenen los cambios de ventana y de foco.\",\"Contexto: {0} en la línea {1} y columna {2}.\",\"Ocultar deshabilitado\",\"Mostrar elementos deshabilitados\",\"Más Acciones...\",\"Corrección rápida\",\"Extraer\",\"Insertado\",\"Reescribir\",\"Mover\",\"Delimitar con\",\"Acción de origen\",\"Icono que genera el menú de acciones de código desde el medianil cuando no hay espacio en el editor.\",\"Icono que genera el menú de acciones de código desde el medianil cuando no hay espacio en el editor y hay disponible una corrección rápida.\",\"Icono que genera el menú de acciones de código desde el medianil cuando no hay espacio en el editor y hay disponible una corrección de IA.\",\"Icono que genera el menú de acciones de código desde el medianil cuando no hay espacio en el editor y hay disponible una corrección de IA y una corrección rápida.\",\"Icono que genera el menú de acciones de código desde el medianil cuando no hay espacio en el editor y hay disponible una corrección de IA y una corrección rápida.\",\"Ejecutar: {0}\",\"Mostrar acciones de código. Corrección rápida preferida disponible ({0})\",\"Mostrar acciones de código ({0})\",\"Mostrar acciones de código\",\"Mostrar comandos de lente de código para la línea actual\",\"Seleccionar un comando\",null,null,null,null,null,null,null,null,null,\"Alternar comentario de línea\",\"&&Alternar comentario de línea\",\"Agregar comentario de línea\",\"Quitar comentario de línea\",\"Alternar comentario de bloque\",\"Alternar &&bloque de comentario\",\"Minimapa\",\"Representar caracteres\",\"Tamaño vertical\",\"Proporcional\",\"Relleno\",\"Ajustar\",\"Control deslizante\",\"Pasar el mouse\",\"Siempre\",\"Mostrar menú contextual del editor\",\"Cursor Deshacer\",\"Cursor Rehacer\",\"El tipo de edición de pegado con la que se intentará pegar.\\r\\nEn caso de haber varias ediciones para esta variante, el editor mostrará un selector. En caso de que no haya ediciones de este tipo, el editor mostrará un mensaje de error.\",\"Pegar como...\",\"Pegar como texto\",\"Si se muestra el widget de pegado\",\"Mostrar opciones de pegado...\",\"No se encontraron ediciones de pegado para '{0}'\",\"Resolviendo la edición de pegado. Hacer clic para cancelar\",\"Ejecutando controladores de pegado. Haga clic para cancelar y hacer pegado básico\",\"Seleccionar acción pegar\",\"Ejecutando controladores de pegado\",\"Insertar texto sin formato\",\"Insertar URIs\",\"Insertar URI\",\"Insertar rutas de acceso\",\"Insertar ruta de acceso\",\"Insertar rutas de acceso relativas\",\"Insertar ruta de acceso relativa\",\"Insertar HTML\",null,\"Si se muestra el widget de colocación\",\"Mostrar opciones de colocación...\",\"Ejecutando controladores de colocación. Haga clic para cancelar.\",\"Error al resolver la edición \\\"{0}\\\":\\r\\n{1}\",\"Error al aplicar la edición ''{0}:\\r\\n{1}\",\"Indica si el editor ejecuta una operación que se puede cancelar como, por ejemplo, \\\"Inspeccionar referencias\\\"\",\"El archivo es demasiado grande para realizar una operación de reemplazar todo.\",\"Buscar\",\"&&Buscar\",\"Búsqueda con argumentos\",\"Buscar con selección\",\"Buscar siguiente\",\"Buscar anterior\",\"Ir a Coincidencia...\",\"No hay coincidencias. Intente buscar otra cosa.\",\"Escriba un número para ir a una coincidencia específica (entre 1 y {0})\",\"Escriba un número entre 1 y {0}\",\"Escriba un número entre 1 y {0}\",\"Buscar selección siguiente\",\"Buscar selección anterior\",\"Reemplazar\",\"&&Reemplazar\",\"Icono para indicar que el widget de búsqueda del editor está contraído.\",\"Icono para indicar que el widget de búsqueda del editor está expandido.\",\"Icono para \\\"Buscar en selección\\\" en el widget de búsqueda del editor.\",\"Icono para \\\"Reemplazar\\\" en el widget de búsqueda del editor.\",\"Icono para \\\"Reemplazar todo\\\" en el widget de búsqueda del editor.\",\"Icono para \\\"Buscar anterior\\\" en el widget de búsqueda del editor.\",\"Icono para \\\"Buscar siguiente\\\" en el widget de búsqueda del editor.\",\"Buscar y reemplazar\",\"Buscar\",\"Buscar\",\"Coincidencia anterior\",\"Coincidencia siguiente\",\"Buscar en selección\",\"Cerrar\",\"Reemplazar\",\"Reemplazar\",\"Reemplazar\",\"Reemplazar todo\",\"Alternar reemplazar\",\"Sólo los primeros {0} resultados son resaltados, pero todas las operaciones de búsqueda trabajan en todo el texto.\",\"{0} de {1}\",\"No hay resultados\",\"Encontrados: {0}\",\"{0} encontrado para \\\"{1}\\\"\",\"{0} encontrado para \\\"{1}\\\", en {2}\",\"{0} encontrado para \\\"{1}\\\"\",\"Ctrl+Entrar ahora inserta un salto de línea en lugar de reemplazar todo. Puede modificar el enlace de claves para editor.action.replaceAll para invalidar este comportamiento.\",\"Desplegar\",\"Desplegar de forma recursiva\",\"Plegar\",\"Alternar plegado\",\"Plegar de forma recursiva\",\"Alternar plegado recursivo\",\"Cerrar todos los comentarios de bloque\",\"Plegar todas las regiones\",\"Desplegar Todas las Regiones\",\"Plegar todas excepto las seleccionadas\",\"Desplegar todas excepto las seleccionadas\",\"Plegar todo\",\"Desplegar todo\",\"Ir al plegado primario\",\"Ir al rango de plegado anterior\",\"Ir al rango de plegado siguiente\",\"Crear rango de plegado a partir de la selección\",\"Quitar rangos de plegado manuales\",\"Nivel de plegamiento {0}\",\"Color de fondo detrás de los rangos plegados. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color del texto contraído después de la primera línea de un rango doblado.\",\"Color del control plegable en el medianil del editor.\",\"Icono de rangos expandidos en el margen de glifo del editor.\",\"Icono de rangos contraídos en el margen de glifo del editor.\",\"Icono de intervalos contraídos manualmente en el margen del glifo del editor.\",\"Icono de intervalos expandidos manualmente en el margen del glifo del editor.\",\"Haga clic para expandir el rango.\",\"Haga clic para contraer el intervalo.\",\"Aumentar el tamaño de fuente del editor\",\"Disminuir el tamaño de fuente del editor\",\"Restablecer tamaño de fuente del editor\",\"Dar formato al documento\",\"Dar formato a la selección\",\"Ir al siguiente problema (Error, Advertencia, Información)\",\"Icono para ir al marcador siguiente.\",\"Ir al problema anterior (Error, Advertencia, Información)\",\"Icono para ir al marcador anterior.\",\"Ir al siguiente problema en Archivos (Error, Advertencia, Información)\",\"Siguiente &&problema\",\"Ir al problema anterior en Archivos (Error, Advertencia, Información)\",\"Anterior &&problema\",\"Error\",\"Advertencia\",\"Información\",\"Sugerencia\",\"{0} en {1}. \",\"{0} de {1} problemas\",\"{0} de {1} problema\",\"Color de los errores del widget de navegación de marcadores del editor.\",\"Fondo del encabezado del error del widget de navegación del marcador de editor.\",\"Color de las advertencias del widget de navegación de marcadores del editor.\",\"Fondo del encabezado de la advertencia del widget de navegación del marcador de editor.\",\"Color del widget informativo marcador de navegación en el editor.\",\"Fondo del encabezado de información del widget de navegación del marcador de editor.\",\"Fondo del widget de navegación de marcadores del editor.\",\"Ver\",\"Definiciones\",\"No se encontró ninguna definición para \\\"{0}\\\"\",\"No se encontró ninguna definición\",\"Ir a &&definición\",\"Declaraciones\",\"No se encontró ninguna definición para '{0}'\",\"No se encontró ninguna declaración\",\"Ir a &&declaración\",\"No se encontró ninguna definición para '{0}'\",\"No se encontró ninguna declaración\",\"Definiciones de tipo\",\"No se encontró ninguna definición de tipo para \\\"{0}\\\"\",\"No se encontró ninguna definición de tipo\",\"Ir a la definición de &&tipo\",\"Implementaciones\",\"No se encontró ninguna implementación para \\\"{0}\\\"\",\"No se encontró ninguna implementación\",\"Ir a &&implementaciones\",\"No se ha encontrado ninguna referencia para \\\"{0}\\\".\",\"No se encontraron referencias\",\"Ir a &&referencias\",\"Referencias\",\"Referencias\",\"Ubicaciones\",\"No hay resultados para \\\"{0}\\\"\",\"Referencias\",\"Ir a definición\",\"Abrir definición en el lateral\",\"Ver la definición sin salir\",\"Ir a Definición\",\"Inspeccionar Definición\",\"Ir a la definición de tipo\",\"Inspeccionar definición de tipo\",\"Ir a Implementaciones\",\"Inspeccionar implementaciones\",\"Ir a Referencias\",\"Inspeccionar Referencias\",\"Ir a cualquier símbolo\",\"Haga clic para mostrar {0} definiciones.\",\"Indica si está visible la inspección de referencias, como \\\"Inspección de referencias\\\" o \\\"Ver la definición sin salir\\\".\",\"Cargando...\",\"{0} ({1})\",\"{0} referencias\",\"{0} referencia\",\"Referencias\",\"vista previa no disponible\",\"No hay resultados\",\"Referencias\",\"en {0} en la línea {1} en la columna {2}\",\"{0} en {1} en la línea {2} en la columna {3}\",\"1 símbolo en {0}, ruta de acceso completa {1}\",\"{0} símbolos en {1}, ruta de acceso completa {2}\",\"No se encontraron resultados\",\"Encontró 1 símbolo en {0}\",\"Encontró {0} símbolos en {1}\",\"Encontró {0} símbolos en {1} archivos\",\"Indica si hay ubicaciones de símbolos a las que se pueda navegar solo con el teclado.\",\"Símbolo {0} de {1}, {2} para el siguiente\",\"Símbolo {0} de {1}\",\"Aumentar nivel de detalle al mantener el puntero sobre el editor\",\"Disminuir nivel de detalle al mantener el puntero\",\"Mostrar o centrarse al mantener el puntero\",\"El cuadro del elemento sobre el que se ha pasado el ratón se enfocará automáticamente.\",\"El cuadro del elemento sobre el que se ha pasado el ratón se enfocará solo si ya está visible.\",\"Se enfocará el cuadro que aparece cuando se pasa el ratón por encima de un elemento.\",\"Mostrar vista previa de la definición que aparece al mover el puntero\",\"Desplazar hacia arriba al mantener el puntero\",\"Desplazar hacia abajo al mantener el puntero\",\"Desplazar al mantener el puntero a la izquierda\",\"Desplazar al mantener el puntero a la derecha\",\"Desplazamiento de página hacia arriba\",\"Desplazamiento de página hacia abajo\",\"Ir al puntero superior\",\"Ir a la parte inferior al mantener el puntero\",\"Mostrar o centrar al mantener el puntero sobre el editor, que muestra documentación, referencias y otro contenido para un símbolo en la posición actual del cursor.\",\"Muestre la vista previa de la definición al mantener el puntero sobre el editor.\",\"Desplácese hacia arriba al mantener el puntero sobre el editor.\",\"Desplácese hacia abajo al mantener el puntero sobre el editor.\",\"Desplácese a la izquierda al mantener el puntero sobre el editor.\",\"Desplácese a la derecha al mantener el puntero sobre el editor.\",\"Desplazar página hacia arriba al mantener el puntero sobre el editor.\",\"Desplace la página hacia abajo al mantener el puntero sobre el editor.\",\"Vaya a la parte superior del editor al mantener el puntero.\",\"Vaya a la parte inferior del editor al mantener el puntero sobre el editor.\",\"Icono para aumentar el nivel de detalle al mantener el puntero.\",\"Icono para reducir el nivel de detalle al mantener el puntero.\",\"Cargando...\",\"Representación en pausa durante una línea larga por motivos de rendimiento. Esto se puede configurar mediante \\\"editor.stopRenderingLineAfter\\\".\",\"Por motivos de rendimiento, la tokenización se omite con filas largas. Esta opción se puede configurar con \\\"editor.maxTokenizationLineLength\\\".\",\"Aumentar nivel de detalle al mantener el puntero ({0})\",\"Aumentar nivel de detalle al mantener el puntero\",\"Disminuir nivel de detalle al mantener el puntero ({0})\",\"Disminuir nivel de detalle al mantener el puntero\",\"Ver el problema\",\"No hay correcciones rápidas disponibles\",\"Buscando correcciones rápidas...\",\"No hay correcciones rápidas disponibles\",\"Corrección Rápida\",\"Convertir sangría en espacios\",\"Convertir sangría en tabulaciones\",\"Tamaño de tabulación configurado\",\"Tamaño de tabulación predeterminado\",\"Tamaño de tabulación actual\",\"Seleccionar tamaño de tabulación para el archivo actual\",\"Aplicar sangría con tabulaciones\",\"Aplicar sangría con espacios\",\"Cambiar tamaño de visualización de tabulación\",\"Detectar sangría del contenido\",\"Volver a aplicar sangría a líneas\",\"Volver a aplicar sangría a líneas seleccionadas\",\"Convierta la sangría de tabulación en espacios.\",\"Convierte la sangría de espacios en tabulaciones.\",\"Usar sangría con tabulaciones.\",\"Use sangría con espacios.\",\"Cambie el tamaño de espacio equivalente de la pestaña.\",\"Detectar la sangría del contenido.\",\"Vuelva a aplicar sangría a las líneas del editor.\",\"Vuelve a aplicar sangría a las líneas seleccionadas del editor.\",\"Haga doble clic para insertar\",\"cmd + clic\",\"ctrl + clic\",\"opción + clic\",\"alt + clic\",\"Ir a Definición ({0}), haga clic con el botón derecho para obtener más información\",\"Ir a Definición ({0})\",\"Ejecutar comando\",\"Mostrar sugerencia alineada siguiente\",\"Mostrar sugerencia alineada anterior\",\"Desencadenar sugerencia insertada\",\"Aceptar la siguiente palabra de sugerencia insertada\",\"Aceptar palabra\",\"Aceptar la siguiente línea de sugerencia insertada\",\"Aceptar línea\",\"Aceptar sugerencia insertada\",\"Aceptar\",\"Ocultar sugerencia insertada\",\"Mostrar siempre la barra de herramientas\",\"Si una sugerencia alineada está visible\",\"Si la sugerencia alineada comienza con un espacio en blanco\",\"Si la sugerencia insertada comienza con un espacio en blanco menor que lo que se insertaría mediante tabulación\",\"Si las sugerencias deben suprimirse para la sugerencia actual\",\"Inspeccionar esto en la vista accesible ({0})\",\"Sugerencia:\",\"Icono para mostrar la sugerencia de parámetro siguiente.\",\"Icono para mostrar la sugerencia de parámetro anterior.\",\"{0} ({1})\",\"Anterior\",\"Siguiente\",null,null,null,null,null,null,null,null,\"Reemplazar con el valor anterior\",\"Reemplazar con el valor siguiente\",\"Expandir selección de línea\",\"Copiar línea arriba\",\"&&Copiar línea arriba\",\"Copiar línea abajo\",\"Co&&piar línea abajo\",\"Selección duplicada\",\"&&Duplicar selección\",\"Mover línea hacia arriba\",\"Mo&&ver línea arriba\",\"Mover línea hacia abajo\",\"Mover &&línea abajo\",\"Ordenar líneas en orden ascendente\",\"Ordenar líneas en orden descendente\",\"Eliminar líneas duplicadas\",\"Recortar espacio final\",\"Eliminar línea\",\"Sangría de línea\",\"Anular sangría de línea\",\"Insertar línea arriba\",\"Insertar línea debajo\",\"Eliminar todo a la izquierda\",\"Eliminar todo lo que está a la derecha\",\"Unir líneas\",\"Transponer caracteres alrededor del cursor\",\"Transformar a mayúsculas\",\"Transformar a minúsculas\",\"Transformar en Title Case\",\"Transformar en Snake Case\",\"Transformar a mayúsculas y minúsculas Camel\",\"Transformar en Pascal Case\",\"Transformar en caso Kebab\",\"Iniciar edición vinculada\",\"Color de fondo cuando el editor cambia el nombre automáticamente al escribir.\",\"No se pudo abrir este vínculo porque no tiene un formato correcto: {0}\",\"No se pudo abrir este vínculo porque falta el destino.\",\"Ejecutar comando\",\"Seguir vínculo\",\"cmd + clic\",\"ctrl + clic\",\"opción + clic\",\"alt + clic\",\"Ejecutar el comando {0}\",\"Abrir vínculo\",\"Indica si el editor muestra actualmente un mensaje insertado\",\"Cursor agregado: {0}\",\"Cursores agregados: {0}\",\"Agregar cursor arriba\",\"&&Agregar cursor arriba\",\"Agregar cursor debajo\",\"A&&gregar cursor abajo\",\"Añadir cursores a finales de línea\",\"Agregar c&&ursores a extremos de línea\",\"Añadir cursores a la parte inferior\",\"Añadir cursores a la parte superior\",\"Agregar selección hasta la siguiente coincidencia de búsqueda\",\"Agregar &&siguiente repetición\",\"Agregar selección hasta la anterior coincidencia de búsqueda\",\"Agregar r&&epetición anterior\",\"Mover última selección hasta la siguiente coincidencia de búsqueda\",\"Mover última selección hasta la anterior coincidencia de búsqueda\",\"Seleccionar todas las repeticiones de coincidencia de búsqueda\",\"Seleccionar todas las &&repeticiones\",\"Cambiar todas las ocurrencias\",\"Enfocar el siguiente cursor\",\"Centra el cursor siguiente\",\"Enfocar cursor anterior\",\"Centra el cursor anterior\",\"Sugerencias para parámetros Trigger\",\"Icono para mostrar la sugerencia de parámetro siguiente.\",\"Icono para mostrar la sugerencia de parámetro anterior.\",\"{0}, sugerencia\",\"Color de primer plano del elemento activo en la sugerencia de parámetro.\",\"Indica si el editor de código actual está incrustado en la inspección.\",\"Cerrar\",\"Color de fondo del área de título de la vista de inspección.\",\"Color del título de la vista de inpección.\",\"Color de la información del título de la vista de inspección.\",\"Color de los bordes y la flecha de la vista de inspección.\",\"Color de fondo de la lista de resultados de vista de inspección.\",\"Color de primer plano de los nodos de inspección en la lista de resultados.\",\"Color de primer plano de los archivos de inspección en la lista de resultados.\",\"Color de fondo de la entrada seleccionada en la lista de resultados de vista de inspección.\",\"Color de primer plano de la entrada seleccionada en la lista de resultados de vista de inspección.\",\"Color de fondo del editor de vista de inspección.\",\"Color de fondo del margen en el editor de vista de inspección.\",\"Color de fondo del desplazamiento permanente en el editor de vista de inspección.\",\"Buscar coincidencia con el color de resaltado de la lista de resultados de vista de inspección.\",\"Buscar coincidencia del color de resultado del editor de vista de inspección.\",\"Hacer coincidir el borde resaltado en el editor de vista previa.\",\"Color de primer plano del texto del marcador de posición en el editor.\",\"Abra primero un editor de texto para ir a una línea.\",\"Vaya a la línea {0} y al carácter {1}.\",\"Ir a la línea {0}.\",\"Línea actual: {0}, Carácter: {1}. Escriba un número de línea entre 1 y {2} a los que navegar.\",\"Línea actual: {0}, Carácter: {1}. Escriba un número de línea al que navegar.\",\"Para ir a un símbolo, primero abra un editor de texto con información de símbolo.\",\"El editor de texto activo no proporciona información de símbolos.\",\"No hay ningún símbolo del editor coincidente.\",\"No hay símbolos del editor.\",\"Abrir en el lateral\",\"Abrir en la parte inferior\",\"símbolos ({0})\",\"propiedades ({0})\",\"métodos ({0})\",\"funciones ({0})\",\"constructores ({0})\",\"variables ({0})\",\"clases ({0})\",\"estructuras ({0})\",\"eventos ({0})\",\"operadores ({0})\",\"interfaces ({0})\",\"espacios de nombres ({0})\",\"paquetes ({0})\",\"parámetros de tipo ({0})\",\"módulos ({0})\",\"propiedades ({0})\",\"enumeraciones ({0})\",\"miembros de enumeración ({0})\",\"cadenas ({0})\",\"archivos ({0})\",\"matrices ({0})\",\"números ({0})\",\"booleanos ({0})\",\"objetos ({0})\",\"claves ({0})\",\"campos ({0})\",\"constantes ({0})\",\"No se puede editar en la entrada de solo lectura\",\"No se puede editar en un editor de sólo lectura\",\"No hay ningún resultado.\",\"Error desconocido al resolver el cambio de nombre de la ubicación\",\"Cambiando el nombre de '{0}' a '{1}'\",\"Cambiar el nombre de {0} a {1}\",\"Nombre cambiado correctamente de '{0}' a '{1}'. Resumen: {2}\",\"No se pudo cambiar el nombre a las ediciones de aplicación\",\"No se pudo cambiar el nombre de las ediciones de cálculo\",\"Cambiar el nombre del símbolo\",\"Activar/desactivar la capacidad de previsualizar los cambios antes de cambiar el nombre\",\"Enfocar la siguiente sugerencia de cambio de nombre\",\"Enfocar sugerencia de cambio de nombre anterior\",\"Indica si el widget de cambio de nombre de entrada está visible.\",\"Si el widget de entrada de cambio de nombre está enfocado\",\"{0} para cambiar de nombre, {1} para obtener una vista previa\",\"Se han recibido {0} sugerencias de cambio de nombre\",\"Cambie el nombre de la entrada. Escriba el nuevo nombre y presione Entrar para confirmar.\",\"Generar sugerencias de nombre nuevo\",\"Cancelar\",\"Expandir selección\",\"&&Expandir selección\",\"Reducir la selección\",\"&&Reducir selección\",\"Indica si el editor actual está en modo de fragmentos de código.\",\"Indica si hay una tabulación siguiente cuando se está en modo de fragmentos de código.\",\"Si hay una tabulación anterior cuando se está en modo de fragmentos de código.\",\"Ir al marcador de posición siguiente...\",\"Domingo\",\"Lunes\",\"Martes\",\"Miércoles\",\"Jueves\",\"Viernes\",\"Sábado\",\"Dom\",\"Lun\",\"Mar\",\"Mié\",\"Jue\",\"Vie\",\"Sáb\",\"Enero\",\"Febrero\",\"Marzo\",\"Abril\",\"May\",\"Junio\",\"Julio\",\"Agosto\",\"Septiembre\",\"Octubre\",\"Noviembre\",\"Diciembre\",\"Ene\",\"Feb\",\"Mar\",\"Abr\",\"May\",\"Jun\",\"Jul\",\"Ago\",\"Sep\",\"Oct\",\"Nov\",\"Dic\",\"&&Alternar desplazamiento permanente del editor\",\"Desplazamiento permanente\",\"&&Desplazamiento permanente\",\"&&Desplazamiento permanente de foco\",\"Alternar desplazamiento Sticky del editor\",\"Alternar o habilitar el desplazamiento permanente del editor que muestra los ámbitos anidados en la parte superior de la ventanilla\",\"Centrarse en el desplazamiento permanente del editor\",\"Seleccionar la siguiente línea de desplazamiento rápida del editor\",\"Seleccionar la línea de desplazamiento rápida anterior\",\"Ir a la línea de desplazamiento rápida centrada\",\"Seleccionar el Editor\",\"Si alguna sugerencia tiene el foco\",\"Indica si los detalles de las sugerencias están visibles.\",\"Indica si hay varias sugerencias para elegir.\",\"Indica si la inserción de la sugerencia actual genera un cambio o si ya se ha escrito todo.\",\"Indica si se insertan sugerencias al presionar Entrar.\",\"Indica si la sugerencia actual tiene el comportamiento de inserción y reemplazo.\",\"Indica si el comportamiento predeterminado es insertar o reemplazar.\",\"Indica si la sugerencia actual admite la resolución de más detalles.\",\"Aceptando \\\"{0}\\\" ediciones adicionales de {1} realizadas\",\"Sugerencias para Trigger\",\"Insertar\",\"Insertar\",\"Reemplazar\",\"Reemplazar\",\"Insertar\",\"Mostrar menos\",\"Mostrar más\",\"Restablecer tamaño del widget de sugerencias\",\"Color de fondo del widget sugerido.\",\"Color de borde del widget sugerido.\",\"Color de primer plano del widget sugerido.\",\"Color de primer plano de le entrada seleccionada del widget de sugerencias.\",\"Color de primer plano del icono de la entrada seleccionada en el widget de sugerencias.\",\"Color de fondo de la entrada seleccionada del widget sugerido.\",\"Color del resaltado coincidido en el widget sugerido.\",\"Color de los resaltados de coincidencia en el widget de sugerencias cuando se enfoca un elemento.\",\"Color de primer plano del estado del widget sugerido.\",\"Cargando...\",\"No hay sugerencias.\",\"Sugerir\",\"{0} {1}, {2}\",\"{0} {1}\",\"{0}, {1}\",\"{0}, documentos: {1}\",\"Cerrar\",\"Cargando...\",\"Icono para obtener más información en el widget de sugerencias.\",\"Leer más\",\"Color de primer plano de los símbolos de matriz. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos booleanos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de clase. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de color. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos constantes. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de constructor. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de enumerador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de miembro del enumerador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de evento. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de campo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de archivo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de carpeta. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de función. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de interfaz. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de claves. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de palabra clave. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de método. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de módulo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de espacio de nombres. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos nulos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano para los símbolos numéricos. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de objeto. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano para los símbolos del operador. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de paquete. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de propiedad. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de referencia. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de fragmento de código. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de cadena. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de estructura. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de texto. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano para los símbolos de parámetro de tipo. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos de unidad. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Color de primer plano de los símbolos variables. Estos símbolos aparecen en el contorno, la ruta de navegación y el widget de sugerencias.\",\"Presionando la pestaña ahora moverá el foco al siguiente elemento enfocable.\",\"Presionando la pestaña ahora insertará el carácter de tabulación\",\"Alternar tecla de tabulación para mover el punto de atención\",\"Determina si la tecla tab mueve el foco alrededor del área de trabajo o inserta el carácter de tabulación en el editor actual. Esto también se denomina captura de pestañas, navegación por pestañas o modo de enfoque de pestañas.\",\"Desarrollador: forzar nueva aplicación de token\",\"Icono que se muestra con un mensaje de advertencia en el editor de extensiones.\",\"Este documento contiene muchos caracteres Unicode ASCII no básicos\",\"Este documento contiene muchos caracteres Unicode ambiguos\",\"Este documento contiene muchos caracteres Unicode invisibles\",\"Configurar opciones de resaltado Unicode\",\"El carácter {0} podría confundirse con el carácter ASCII {1}, que es más común en el código fuente.\",\"El carácter {0} podría confundirse con el carácter {1}, que es más común en el código fuente.\",\"El carácter {0} es invisible.\",\"El carácter {0} no es un carácter ASCII básico.\",\"Ajustar la configuración\",\"Deshabilitar resaltado en comentarios\",\"Deshabilitar resaltado de caracteres en comentarios\",\"Deshabilitar resaltado en cadenas\",\"Deshabilitar resaltado de caracteres en cadenas\",\"Deshabilitar resaltado ambiguo\",\"Deshabilitar el resaltado de caracteres ambiguos\",\"Deshabilitar resaltado invisible\",\"Deshabilitar el resaltado de caracteres invisibles\",\"Deshabilitar resaltado que no es ASCII\",\"Deshabilitar el resaltado de caracteres ASCII no básicos\",\"Mostrar opciones de exclusión\",\"Excluir {0} (carácter invisible) de que se resalte\",\"Excluir {0} de ser resaltado\",\"Permite caracteres Unicode más comunes en el idioma \\\"{0}\\\".\",\"Terminadores de línea inusuales\",\"Se han detectado terminadores de línea inusuales\",\"Este archivo \\\"{0}\\\" contiene uno o más caracteres de terminación de línea inusuales, como el separador de línea (LS) o el separador de párrafo (PS).\\r\\n\\r\\nSe recomienda eliminarlos del archivo. Esto puede configurarse mediante \\\"editor.unusualLineTerminators\\\".\",\"&&Quitar terminadores de línea inusuales\",\"Omitir\",\"Color de fondo de un símbolo durante el acceso de lectura, como la lectura de una variable. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Color de fondo de un símbolo durante el acceso de escritura, como escribir en una variable. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de fondo de la presencia textual para un símbolo. Para evitar ocultar cualquier decoración subyacente, el color no debe ser opaco.\",\"Color de fondo de un símbolo durante el acceso de lectura; por ejemplo, cuando se lee una variable.\",\"Color de fondo de un símbolo durante el acceso de escritura; por ejemplo, cuando se escribe una variable.\",\"Color de borde de una repetición textual de un símbolo.\",\"Color del marcador de regla general para destacados de símbolos. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Color de marcador de regla general para destacados de símbolos de acceso de escritura. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color del marcador de regla de información general de una repetición textual de un símbolo. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Ir al siguiente símbolo destacado\",\"Ir al símbolo destacado anterior\",\"Desencadenar los símbolos destacados\",\"Eliminar palabra\",\"Error en la posición\",\"Error\",\"Advertencia en posición\",\"Advertencia\",\"Error en la línea\",\"Error en la línea\",\"Advertencia en la línea\",\"Advertencia en la línea\",\"Área doblada en la línea\",\"Contraída\",\"Punto de interrupción en la línea\",\"Punto de interrupción\",\"Sugerencia insertada en la línea\",\"Corrección rápida del terminal\",\"Corrección rápida\",\"Depurador detenido en el punto de interrupción\",\"Punto de interrupción\",\"No hay sugerencias de incrustación en la línea\",\"No hay sugerencias de incrustación\",\"Tarea completada.\",\"Tarea completada.\",\"Error en la tarea\",\"Error en la tarea\",\"Error del comando de terminal\",\"Error del comando\",\"Comando de terminal correcto\",\"Comando correcto\",\"Campana de terminal\",\"Campana de terminal\",\"Celda del bloc de notas completada\",\"Celda del bloc de notas completada\",\"Error en la celda del bloc de notas\",\"Error en la celda del bloc de notas\",\"Línea de diferencia insertada\",\"Línea de diferencia eliminada\",\"Línea de diferencia modificada\",\"Se envió una solicitud de chat\",\"Se envió una solicitud de chat\",\"Respuesta de chat recibida\",\"Progreso\",\"Progreso\",\"Borrar\",\"Borrar\",\"Guardar\",\"Guardar\",\"Formato\",\"Formato\",\"Grabación de voz iniciada\",\"Grabación de voz detenida\",\"Ver\",\"Ayuda\",\"Probar\",\"archivo\",\"Preferencias\",\"Desarrollador\",\"{0} ({1})\",\"{0} ({1})\",\"{0}\\r\\n[{1}] {2}\",\"{1} al {0}\",\"{0} ({1})\",\"Ocultar\",\"Menú Restablecer\",\"Ocultar \\\"{0}\\\"\",\"Configurar el enlace de teclado\",\"{0} para aplicar, {1} para previsualizar\",\"{0} para aplicar\",\"{0}, Motivo de deshabilitación: {1}\",\"Widget de acción\",\"Color de fondo de los elementos de acción alternados en la barra de acciones.\",\"Si la lista de widgets de acción es visible\",\"Ocultar el widget de acción\",\"Seleccione la acción anterior\",\"Seleccione la siguiente acción\",\"Aceptar la acción seleccionada\",\"Vista previa de la acción seleccionada\",\"La configuración del lenguaje predeterminada se reemplaza\",\"Configure los valores que se invalidarán para el idioma {0}.\",\"Establecer los valores de configuración que se reemplazarán para un lenguaje.\",\"Esta configuración no admite la configuración por idioma.\",\"Establecer los valores de configuración que se reemplazarán para un lenguaje.\",\"Esta configuración no admite la configuración por idioma.\",\"No se puede registrar una propiedad vacía.\",\"No se puede registrar \\\"{0}\\\". Coincide con el patrón de propiedad '\\\\\\\\[.*\\\\\\\\]$' para describir la configuración del editor específica del lenguaje. Utilice la contribución \\\"configurationDefaults\\\".\",\"No se puede registrar \\\"{0}\\\". Esta propiedad ya está registrada.\",\"No se puede registrar \\\"{0}\\\". La directiva asociada {1} ya está registrada con {2}.\",\"Comando que devuelve información sobre las claves de contexto\",\"Expresión de clave de contexto vacía\",\"¿Ha olvidado escribir una expresión? también puede poner \\\"false\\\" o \\\"true\\\" para evaluar siempre como false o true, respectivamente.\",\"'in' después de 'not'.\",\"paréntesis de cierre ')'\",\"Token inesperado\",\"¿Ha olvidado poner && o || antes del token?\",\"Final de expresión inesperado\",\"¿Ha olvidado poner una clave de contexto?\",\"Esperado: {0}\\r\\nrecibido: '{1}'.\",\"Si el sistema operativo es macOS\",\"Si el sistema operativo es Linux\",\"Si el sistema operativo es Windows\",\"Si la plataforma es un explorador web\",\"Si el sistema operativo es macOS en una plataforma que no es de explorador\",\"Si el sistema operativo es IOS\",\"Si la plataforma es un explorador web móvil\",\"Tipo de calidad de VS Code\",\"Si el foco del teclado está dentro de un cuadro de entrada\",\"¿Quiso decir {0}?\",\"¿Quiso decir {0} o {1}?\",\"¿Quiso decir {0}, {1} o {2}?\",\"¿Ha olvidado abrir o cerrar la cita?\",\"¿Ha olvidado escapar el carácter \\\"/\\\" (barra diagonal)?Coloque dos barras diagonales inversas antes de que escape, por ejemplo, '\\\\\\\\/'.\",\"Indica si las sugerencias están visibles.\",\"Se presionó ({0}). Esperando la siguiente tecla...\",\"Se ha presionado ({0}). Esperando la siguiente tecla...\",\"La combinación de claves ({0}, {1}) no es un comando.\",\"La combinación de claves ({0}, {1}) no es un comando.\",\"Área de trabajo\",\"Se asigna a \\\"Control\\\" en Windows y Linux y a \\\"Comando\\\" en macOS.\",\"Se asigna a \\\"Alt\\\" en Windows y Linux y a \\\"Opción\\\" en macOS.\",\"El modificador que se utilizará para agregar un elemento en los árboles y listas para una selección múltiple con el ratón (por ejemplo en el explorador, abiertos editores y vista de scm). Los gestos de ratón 'Abrir hacia' - si están soportados - se adaptarán de forma tal que no tenga conflicto con el modificador múltiple.\",\"Controla cómo abrir elementos en los árboles y las listas mediante el mouse (si se admite). Tenga en cuenta que algunos árboles y listas pueden optar por ignorar esta configuración si no es aplicable.\",\"Controla si las listas y los árboles admiten el desplazamiento horizontal en el área de trabajo. Advertencia: La activación de esta configuración repercute en el rendimiento.\",\"Controla si los clics en la barra de desplazamiento se desplazan página por página.\",\"Controla la sangría de árbol en píxeles.\",\"Controla si el árbol debe representar guías de sangría.\",\"Controla si las listas y los árboles tienen un desplazamiento suave.\",\"Se usará un multiplicador en los eventos de desplazamiento de la rueda del mouse \\\"deltaX\\\" y \\\"deltaY\\\". \",\"Multiplicador de la velocidad de desplazamiento al presionar \\\"Alt\\\".\",\"Resalta elementos al buscar. Navegar más arriba o abajo pasará solo por los elementos resaltados.\",\"Filtre elementos al buscar.\",\"Controla el modo de búsqueda predeterminado para listas y árboles en el área de trabajo.\",\"La navegación simple del teclado se centra en elementos que coinciden con la entrada del teclado. El emparejamiento se hace solo en prefijos.\",\"Destacar la navegación del teclado resalta los elementos que coinciden con la entrada del teclado. Más arriba y abajo la navegación atravesará solo los elementos destacados.\",\"La navegación mediante el teclado de filtro filtrará y ocultará todos los elementos que no coincidan con la entrada del teclado.\",\"Controla el estilo de navegación del teclado para listas y árboles en el área de trabajo. Puede ser simple, resaltar y filtrar.\",\"Use \\\"workbench.list.defaultFindMode\\\" y \\\"workbench.list.typeNavigationMode\\\" en su lugar.\",\"Usar coincidencias aproximadas al buscar.\",\"Use coincidencias contiguas al buscar.\",\"Controla el tipo de coincidencia que se usa al buscar listas y árboles en el área de trabajo.\",\"Controla cómo se expanden las carpetas de árbol al hacer clic en sus nombres. Tenga en cuenta que algunos árboles y listas pueden optar por omitir esta configuración si no es aplicable.\",\"Controla si el desplazamiento permanente está habilitado en los árboles.\",\"Controla el número de elementos permanentes que se muestran en el árbol cuando {0} está habilitado.\",\"Controla el funcionamiento de la navegación por tipos en listas y árboles del área de trabajo. Cuando se establece en \\\"trigger\\\", la navegación por tipos comienza una vez que se ejecuta el comando \\\"list.triggerTypeNavigation\\\".\",\"Error\",\"Advertencia\",\"Información\",\"usado recientemente\",\"comandos similares\",\"usados habitualmente\",\"otros comandos\",\"comandos similares\",\"{0}, {1}\",\"El comando \\\"{0}\\\" ha dado lugar a un error\",\"{0}, {1}\",\"Si el foco del teclado está dentro del control de entrada rápida\",\"El tipo de la entrada rápida visible actualmente\",\"Si el cursor de la entrada rápida está al final del cuadro de entrada\",\"Atrás\",\"Presione \\\"Entrar\\\" para confirmar su entrada o \\\"Esc\\\" para cancelar\",\"{0}/{1}\",\"Escriba para restringir los resultados.\",\"Se usa en el contexto de la selección rápida. Si cambia un enlace de teclado para este comando, también debe cambiar todos los demás enlaces de teclado (variantes modificadoras) de este comando.\",\"Si estamos en modo de acceso rápido, se desplazará al siguiente elemento. Si no estamos en modo de acceso rápido, se desplazará al separador siguiente.\",\"Si estamos en modo de acceso rápido, se navegará al elemento anterior. Si no estamos en modo de acceso rápido, se navegará al separador anterior.\",\"Activar o desactivar todas las casillas\",\"{0} resultados\",\"{0} seleccionados\",\"Aceptar\",\"Personalizado\",\"Atrás ({0})\",\"Atrás\",\"Entrada rápida\",\"Haga clic en para ejecutar el comando \\\"{0}\\\"\",\"Color de primer plano general. Este color solo se usa si un componente no lo invalida.\",\"Primer plano general de los elementos deshabilitados. Este color solo se usa si un componente no lo reemplaza.\",\"Color de primer plano general para los mensajes de erroe. Este color solo se usa si un componente no lo invalida.\",\"Color de primer plano para el texto descriptivo que proporciona información adicional, por ejemplo para una etiqueta.\",\"El color predeterminado para los iconos en el área de trabajo.\",\"Color de borde de los elementos con foco. Este color solo se usa si un componente no lo invalida.\",\"Un borde adicional alrededor de los elementos para separarlos unos de otros y así mejorar el contraste.\",\"Un borde adicional alrededor de los elementos activos para separarlos unos de otros y así mejorar el contraste.\",\"El color de fondo del texto seleccionado en el área de trabajo (por ejemplo, campos de entrada o áreas de texto). Esto no se aplica a las selecciones dentro del editor.\",\"Color de primer plano para los vínculos en el texto.\",\"Color de primer plano para los enlaces de texto, al hacer clic o pasar el mouse sobre ellos.\",\"Color para los separadores de texto.\",\"Color de primer plano para los segmentos de texto con formato previo.\",\"Color de fondo para segmentos de texto con formato previo.\",\"Color de fondo para los bloques en texto.\",\"Color de borde para los bloques en texto.\",\"Color de fondo para los bloques de código en el texto.\",\"Color de primer plano que se usa en los gráficos.\",\"Color que se usa para las líneas horizontales en los gráficos.\",\"Color rojo que se usa en las visualizaciones de gráficos.\",\"Color azul que se usa en las visualizaciones de gráficos.\",\"Color amarillo que se usa en las visualizaciones de gráficos.\",\"Color naranja que se usa en las visualizaciones de gráficos.\",\"Color verde que se usa en las visualizaciones de gráficos.\",\"Color púrpura que se usa en las visualizaciones de gráficos.\",\"Color de fondo del editor.\",\"Color de primer plano predeterminado del editor.\",\"Color de fondo del desplazamiento permanente en el editor\",\"Color de fondo del desplazamiento permanente al mantener el mouse en el editor\",\"Color de borde del desplazamiento permanente en el editor\",\" Color de sombra del desplazamiento permanente en el editor\",\"Color de fondo del editor de widgets como buscar/reemplazar\",\"Color de primer plano de los widgets del editor, como buscar y reemplazar.\",\"Color de borde de los widgets del editor. El color solo se usa si el widget elige tener un borde y no invalida el color.\",\"Color del borde de la barra de cambio de tamaño de los widgets del editor. El color se utiliza solo si el widget elige tener un borde de cambio de tamaño y si un widget no invalida el color.\",\"Color de fondo del texto de error del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de primer plano de squigglies de error en el editor.\",\"Si se establece, color de subrayados dobles para errores en el editor.\",\"Color de fondo del texto de advertencia del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de primer plano de squigglies de advertencia en el editor.\",\"Si se establece, color de subrayados dobles para advertencias en el editor.\",\"Color de fondo del texto de información del editor. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de primer plano de los subrayados ondulados informativos en el editor.\",\"Si se establece, color de subrayados dobles para informaciones en el editor.\",\"Color de primer plano de pista squigglies en el editor.\",\"Si se establece, color de subrayados dobles para sugerencias en el editor.\",\"Color de los vínculos activos.\",\"Color de la selección del editor.\",\"Color del texto seleccionado para alto contraste.\",\"Color de la selección en un editor inactivo. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Color en las regiones con el mismo contenido que la selección. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Color de borde de las regiones con el mismo contenido que la selección.\",\"Color de la coincidencia de búsqueda actual.\",\"Color de texto de la coincidencia de búsqueda actual.\",\"Color de los otros resultados de la búsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de primer plano de las otras coincidencias de búsqueda.\",\"Color de la gama que limita la búsqueda. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Color de borde de la coincidencia de búsqueda actual.\",\"Color de borde de otra búsqueda que coincide.\",\"Color del borde de la gama que limita la búsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Destacar debajo de la palabra para la que se muestra un mensaje al mantener el mouse. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Color de fondo al mantener el puntero en el editor.\",\"Color de primer plano al mantener el puntero en el editor.\",\"Color del borde al mantener el puntero en el editor.\",\"Color de fondo de la barra de estado al mantener el puntero en el editor.\",\"Color de primer plano de las sugerencias insertadas\",\"Color de fondo de las sugerencias insertadas\",\"Color de primer plano de las sugerencias insertadas para los tipos de letra\",\"Color de fondo de las sugerencias insertadas para los tipos de letra\",\"Color de primer plano de las sugerencias insertadas para los parámetros\",\"Color de fondo de las sugerencias insertadas para los parámetros\",\"El color utilizado para el icono de bombilla de acciones.\",\"El color utilizado para el icono de la bombilla de acciones de corrección automática.\",\"El color utilizado para el icono de bombilla de inteligencia artificial.\",\"Resaltado del color de fondo para una ficha de un fragmento de código.\",\"Resaltado del color del borde para una ficha de un fragmento de código.\",\"Resaltado del color de fondo para la última ficha de un fragmento de código.\",\"Resaltado del color del borde para la última tabulación de un fragmento de código.\",\"Color de fondo para el texto que se insertó. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de fondo para el texto que se eliminó. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Color de fondo de las líneas insertadas. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de fondo de las líneas que se quitaron. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color de fondo del margen donde se insertaron las líneas.\",\"Color de fondo del margen donde se quitaron las líneas.\",\"Primer plano de la regla de información general de diferencias para el contenido insertado.\",\"Primer plano de la regla de información general de diferencias para el contenido quitado.\",\"Color de contorno para el texto insertado.\",\"Color de contorno para el texto quitado.\",\"Color del borde entre ambos editores de texto.\",\"Color de relleno diagonal del editor de diferencias. El relleno diagonal se usa en las vistas de diferencias en paralelo.\",\"Color de fondo de los bloques sin modificar en el editor de diferencias.\",\"Color de primer plano de los bloques sin modificar en el editor de diferencias.\",\"Color de fondo del código sin modificar en el editor de diferencias.\",\"Color de sombra de los widgets dentro del editor, como buscar/reemplazar\",\"Color de borde de los widgets dentro del editor, como buscar/reemplazar\",\"El fondo de la barra de herramientas se perfila al pasar por encima de las acciones con el mouse.\",\"La barra de herramientas se perfila al pasar por encima de las acciones con el mouse.\",\"Fondo de la barra de herramientas al mantener el mouse sobre las acciones\",\"Color de los elementos de ruta de navegación que reciben el foco.\",\"Color de fondo de los elementos de ruta de navegación\",\"Color de los elementos de ruta de navegación que reciben el foco.\",\"Color de los elementos de ruta de navegación seleccionados.\",\"Color de fondo del selector de elementos de ruta de navegación.\",\"Fondo del encabezado actual en los conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Fondo de contenido actual en los conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Fondo de encabezado entrante en los conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Fondo de contenido entrante en los conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Fondo de cabecera de elemento antecesor común en conflictos de fusión en línea. El color no debe ser opaco para no ocultar decoraciones subyacentes.\",\"Fondo de contenido antecesor común en conflictos de combinación en línea. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color del borde en los encabezados y el divisor en conflictos de combinación alineados.\",\"Primer plano de la regla de visión general actual para conflictos de combinación alineados.\",\"Primer plano de regla de visión general de entrada para conflictos de combinación alineados.\",\"Primer plano de la regla de visión general de ancestros comunes para conflictos de combinación alineados.\",\"Color del marcador de regla general para buscar actualizaciones. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color del marcador de la regla general para los destacados de la selección. El color no debe ser opaco para no ocultar las decoraciones subyacentes.\",\"Color utilizado para el icono de error de problemas.\",\"Color utilizado para el icono de advertencia de problemas.\",\"Color utilizado para el icono de información de problemas.\",\"Fondo de cuadro de entrada.\",\"Primer plano de cuadro de entrada.\",\"Borde de cuadro de entrada.\",\"Color de borde de opciones activadas en campos de entrada.\",\"Color de fondo de las opciones activadas en los campos de entrada.\",\"Color de fondo al pasar por encima de las opciones en los campos de entrada.\",\"Color de primer plano de las opciones activadas en los campos de entrada.\",\"Color de primer plano para el marcador de posición de texto\",\"Color de fondo de validación de entrada para gravedad de información.\",\"Color de primer plano de validación de entrada para información de gravedad.\",\"Color de borde de validación de entrada para gravedad de información.\",\"Color de fondo de validación de entrada para gravedad de advertencia.\",\"Color de primer plano de validación de entrada para información de advertencia.\",\"Color de borde de validación de entrada para gravedad de advertencia.\",\"Color de fondo de validación de entrada para gravedad de error.\",\"Color de primer plano de validación de entrada para información de error.\",\"Color de borde de valdación de entrada para gravedad de error.\",\"Fondo de lista desplegable.\",\"Fondo de la lista desplegable.\",\"Primer plano de lista desplegable.\",\"Borde de lista desplegable.\",\"Color de primer plano del botón.\",\"Color del separador de botones.\",\"Color de fondo del botón.\",\"Color de fondo del botón al mantener el puntero.\",\"Color del borde del botón\",\"Color de primer plano del botón secundario.\",\"Color de fondo del botón secundario.\",\"Color de fondo del botón secundario al mantener el mouse.\",\"Color de primer plano de la opción de radio activa.\",\"Color de fondo de la opción de radio activa.\",\"Color de borde de la opción de radio activa.\",\"Color de primer plano de la opción de radio inactiva.\",\"Color de fondo de la opción de radio inactiva.\",\"Color de borde de la opción de radio inactiva.\",\"Color de fondo de la opción de radio activa inactiva al mantener el puntero.\",\"Color de fondo de la casilla de verificación del widget.\",\"Color de fondo del widget de la casilla cuando se selecciona el elemento en el que se encuentra.\",\"Color de primer plano del widget de la casilla de verificación.\",\"Color del borde del widget de la casilla de verificación.\",\"Color de borde del widget de la casilla cuando se selecciona el elemento en el que se encuentra.\",\"Color de fondo de etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un método abreviado de teclado.\",\"Color de primer plano de etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un método abreviado de teclado.\",\"Color del borde de la etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un método abreviado de teclado.\",\"Color del borde inferior de la etiqueta de enlace de teclado. La etiqueta enlace de teclado se usa para representar un método abreviado de teclado.\",\"Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\"Color de primer plano de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\"Color de contorno de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.\",\"Color de contorno de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos y seleccionados. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.\",\"Color de fondo de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\"Color de primer plano de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\"Color de primer plano del icono de lista o árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\"Color de fondo de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\"Color de primer plano de la lista o el árbol del elemento con el foco cuando la lista o el árbol esta inactiva. Una lista o un árbol tiene el foco del teclado cuando está activo, cuando esta inactiva no.\",\"Color de primer plano del icono de lista o árbol del elemento seleccionado cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.\",\"Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.\",\"Color de contorno de la lista o el árbol del elemento con el foco cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.\",\"Fondo de la lista o el árbol al mantener el mouse sobre los elementos.\",\"Color de primer plano de la lista o el árbol al pasar por encima de los elementos con el ratón.\",\"Fondo de lista/árbol al arrastrar y colocar cuando se mueven elementos sobre otros elementos al usar el mouse.\",\"Color del borde de lista o árbol al arrastrar y colocar cuando se mueven elementos entre otros elementos mediante el mouse.\",\"Color de primer plano de la lista o el árbol de las coincidencias resaltadas al buscar dentro de la lista o el ábol.\",\"Color de primer plano de la lista o árbol de los elementos coincidentes en los elementos enfocados activamente cuando se busca dentro de la lista o árbol.\",\"Color de primer plano de una lista o árbol para los elementos inválidos, por ejemplo una raiz sin resolver en el explorador.\",\"Color del primer plano de elementos de lista que contienen errores.\",\"Color del primer plano de elementos de lista que contienen advertencias.\",\"Color de fondo del widget de filtro de tipo en listas y árboles.\",\"Color de contorno del widget de filtro de tipo en listas y árboles.\",\"Color de contorno del widget de filtro de tipo en listas y árboles, cuando no hay coincidencias.\",\"Color de sombra del widget de filtrado de escritura en listas y árboles.\",\"Color de fondo de la coincidencia filtrada.\",\"Color de borde de la coincidencia filtrada.\",\"Color de primer plano de lista/árbol para los elementos no enfatizados.\",\"Color de trazo de árbol para las guías de sangría.\",\"Color de trazo de árbol para las guías de sangría que no están activas.\",\"Color de borde de la tabla entre columnas.\",\"Color de fondo para las filas de tabla impares.\",\"Color de fondo de la lista de acciones.\",\"Color de primer plano de la lista de acciones.\",\"Color de primer plano de la lista de acciones para el elemento con el foco.\",\"Color de fondo de la lista de acciones para el elemento con el foco.\",\"Color del borde de los menús.\",\"Color de primer plano de los elementos de menú.\",\"Color de fondo de los elementos de menú.\",\"Color de primer plano del menu para el elemento del menú seleccionado.\",\"Color de fondo del menu para el elemento del menú seleccionado.\",\"Color del borde del elemento seleccionado en los menús.\",\"Color del separador del menu para un elemento del menú.\",\"Color de marcador de minimapa para coincidencias de búsqueda.\",\"Color de marcador de minimapa para las selecciones del editor que se repiten.\",\"Color del marcador de minimapa para la selección del editor.\",\"Color del marcador de minimapa para información.\",\"Color del marcador de minimapa para advertencias.\",\"Color del marcador de minimapa para errores.\",\"Color de fondo del minimapa.\",\"Opacidad de los elementos de primer plano representados en el minimapa. Por ejemplo, \\\"#000000c0\\\" representará los elementos con 75% de opacidad.\",\"Color de fondo del deslizador del minimapa.\",\"Color de fondo del deslizador del minimapa al pasar el puntero.\",\"Color de fondo del deslizador de minimapa al hacer clic en él.\",\"Color de borde de los marcos activos.\",\"Color de fondo de la insignia. Las insignias son pequeñas etiquetas de información, por ejemplo los resultados de un número de resultados.\",\"Color de primer plano de la insignia. Las insignias son pequeñas etiquetas de información, por ejemplo los resultados de un número de resultados.\",\"Sombra de la barra de desplazamiento indica que la vista se ha despazado.\",\"Color de fondo de control deslizante de barra de desplazamiento.\",\"Color de fondo de barra de desplazamiento cursor cuando se pasar sobre el control.\",\"Color de fondo de la barra de desplazamiento al hacer clic.\",\"Color de fondo para la barra de progreso que se puede mostrar para las operaciones de larga duración.\",\"Color de fondo del selector rápido. El widget del selector rápido es el contenedor para selectores como la paleta de comandos.\",\"Color de primer plano del selector rápido. El widget del selector rápido es el contenedor para selectores como la paleta de comandos.\",\"Color de fondo del título del selector rápido. El widget del selector rápido es el contenedor para selectores como la paleta de comandos.\",\"Selector de color rápido para la agrupación de etiquetas.\",\"Selector de color rápido para la agrupación de bordes.\",\"Use quickInputList.focusBackground en su lugar.\",\"Selector rápido del color de primer plano para el elemento con el foco.\",\"Color de primer plano del icono del selector rápido para el elemento con el foco.\",\"Color de fondo del selector rápido para el elemento con el foco.\",\"Color del texto en el mensaje de finalización del viewlet de búsqueda.\",\"Color de las consultas coincidentes del Editor de búsqueda.\",\"Color de borde de las consultas coincidentes del Editor de búsqueda.\",\"Este color debe ser transparente u ocultará el contenido\",\"Use el color predeterminado.\",\"Identificador de la fuente que se va a usar. Si no se establece, se usa la fuente definida en primer lugar.\",\"Carácter de fuente asociado a la definición del icono.\",\"Icono de la acción de cierre en los widgets.\",\"Icono para ir a la ubicación del editor anterior.\",\"Icono para ir a la ubicación del editor siguiente.\",\"Se han cerrado los siguientes archivos y se han modificado en el disco: {0}.\",\"Los siguientes archivos se han modificado de forma incompatible: {0}.\",\"No se pudo deshacer \\\"{0}\\\" en todos los archivos. {1}\",\"No se pudo deshacer \\\"{0}\\\" en todos los archivos. {1}\",\"No se pudo deshacer \\\"{0}\\\" en todos los archivos porque se realizaron cambios en {1}\",\"No se pudo deshacer \\\"{0}\\\" en todos los archivos porque ya hay una operación de deshacer o rehacer en ejecución en {1}\",\"No se pudo deshacer \\\"{0}\\\" en todos los archivos porque se produjo una operación de deshacer o rehacer mientras tanto\",\"¿Desea deshacer \\\"{0}\\\" en todos los archivos?\",\"&&Deshacer en {0} archivos\",\"Deshacer este &&archivo\",\"No se pudo deshacer \\\"{0}\\\" porque ya hay una operación de deshacer o rehacer en ejecución.\",\"¿Quiere deshacer \\\"{0}\\\"?\",\"&&Sí\",\"No\",\"No se pudo rehacer \\\"{0}\\\" en todos los archivos. {1}\",\"No se pudo rehacer \\\"{0}\\\" en todos los archivos. {1}\",\"No se pudo volver a hacer \\\"{0}\\\" en todos los archivos porque se realizaron cambios en {1}\",\"No se pudo rehacer \\\"{0}\\\" en todos los archivos porque ya hay una operación de deshacer o rehacer en ejecución en {1}\",\"No se pudo rehacer \\\"{0}\\\" en todos los archivos porque se produjo una operación de deshacer o rehacer mientras tanto\",\"No se pudo rehacer \\\"{0}\\\" porque ya hay una operación de deshacer o rehacer en ejecución.\",\"Área de trabajo de código\"];\nglobalThis._VSCODE_NLS_LANGUAGE=\"es\";"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,WAAW,qBAAqB,CAAC,YAAY,UAAU,0CAAoC,0BAA0B,4BAAyB,UAAU,oBAAoB,kDAAkD,4JAA4J,aAAa,mBAAmB,sBAAmB,2BAA2B,2BAA2B,kBAAkB,cAAc,qBAAqB,qBAAkB,UAAU,0BAA0B,6BAA6B,4BAA4B,4BAA4B,SAAS,oBAAoB,gCAAgC,KAAK,aAAU,WAAW,0BAA0B,2FAAwF,2FAAwF,6BAA6B,2FAAwF,OAAO,WAAQ,MAAM,UAAU,OAAO,WAAQ,MAAM,QAAQ,UAAU,WAAQ,YAAS,UAAU,UAAU,WAAQ,MAAM,UAAU,UAAU,WAAQ,MAAM,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,oEAA8D,oEAA8D,gCAAgC,aAAa,WAAW,YAAY,UAAU,qBAAqB,mBAAmB,uDAAuD,cAAc,0RAAiR,2CAAwC,yCAAyC,gDAA6C,sEAAsE,wBAAwB,sBAAsB,4CAAyC,0CAA0C,yCAAyC,WAAW,kCAAkC,+BAA+B,8BAA8B,4DAA8D,0DAA4D,0DAA4D,SAAS,6FAA6F,+BAA4B,sBAAmB,0BAAuB,kFAA4E,WAAQ,+BAA4B,oDAA8C,gCAA6B,8BAA2B,iDAAiD,8BAA2B,4BAAyB,6BAA0B,2BAAwB,qCAAkC,iCAA8B,uBAAuB,0DAA0D,uCAAoC,kBAAkB,wBAAqB,wCAAwC,kCAA+B,wBAAqB,kDAA+C,gCAA6B,mDAAgD,wBAAqB,4BAA4B,sDAAgD,sDAAgD,yCAAmC,0CAAoC,qCAAqC,qBAAqB,yEAAsE,+EAA4E,+EAA4E,8EAAwE,gFAA0E,0DAA0D,8DAA8D,8DAA8D,8BAA8B,SAAS,6JAAiJ,wOAA+N,oIAAgI,gHAA0G,mDAAmD,gGAAgG,0CAA0C,8CAA8C,sEAAsE,qDAAqD,sIAAgI,8EAAwE,iFAA2E,8GAA0G,+EAA+E,iHAAmH,0FAAuF,+FAAyF,4FAAmF,kLAAmK,wMAA2L,6DAA0D,8CAA8C,4CAA4C,mIAA6H,8CAA8C,4CAA4C,yIAAmI,gGAA0F,wFAAwF,4FAA4F,oGAA2F,uHAAoH,yHAAmH,uHAAoH,0GAA0G,0CAA0C,0CAAoC,8DAAwD,6EAAiE,kDAA+C,kDAA+C,yEAAyE,uEAAiE,sFAA6E,wFAAkF,8FAA2F,+HAAyH,+GAAyG,0FAAuF,iDAAiD,yDAAyD,wHAAqH,gEAA6D,gJAAoI,sEAAgE,mFAAmF,+EAAyE,mIAA0H,8EAAwE,iHAAwG,+EAAyE,6DAAuD,4GAAmG,kGAAsF,iHAA2G,2MAAyL,0IAAiI,8MAAwM,+JAA8J,sOAAkO,qMAA+L,2KAAwK,2NAAwN,iDAA2C,4FAA6F,mHAAoH,oEAAiE,iEAA8D,8EAA2E,mMAAiM,4GAA2G,oHAAmH,6GAA4G,+GAAiH,0GAA4G,6HAAyH,sIAA+H,+HAAwH,kIAA2H,0HAAyH,qFAAkF,iIAA2H,qIAAkI,uIAAoI,kFAA+E,oOAAiO,yLAAmL,4KAAyK,oDAA2C,qGAAmF,6HAAqG,+DAAyD,sGAAmG,6EAA8D,sPAA0O,+GAA4G,4DAAyD,0DAAoD,wHAAqH,uHAAiH,6DAAuD,wNAA+M,iIAA2H,oFAAiF;AAAA;AAAA;AAAA,2EAAyR,sCAAsC,wDAAqD,8FAAwF,yHAAgH,wHAA+G,sCAAmC,kDAA+C,oEAAiE,0DAA0D,6FAAuF,iGAA8F,iGAA8F,+FAA4F,mFAA6E,qLAAyK,4FAAyF,kFAA4E,mIAA0H,8GAAwG,0EAAuE,6DAA0D,qDAA+C,kDAA+C,sDAAmD,0EAAuE,4YAA6X,6CAAuC,qEAA4D,kGAAsF,4DAAmD,8DAAqD,8FAAwF,kCAAkC,6MAA2L,mFAAgF,iEAA8D,gEAA6D,kEAAkE,qFAAkF,mEAAgE,kEAA+D,oEAAoE,gDAAgD,mDAAmD,mGAA6F,oHAA8G,oMAAwL,0FAA0F,8KAAwK,qGAAkG,iGAA8F,uDAAuD,sGAAsG,0FAAuF,+GAA+G,oHAAoH,uEAAuE,iFAA8E,oNAA2M,gEAAgE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,2IAAqI,oFAAoF,yCAAsC,8EAA2E,gDAA6C,uEAAiE,4FAAmF,uEAAoE,6DAA0D,oFAA8E,kEAAkE,iEAA2D,2CAAqC,4FAAmF,8CAAwC,uEAAiE,2EAA2E,wEAAwE,wKAAkK,8HAAqH,kFAA+E,+JAA2J,2FAAwF,sFAAmF,yGAAsG,mFAAmF,2SAAkS,8EAAwE,uDAAuD,gGAAgG,6FAA6F,mHAAmH,uGAA8F,wJAAsJ,gFAA+E,+EAA2E,qFAAoF,iEAAmE,6TAAmT,+EAA8E,kFAAiF,+EAA8E,gFAA+E,mFAAkF,gFAA+E,kFAAiF,+EAA8E,kFAAiF,8EAA6E,0EAAyE,kFAAiF,8EAA6E,oFAAmF,iFAAgF,0EAAyE,0EAAyE,8EAA6E,mFAAkF,qFAAoF,4EAA2E,uFAAsF,iFAAgF,2EAAwE,6EAA0E,oFAAoF,uFAA6F,2QAAqQ,2QAAqQ,wEAAkE,wFAAkF,8FAAwF,8FAAwF,qDAA+C,iKAAgK,oIAAiI,iGAA2F,sIAAgI,4DAA4D,iIAA8H,uFAAoF,6HAA6H,oOAA0N,6EAA+E,uKAAkK,uTAAqS,uBAAuB,qEAAqE,+GAAyG,2GAAqG,mIAA6H,6GAAuG,kHAA4G,uIAAiI,oGAAiG,mGAAmG,oFAAiF,kEAAkE,+GAAyG,2GAAqG,iIAA2H,8DAAqD,8DAAqD,sGAA6F,8KAA+J,4MAAgM,yIAAgI,4GAAsG,gDAAgD,gDAAgD,yGAAsG,qKAAsJ,0CAA0C,gDAAgD,iIAA0H,gHAA6G,wHAAwH,4FAA4F,mFAAmF,6FAA0F,2GAAqG,yGAAgG,yEAAyE,iDAA8C,kFAAsE,wIAA4H,sFAA0E,sEAAmE,uEAAoE,uMAAyL,8FAAgG,8CAAgD,wEAAqE,qFAAyF,0FAA0F,wDAAkD,wEAAkE,iDAA2C,yFAAsF,sEAAwE,kEAA+D,8HAAqH,yDAAsD,0DAA0D,2DAA2D,0FAAoF,uMAA8L,sHAAuG,kCAAkC,mLAAgL,oGAA2F,qIAAkI,8EAA2E,kDAA+C,gMAAuL,2EAAwE,uCAAoC,4GAA6G,oFAAoF,6FAA+F,8CAA8C,mEAAuE,iEAAkE,wSAA4R,oDAA8C,sCAAsC,4GAAyG,4FAAsF,+BAA+B,sDAAsD,oFAAiF,0EAA0E,uFAAoF,gDAA0C,8CAA2C,yFAAmF,wFAAqF,2FAAqF,wEAAqE,yDAA2D,gEAAgE,wGAA4F,4CAAyC,+EAAyE,oHAA8G,gGAAgG,gFAAgF,gEAAgE,2FAA2F,0EAAuE,gHAA0G,wFAA4E,gMAA6L,iEAAiE,qFAAkF,4CAA4C,iFAA8E,mFAAgF,0EAAuE,wDAAqD,6CAA6C,kFAA+E,kFAA+E,wEAAqE,qDAAkD,kGAA4F,+DAAyD,yJAAsJ,qGAAkG,+HAAyH,yGAAsG,6CAA6C,0KAA2K,4IAAgJ,iFAA8E,sHAA6G,kDAA+C,0JAAiJ,kCAA+B,2EAAqE,wDAAqD,wEAAkE,kFAA4E,wFAAqF,oDAAiD,4JAAyJ,+FAA4F,6HAA0H,0CAAoC,8DAAwD,wEAAoE,kIAA2H,kDAA4C,uGAA6G,8HAA2H,yGAAgG,0EAAoE,kFAA4E,oKAA8J,mEAAmE,kLAAsK,oEAAiE,+BAA+B,yHAAsH,mFAAmF,6KAA0K,oFAAoF,8KAA2K,2DAA2D,8CAAwC,kDAA4C,qGAAsG,0DAAoD,iHAAkH,sDAAgD,sDAAgD,sDAAgD,sDAAgD,sDAAgD,sDAAgD,6DAAuD,6DAAuD,6DAAuD,6DAAuD,6DAAuD,6DAAuD,sDAAgD,sEAAsE,sDAAgD,mGAAgG,iCAAiC,sDAAmD,6CAA6C,8CAA8C,oDAAiD,mEAAgE,sHAA6G,2EAAwE,sSAA4R,mDAAmD,yDAAyD,kDAAkD,4IAA4I,sEAAmE,0EAAuE,oFAAiF,8GAA2G,8GAA2G,8GAA2G,8GAA2G,8GAA2G,8GAA2G,kDAAkD,qHAA+G,qHAA+G,qHAA+G,qHAA+G,qHAA+G,qHAA+G,qHAA+G,mHAA6G,qHAA+G,mHAA6G,mHAA6G,mHAA6G,yDAAyD,yDAAyD,4DAA4D,+GAAyG,sFAAsF,kCAAkC,6CAA6C,wDAAwD,KAAK,sFAA6E,4CAA4C,+EAAyE,uDAAoD,mGAAgG,sCAAmC,6EAA0E,6EAA0E,6BAA6B,+BAA+B,+CAAiD,wCAAwC,wCAAwC,wDAAoD,wDAAwD,4CAA4C,mDAAgD,kDAA+C,0DAAuD,2DAAwD,+EAA4E,qCAAqC,kEAA+D,2DAAwD,8CAA8C,kDAAkD,mDAAmD,uDAAoD,0DAA0D,6EAA6E,6DAA6D,+DAA4D,gDAAgD,sDAAsD,wDAAwD,4DAA4D,2DAA2D,4EAAyE,kEAAkE,oFAAiF,SAAS,WAAW,QAAQ,YAAY,cAAc,iBAAc,+BAA4B,SAAS,QAAQ,UAAU,aAAU,WAAW,QAAQ,YAAS,YAAS,qBAAqB,OAAO,YAAS,SAAS,WAAW,UAAU,YAAY,SAAS,aAAa,uBAAoB,WAAW,YAAY,oBAAoB,cAAc,qCAAqC,6BAA0B,oDAAiD,qBAAqB,8BAA8B,qBAAkB,sCAAgC,uBAAuB,kCAAkC,2CAA2C,uBAAoB,iBAAiB,iCAA8B,qCAAqC,+CAA4C,uCAAoC,mDAAmD,6CAA0C,qDAAqD,iBAAiB,4BAA4B,mBAAmB,mBAAmB,mFAAgF,6CAA6C,2CAA2C,oBAAoB,WAAW,SAAS,SAAS,SAAS,WAAW,SAAS,SAAS,SAAS,UAAU,QAAQ,QAAQ,QAAQ,mCAAmC,cAAc,cAAc,YAAY,YAAY,4EAAsE,0DAAoD,wDAAqD,8DAAwD,sEAAgE,iDAA8C,2EAAwE,0BAAoB,2CAAwC,iEAAgE,+DAA2D,sDAAmD,2CAAwC,kBAAkB,2DAA6D,gDAAkD,yDAAsD,uCAAuC,mCAA6B,8DAAgE,sEAAkE,oEAA8D,wCAAwC,0BAA0B,gDAA6C,gBAAgB,sDAAgD,iCAA8B,sCAAsC,mHAAuG,qKAAgJ,oLAAiL,kDAA+C,wBAAwB,mCAAmC,qBAAkB,0BAAoB,UAAU,YAAY,aAAa,QAAQ,gBAAgB,sBAAmB,6GAAuG,0JAA8I,sJAA6I,oLAAqK,oLAAqK,gBAAgB,oFAA2E,sCAAmC,gCAA6B,iEAA2D,yBAAyB,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,kCAA+B,oCAAiC,iCAA8B,gCAA6B,gCAAgC,kCAAkC,WAAW,yBAAyB,qBAAkB,eAAe,UAAU,UAAU,qBAAqB,iBAAiB,UAAU,wCAAqC,kBAAkB,iBAAiB;AAAA,oLAA8O,gBAAgB,mBAAmB,oCAAoC,gCAAgC,mDAAmD,gEAA6D,uFAAoF,8BAA2B,qCAAqC,6BAA6B,gBAAgB,eAAe,2BAA2B,0BAA0B,qCAAqC,mCAAmC,gBAAgB,KAAK,2CAAwC,uCAAoC,sEAAmE;AAAA,KAA+C;AAAA,KAA4C,mHAAkH,oFAAiF,SAAS,WAAW,6BAA0B,0BAAuB,mBAAmB,kBAAkB,uBAAuB,kDAAkD,gFAA0E,qCAAkC,qCAAkC,gCAA6B,+BAA4B,aAAa,eAAe,mFAA0E,gFAA0E,8EAA0E,kEAAiE,uEAAsE,uEAAsE,wEAAuE,sBAAsB,SAAS,SAAS,wBAAwB,yBAAyB,yBAAsB,SAAS,aAAa,aAAa,aAAa,kBAAkB,sBAAsB,2HAAqH,aAAa,oBAAoB,mBAAmB,4BAA8B,oCAAsC,4BAA8B,oLAAiL,YAAY,+BAA+B,SAAS,mBAAmB,4BAA4B,6BAA6B,yCAAyC,4BAA4B,+BAA+B,yCAAyC,4CAA4C,cAAc,iBAAiB,yBAAyB,kCAAkC,mCAAmC,qDAAkD,oCAAoC,2BAA2B,4HAAyH,sFAA6E,wDAAwD,+DAA+D,kEAA+D,mFAAgF,gFAAgF,oCAAoC,wCAAwC,6CAA0C,8CAA2C,6CAA0C,2BAA2B,gCAA6B,gEAA6D,uCAAuC,+DAA4D,sCAAsC,4EAAyE,uBAAuB,2EAAwE,sBAAsB,QAAQ,cAAc,iBAAc,aAAa,eAAe,uBAAuB,sBAAsB,6EAA0E,qFAAkF,kFAA+E,6FAA0F,uEAAoE,6FAAuF,8DAA2D,MAAM,eAAe,qDAAiD,0CAAoC,uBAAoB,gBAAgB,qDAA+C,2CAAqC,wBAAqB,qDAA+C,2CAAqC,uBAAuB,6DAAyD,kDAA4C,kCAA+B,mBAAmB,yDAAqD,8CAAwC,0BAA0B,qDAAuD,gCAAgC,qBAAqB,cAAc,cAAc,cAAc,+BAAiC,cAAc,qBAAkB,oCAAiC,iCAA8B,qBAAkB,6BAA0B,gCAA6B,qCAAkC,wBAAwB,gCAAgC,mBAAmB,2BAA2B,4BAAyB,2CAA2C,qIAA6H,cAAc,YAAY,kBAAkB,iBAAiB,cAAc,6BAA6B,oBAAoB,cAAc,8CAA2C,kDAA+C,mDAAgD,sDAAmD,+BAA+B,kCAA4B,qCAA+B,8CAAwC,2FAAwF,+CAA4C,wBAAqB,mEAAmE,oDAAoD,6CAA6C,kGAAyF,0GAAiG,6FAAuF,2EAAwE,gDAAgD,+CAA+C,kDAAkD,gDAAgD,2CAAwC,0CAAuC,yBAAyB,gDAAgD,+KAAsK,sFAAmF,qEAAkE,oEAAiE,uEAAoE,qEAAkE,2EAAwE,4EAAyE,8DAA8D,8EAA8E,kEAAkE,iEAAiE,cAAc,uJAAmJ,uJAAmJ,yDAAyD,mDAAmD,0DAA0D,oDAAoD,kBAAkB,6CAA0C,sCAAmC,6CAA0C,0BAAoB,mCAAgC,uCAAoC,yCAAmC,4CAAsC,oCAA8B,gEAA0D,sCAAmC,kCAA+B,yDAAgD,oCAAiC,0CAAoC,wDAAkD,wDAAkD,uDAAoD,oCAAiC,+BAA4B,+DAAyD,wCAAqC,0DAAoD,wEAAkE,gCAAgC,aAAa,cAAc,mBAAgB,aAAa,iGAAqF,2BAAwB,mBAAmB,wCAAwC,uCAAuC,oCAAoC,uDAAuD,kBAAkB,wDAAqD,mBAAgB,+BAA+B,UAAU,+BAA+B,2CAA2C,6CAA0C,8DAA8D,wHAAkH,gEAAgE,gDAAgD,cAAc,8DAA2D,6DAA0D,YAAY,WAAW,YAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,mCAAmC,oCAAoC,oCAA8B,yBAAsB,2BAAwB,wBAAqB,0BAAuB,yBAAsB,0BAAuB,8BAA2B,0BAAuB,6BAA0B,yBAAsB,wCAAqC,yCAAsC,gCAA6B,yBAAyB,oBAAiB,yBAAmB,gCAA0B,2BAAwB,2BAAwB,+BAA+B,4CAAyC,iBAAc,6CAA6C,8BAA2B,8BAA2B,4BAA4B,4BAA4B,oDAA8C,6BAA6B,4BAA4B,+BAA4B,mFAAgF,4EAAyE,4DAAyD,mBAAmB,oBAAiB,aAAa,cAAc,mBAAgB,aAAa,0BAA0B,mBAAgB,+DAA+D,uBAAuB,0BAA0B,wBAAwB,0BAA0B,wBAAwB,yBAAyB,2CAAqC,4CAAyC,yCAAsC,yCAAsC,sEAAgE,oCAAiC,qEAA+D,mCAAgC,8EAAqE,6EAAoE,oEAAiE,uCAAuC,gCAAgC,8BAA8B,6BAA6B,0BAA0B,4BAA4B,yCAAsC,8DAA2D,6DAA0D,kBAAkB,8EAA2E,kFAAyE,SAAS,wEAA+D,mDAA6C,yEAAgE,gEAA6D,sEAAmE,iFAA8E,oFAAiF,iGAA8F,wGAAqG,uDAAoD,oEAAiE,uFAAoF,qGAAkG,mFAAgF,mEAAmE,4EAAyE,0DAAuD,+CAAyC,wBAAqB,4GAAgG,2FAA+E,6FAAoF,0EAAoE,sDAAgD,iCAA8B,sBAAsB,6BAA6B,oBAAiB,oBAAoB,mBAAgB,kBAAkB,sBAAsB,kBAAkB,eAAe,oBAAoB,gBAAgB,mBAAmB,mBAAmB,4BAA4B,iBAAiB,8BAA2B,mBAAgB,oBAAoB,sBAAsB,mCAAgC,gBAAgB,iBAAiB,iBAAiB,mBAAgB,kBAAkB,gBAAgB,eAAe,eAAe,mBAAmB,mDAAmD,qDAAkD,8BAA2B,uEAAoE,uCAAuC,iCAAiC,+DAA+D,gEAA6D,8DAA2D,mCAAgC,0FAA0F,sDAAsD,kDAAkD,sEAAmE,+DAA4D,gEAAgE,sDAAsD,4FAA4F,sCAAsC,WAAW,wBAAqB,0BAAuB,0BAAuB,yBAAsB,yEAAmE,kGAAyF,0FAAiF,6CAA0C,UAAU,QAAQ,SAAS,eAAY,SAAS,UAAU,YAAS,MAAM,MAAM,MAAM,SAAM,MAAM,MAAM,SAAM,QAAQ,UAAU,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,SAAS,aAAa,UAAU,YAAY,YAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,kDAAkD,4BAA4B,8BAA8B,sCAAsC,4CAA4C,yIAAsI,uDAAuD,2EAAqE,+DAAyD,wDAAkD,wBAAwB,qCAAqC,+DAA4D,gDAAgD,iGAA8F,yDAAyD,sFAAmF,uEAAuE,6EAAuE,0DAA4D,2BAA2B,WAAW,WAAW,aAAa,aAAa,WAAW,gBAAgB,iBAAc,kDAA+C,sCAAsC,sCAAsC,6CAA6C,8EAA8E,0FAA0F,iEAAiE,wDAAwD,oGAAoG,wDAAwD,cAAc,sBAAsB,UAAU,eAAe,UAAU,WAAW,uBAAuB,SAAS,cAAc,wEAAkE,cAAW,sJAA6I,sJAA6I,qJAA4I,qJAA4I,uJAA8I,2JAAkJ,0JAAiJ,sKAA6J,sJAA6I,qJAA4I,uJAA8I,uJAA8I,0JAA8I,wJAA+I,sJAA6I,6JAAoJ,yJAA6I,yJAA6I,kKAAyJ,kJAAyI,2JAA+I,sJAA6I,2JAAkJ,uJAA8I,yJAAgJ,0JAAiJ,sKAA0J,sJAA6I,0JAAiJ,qJAA4I,sKAA0J,sJAA6I,sJAA6I,qFAA+E,+EAAmE,qEAA+D,8PAAsO,qDAAkD,kFAAkF,wEAAqE,6DAA6D,+DAA+D,2CAA2C,wHAAsG,kHAAgG,mCAAgC,2DAAkD,8BAA2B,wCAAwC,sDAAsD,oCAAoC,kDAAkD,iCAAiC,mDAAmD,mCAAmC,qDAAqD,yCAAyC,8DAA2D,mCAAgC,wDAAqD,+BAA+B,gEAA+D,qCAAkC,sDAAmD;AAAA;AAAA,0GAA0Q,8CAA2C,SAAS,sKAAmK,0KAAuK,iJAA2I,yGAAsG,+GAA4G,gEAA0D,2IAAwI,qKAAkK,gLAAuK,uCAAoC,sCAAmC,0CAAuC,mBAAmB,0BAAuB,QAAQ,6BAA0B,cAAc,uBAAoB,uBAAoB,6BAA0B,6BAA0B,iCAA2B,eAAY,0CAAoC,2BAAwB,sCAAmC,uCAAiC,0BAAoB,oDAAiD,2BAAwB,uDAAiD,wCAAqC,oBAAoB,oBAAoB,oBAAoB,oBAAoB,gCAAgC,oBAAoB,+BAA+B,mBAAmB,sBAAsB,sBAAsB,qCAAqC,qCAAqC,sCAAsC,sCAAsC,mCAAgC,mCAAgC,oCAAiC,oCAAiC,oCAAiC,6BAA6B,WAAW,WAAW,SAAS,SAAS,UAAU,UAAU,UAAU,UAAU,+BAA4B,+BAA4B,MAAM,QAAQ,SAAS,UAAU,eAAe,gBAAgB,YAAY,YAAY;AAAA,WAAmB,aAAa,YAAY,UAAU,sBAAmB,gBAAkB,kCAAkC,2CAA2C,mBAAmB,yCAAsC,sBAAmB,mFAAgF,iDAA8C,iCAA8B,mCAAgC,oCAAiC,oCAAiC,4CAAyC,+DAA4D,kEAA+D,sFAAgF,kEAA4D,sFAAgF,kEAA4D,gDAA6C,oNAA4M,qEAAoE,wFAAuF,mEAAgE,6CAAuC,8IAAyI,4BAAyB,8BAA2B,mBAAmB,iDAA8C,mCAAgC,+CAA4C;AAAA,kBAAoC,mCAAmC,mCAAmC,qCAAqC,wCAAwC,6EAA6E,iCAAiC,iDAA8C,6BAA6B,gEAA6D,uBAAoB,6BAA0B,kCAA+B,0CAAuC,gJAA4I,+CAA4C,wDAAqD,0DAA0D,2DAAwD,2DAAwD,qBAAkB,mEAAuE,iEAAkE,iWAAsU,uNAA2M,6LAAiL,4FAAsF,oDAA2C,mEAA0D,0EAAuE,4GAA6G,sEAAwE,0GAAoG,8BAA8B,oGAA2F,mJAAgJ,4LAAgL,4IAAmI,2IAAkI,0FAA8F,4CAA4C,yCAAyC,sGAAgG,wMAA4L,iFAA2E,+GAAsG,gPAAwO,QAAQ,cAAc,iBAAc,sBAAsB,qBAAqB,uBAAuB,iBAAiB,qBAAqB,WAAW,4CAA8C,WAAW,yEAAmE,sDAAmD,8EAAwE,WAAQ,oEAAwE,UAAU,0CAA0C,iNAAqM,sKAA0J,gKAAoJ,0CAA0C,iBAAiB,oBAAoB,UAAU,gBAAgB,iBAAc,WAAQ,oBAAiB,8CAAgD,yFAAyF,iHAAiH,oHAAoH,2HAAwH,oEAAiE,oGAAoG,6GAA0G,qHAAkH,iLAA2K,0DAAuD,+FAA+F,uCAAuC,wEAAwE,6DAA6D,4CAA4C,4CAA4C,4DAAyD,uDAAoD,uEAAiE,+DAA4D,+DAA4D,mEAAgE,kEAA+D,gEAA6D,qEAA+D,6BAA6B,mDAAmD,4DAA4D,iFAAiF,4DAA4D,8DAA8D,8DAA8D,6EAA6E,2HAA2H,uMAAiM,yHAAyH,6DAA6D,yEAAyE,+HAA+H,mEAAmE,8EAA8E,kIAA+H,+EAA+E,+EAA+E,0DAA0D,6EAA6E,oCAAiC,uCAAoC,oDAAoD,uHAAoH,yIAAsI,6EAA0E,kDAA+C,2DAAwD,4HAAyH,mEAAgE,mHAAgH,2DAAwD,mDAAgD,iIAA8H,6JAA6J,sDAAsD,6DAA6D,uDAAuD,4EAA4E,sDAAsD,+CAA+C,8EAA8E,uEAAuE,6EAA0E,sEAAmE,4DAA4D,8FAAwF,2EAA2E,4EAAyE,6EAA0E,qFAA+E,8FAAqF,2HAAwH,uHAAoH,uHAAoH,4HAAyH,+DAA4D,6DAA0D,iGAA8F,+FAA4F,6CAA6C,2CAA2C,iDAAiD,4HAA4H,2EAA2E,kFAAkF,0EAAuE,2EAA2E,0EAA0E,oGAAoG,wFAAwF,4EAA4E,uEAAoE,2DAAwD,uEAAoE,iEAA8D,qEAAkE,wJAAkJ,sJAAgJ,yJAAmJ,wJAAkJ,gKAAuJ,8JAAqJ,6FAA0F,oGAA8F,qGAA+F,kHAA4G,4IAA4I,0JAAuJ,uDAAuD,6DAA6D,gEAA6D,8BAA8B,qCAAqC,8BAA8B,6DAA6D,qEAAqE,+EAA+E,4EAA4E,iEAA8D,8EAAwE,qFAA+E,8EAAwE,2EAAwE,wFAAkF,2EAAwE,qEAAkE,kFAA4E,oEAAiE,8BAA8B,iCAAiC,qCAAqC,8BAA8B,sCAAmC,kCAAkC,+BAA4B,sDAAmD,+BAA4B,iDAA8C,0CAAuC,+DAA4D,yDAAsD,kDAA+C,kDAA+C,2DAAwD,oDAAiD,oDAAiD,kFAA+E,8DAA2D,mGAAmG,qEAAkE,+DAA4D,mGAAmG,4IAAyI,mJAAgJ,gJAA6I,yJAAsJ,8NAA4M,qOAAmN,sOAAoN,sPAAoO,+NAA6M,sOAAoN,0OAAwN,iOAA+M,0NAA8M,4OAA0N,qOAAmN,wOAAsN,4EAAyE,wGAAkG,oHAAiH,iIAA8H,6HAAuH,mKAA6J,qIAA+H,sEAAsE,2EAA2E,sEAAmE,yEAAsE,sGAAmG,8EAA2E,8CAA8C,8CAA8C,6EAA0E,8DAAqD,sFAA0E,6CAA6C,kDAAkD,0CAA0C,iDAAiD,8EAA8E,uEAAuE,mCAAgC,qDAAkD,8CAA2C,4EAAyE,qEAAkE,6DAA0D,6DAA0D,mEAAgE,gFAAgF,kEAA+D,sDAAmD,oDAAoD,+CAA+C,+BAA+B,sJAAqJ,8CAA8C,kEAAkE,oEAAiE,wCAAwC,sJAA6I,6JAAoJ,4EAA4E,mEAAmE,qFAAqF,8DAA8D,2GAAwG,uIAAiI,8IAAwI,qJAA4I,kEAA4D,+DAAyD,kDAAkD,6EAA0E,uFAAoF,sEAAmE,+EAAyE,iEAA8D,0EAAuE,8DAA2D,+BAA+B,8GAA8G,+DAAyD,kDAA+C,uDAAoD,wDAAqD,+EAA+E,wEAAwE,uDAAyD,uDAAyD,sFAAwF,8HAA0H,0HAAyH,kDAAiD,6BAA6B,0BAA0B,kGAA8F,6BAA4B,UAAO,KAAK,sDAAwD,sDAAwD,4FAA8F,6HAAyH,yHAAwH,iGAA6F,iCAA2B,EACxzgH,WAAW,qBAAqB", "names": [], "file": "nls.messages.es.js"}