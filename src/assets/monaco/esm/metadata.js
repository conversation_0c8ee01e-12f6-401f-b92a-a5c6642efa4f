
exports.features = [
  {
    "label": "anchorSelect",
    "entry": "vs/editor/contrib/anchorSelect/browser/anchorSelect"
  },
  {
    "label": "bracketMatching",
    "entry": "vs/editor/contrib/bracketMatching/browser/bracketMatching"
  },
  {
    "label": "browser",
    "entry": "vs/editor/browser/coreCommands"
  },
  {
    "label": "caretOperations",
    "entry": [
      "vs/editor/contrib/caretOperations/browser/caretOperations",
      "vs/editor/contrib/caretOperations/browser/transpose"
    ]
  },
  {
    "label": "clipboard",
    "entry": "vs/editor/contrib/clipboard/browser/clipboard"
  },
  {
    "label": "codeAction",
    "entry": "vs/editor/contrib/codeAction/browser/codeActionContributions"
  },
  {
    "label": "codeEditor",
    "entry": "vs/editor/browser/widget/codeEditor/codeEditorWidget"
  },
  {
    "label": "codelens",
    "entry": "vs/editor/contrib/codelens/browser/codelensController"
  },
  {
    "label": "colorPicker",
    "entry": [
      "vs/editor/contrib/colorPicker/browser/colorContributions",
      "vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions"
    ]
  },
  {
    "label": "comment",
    "entry": "vs/editor/contrib/comment/browser/comment"
  },
  {
    "label": "contextmenu",
    "entry": "vs/editor/contrib/contextmenu/browser/contextmenu"
  },
  {
    "label": "cursorUndo",
    "entry": "vs/editor/contrib/cursorUndo/browser/cursorUndo"
  },
  {
    "label": "diffEditor",
    "entry": "vs/editor/browser/widget/diffEditor/diffEditor.contribution"
  },
  {
    "label": "diffEditorBreadcrumbs",
    "entry": "vs/editor/contrib/diffEditorBreadcrumbs/browser/contribution"
  },
  {
    "label": "dnd",
    "entry": "vs/editor/contrib/dnd/browser/dnd"
  },
  {
    "label": "documentSymbols",
    "entry": "vs/editor/contrib/documentSymbols/browser/documentSymbols"
  },
  {
    "label": "dropOrPasteInto",
    "entry": [
      "vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution",
      "vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution"
    ]
  },
  {
    "label": "find",
    "entry": "vs/editor/contrib/find/browser/findController"
  },
  {
    "label": "folding",
    "entry": "vs/editor/contrib/folding/browser/folding"
  },
  {
    "label": "fontZoom",
    "entry": "vs/editor/contrib/fontZoom/browser/fontZoom"
  },
  {
    "label": "format",
    "entry": "vs/editor/contrib/format/browser/formatActions"
  },
  {
    "label": "gotoError",
    "entry": "vs/editor/contrib/gotoError/browser/gotoError"
  },
  {
    "label": "gotoLine",
    "entry": "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess"
  },
  {
    "label": "gotoSymbol",
    "entry": [
      "vs/editor/contrib/gotoSymbol/browser/goToCommands",
      "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition"
    ]
  },
  {
    "label": "hover",
    "entry": "vs/editor/contrib/hover/browser/hoverContribution"
  },
  {
    "label": "iPadShowKeyboard",
    "entry": "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard"
  },
  {
    "label": "inPlaceReplace",
    "entry": "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace"
  },
  {
    "label": "indentation",
    "entry": "vs/editor/contrib/indentation/browser/indentation"
  },
  {
    "label": "inlayHints",
    "entry": "vs/editor/contrib/inlayHints/browser/inlayHintsContribution"
  },
  {
    "label": "inlineCompletions",
    "entry": "vs/editor/contrib/inlineCompletions/browser/inlineCompletions.contribution"
  },
  {
    "label": "inlineEdit",
    "entry": "vs/editor/contrib/inlineEdit/browser/inlineEdit.contribution"
  },
  {
    "label": "inlineEdits",
    "entry": "vs/editor/contrib/inlineEdits/browser/inlineEdits.contribution"
  },
  {
    "label": "inlineProgress",
    "entry": "vs/editor/contrib/inlineProgress/browser/inlineProgress"
  },
  {
    "label": "inspectTokens",
    "entry": "vs/editor/standalone/browser/inspectTokens/inspectTokens"
  },
  {
    "label": "lineSelection",
    "entry": "vs/editor/contrib/lineSelection/browser/lineSelection"
  },
  {
    "label": "linesOperations",
    "entry": "vs/editor/contrib/linesOperations/browser/linesOperations"
  },
  {
    "label": "linkedEditing",
    "entry": "vs/editor/contrib/linkedEditing/browser/linkedEditing"
  },
  {
    "label": "links",
    "entry": "vs/editor/contrib/links/browser/links"
  },
  {
    "label": "longLinesHelper",
    "entry": "vs/editor/contrib/longLinesHelper/browser/longLinesHelper"
  },
  {
    "label": "multicursor",
    "entry": "vs/editor/contrib/multicursor/browser/multicursor"
  },
  {
    "label": "parameterHints",
    "entry": "vs/editor/contrib/parameterHints/browser/parameterHints"
  },
  {
    "label": "placeholderText",
    "entry": "vs/editor/contrib/placeholderText/browser/placeholderText.contribution"
  },
  {
    "label": "quickCommand",
    "entry": "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess"
  },
  {
    "label": "quickHelp",
    "entry": "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess"
  },
  {
    "label": "quickOutline",
    "entry": "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess"
  },
  {
    "label": "readOnlyMessage",
    "entry": "vs/editor/contrib/readOnlyMessage/browser/contribution"
  },
  {
    "label": "referenceSearch",
    "entry": "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch"
  },
  {
    "label": "rename",
    "entry": "vs/editor/contrib/rename/browser/rename"
  },
  {
    "label": "sectionHeaders",
    "entry": "vs/editor/contrib/sectionHeaders/browser/sectionHeaders"
  },
  {
    "label": "semanticTokens",
    "entry": [
      "vs/editor/contrib/semanticTokens/browser/documentSemanticTokens",
      "vs/editor/contrib/semanticTokens/browser/viewportSemanticTokens"
    ]
  },
  {
    "label": "smartSelect",
    "entry": "vs/editor/contrib/smartSelect/browser/smartSelect"
  },
  {
    "label": "snippet",
    "entry": "vs/editor/contrib/snippet/browser/snippetController2"
  },
  {
    "label": "stickyScroll",
    "entry": "vs/editor/contrib/stickyScroll/browser/stickyScrollContribution"
  },
  {
    "label": "suggest",
    "entry": [
      "vs/editor/contrib/suggest/browser/suggestController",
      "vs/editor/contrib/suggest/browser/suggestInlineCompletions"
    ]
  },
  {
    "label": "toggleHighContrast",
    "entry": "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast"
  },
  {
    "label": "toggleTabFocusMode",
    "entry": "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode"
  },
  {
    "label": "tokenization",
    "entry": "vs/editor/contrib/tokenization/browser/tokenization"
  },
  {
    "label": "unicodeHighlighter",
    "entry": "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter"
  },
  {
    "label": "unusualLineTerminators",
    "entry": "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators"
  },
  {
    "label": "wordHighlighter",
    "entry": "vs/editor/contrib/wordHighlighter/browser/wordHighlighter"
  },
  {
    "label": "wordOperations",
    "entry": "vs/editor/contrib/wordOperations/browser/wordOperations"
  },
  {
    "label": "wordPartOperations",
    "entry": "vs/editor/contrib/wordPartOperations/browser/wordPartOperations"
  }
];
exports.languages = [
  {
    "label": "abap",
    "entry": "vs/basic-languages/abap/abap.contribution"
  },
  {
    "label": "apex",
    "entry": "vs/basic-languages/apex/apex.contribution"
  },
  {
    "label": "azcli",
    "entry": "vs/basic-languages/azcli/azcli.contribution"
  },
  {
    "label": "bat",
    "entry": "vs/basic-languages/bat/bat.contribution"
  },
  {
    "label": "bicep",
    "entry": "vs/basic-languages/bicep/bicep.contribution"
  },
  {
    "label": "cameligo",
    "entry": "vs/basic-languages/cameligo/cameligo.contribution"
  },
  {
    "label": "clojure",
    "entry": "vs/basic-languages/clojure/clojure.contribution"
  },
  {
    "label": "coffee",
    "entry": "vs/basic-languages/coffee/coffee.contribution"
  },
  {
    "label": "cpp",
    "entry": "vs/basic-languages/cpp/cpp.contribution"
  },
  {
    "label": "csharp",
    "entry": "vs/basic-languages/csharp/csharp.contribution"
  },
  {
    "label": "csp",
    "entry": "vs/basic-languages/csp/csp.contribution"
  },
  {
    "label": "css",
    "entry": [
      "vs/basic-languages/css/css.contribution",
      "vs/language/css/monaco.contribution"
    ],
    "worker": {
      "id": "vs/language/css/cssWorker",
      "entry": "vs/language/css/css.worker"
    }
  },
  {
    "label": "cypher",
    "entry": "vs/basic-languages/cypher/cypher.contribution"
  },
  {
    "label": "dart",
    "entry": "vs/basic-languages/dart/dart.contribution"
  },
  {
    "label": "dockerfile",
    "entry": "vs/basic-languages/dockerfile/dockerfile.contribution"
  },
  {
    "label": "ecl",
    "entry": "vs/basic-languages/ecl/ecl.contribution"
  },
  {
    "label": "elixir",
    "entry": "vs/basic-languages/elixir/elixir.contribution"
  },
  {
    "label": "flow9",
    "entry": "vs/basic-languages/flow9/flow9.contribution"
  },
  {
    "label": "freemarker2",
    "entry": "vs/basic-languages/freemarker2/freemarker2.contribution"
  },
  {
    "label": "fsharp",
    "entry": "vs/basic-languages/fsharp/fsharp.contribution"
  },
  {
    "label": "go",
    "entry": "vs/basic-languages/go/go.contribution"
  },
  {
    "label": "graphql",
    "entry": "vs/basic-languages/graphql/graphql.contribution"
  },
  {
    "label": "handlebars",
    "entry": "vs/basic-languages/handlebars/handlebars.contribution"
  },
  {
    "label": "hcl",
    "entry": "vs/basic-languages/hcl/hcl.contribution"
  },
  {
    "label": "html",
    "entry": [
      "vs/basic-languages/html/html.contribution",
      "vs/language/html/monaco.contribution"
    ],
    "worker": {
      "id": "vs/language/html/htmlWorker",
      "entry": "vs/language/html/html.worker"
    }
  },
  {
    "label": "ini",
    "entry": "vs/basic-languages/ini/ini.contribution"
  },
  {
    "label": "java",
    "entry": "vs/basic-languages/java/java.contribution"
  },
  {
    "label": "javascript",
    "entry": "vs/basic-languages/javascript/javascript.contribution"
  },
  {
    "label": "json",
    "entry": "vs/language/json/monaco.contribution",
    "worker": {
      "id": "vs/language/json/jsonWorker",
      "entry": "vs/language/json/json.worker"
    }
  },
  {
    "label": "julia",
    "entry": "vs/basic-languages/julia/julia.contribution"
  },
  {
    "label": "kotlin",
    "entry": "vs/basic-languages/kotlin/kotlin.contribution"
  },
  {
    "label": "less",
    "entry": "vs/basic-languages/less/less.contribution"
  },
  {
    "label": "lexon",
    "entry": "vs/basic-languages/lexon/lexon.contribution"
  },
  {
    "label": "liquid",
    "entry": "vs/basic-languages/liquid/liquid.contribution"
  },
  {
    "label": "lua",
    "entry": "vs/basic-languages/lua/lua.contribution"
  },
  {
    "label": "m3",
    "entry": "vs/basic-languages/m3/m3.contribution"
  },
  {
    "label": "markdown",
    "entry": "vs/basic-languages/markdown/markdown.contribution"
  },
  {
    "label": "mdx",
    "entry": "vs/basic-languages/mdx/mdx.contribution"
  },
  {
    "label": "mips",
    "entry": "vs/basic-languages/mips/mips.contribution"
  },
  {
    "label": "msdax",
    "entry": "vs/basic-languages/msdax/msdax.contribution"
  },
  {
    "label": "mysql",
    "entry": "vs/basic-languages/mysql/mysql.contribution"
  },
  {
    "label": "objective-c",
    "entry": "vs/basic-languages/objective-c/objective-c.contribution"
  },
  {
    "label": "pascal",
    "entry": "vs/basic-languages/pascal/pascal.contribution"
  },
  {
    "label": "pascaligo",
    "entry": "vs/basic-languages/pascaligo/pascaligo.contribution"
  },
  {
    "label": "perl",
    "entry": "vs/basic-languages/perl/perl.contribution"
  },
  {
    "label": "pgsql",
    "entry": "vs/basic-languages/pgsql/pgsql.contribution"
  },
  {
    "label": "php",
    "entry": "vs/basic-languages/php/php.contribution"
  },
  {
    "label": "pla",
    "entry": "vs/basic-languages/pla/pla.contribution"
  },
  {
    "label": "postiats",
    "entry": "vs/basic-languages/postiats/postiats.contribution"
  },
  {
    "label": "powerquery",
    "entry": "vs/basic-languages/powerquery/powerquery.contribution"
  },
  {
    "label": "powershell",
    "entry": "vs/basic-languages/powershell/powershell.contribution"
  },
  {
    "label": "protobuf",
    "entry": "vs/basic-languages/protobuf/protobuf.contribution"
  },
  {
    "label": "pug",
    "entry": "vs/basic-languages/pug/pug.contribution"
  },
  {
    "label": "python",
    "entry": "vs/basic-languages/python/python.contribution"
  },
  {
    "label": "qsharp",
    "entry": "vs/basic-languages/qsharp/qsharp.contribution"
  },
  {
    "label": "r",
    "entry": "vs/basic-languages/r/r.contribution"
  },
  {
    "label": "razor",
    "entry": "vs/basic-languages/razor/razor.contribution"
  },
  {
    "label": "redis",
    "entry": "vs/basic-languages/redis/redis.contribution"
  },
  {
    "label": "redshift",
    "entry": "vs/basic-languages/redshift/redshift.contribution"
  },
  {
    "label": "restructuredtext",
    "entry": "vs/basic-languages/restructuredtext/restructuredtext.contribution"
  },
  {
    "label": "ruby",
    "entry": "vs/basic-languages/ruby/ruby.contribution"
  },
  {
    "label": "rust",
    "entry": "vs/basic-languages/rust/rust.contribution"
  },
  {
    "label": "sb",
    "entry": "vs/basic-languages/sb/sb.contribution"
  },
  {
    "label": "scala",
    "entry": "vs/basic-languages/scala/scala.contribution"
  },
  {
    "label": "scheme",
    "entry": "vs/basic-languages/scheme/scheme.contribution"
  },
  {
    "label": "scss",
    "entry": "vs/basic-languages/scss/scss.contribution"
  },
  {
    "label": "shell",
    "entry": "vs/basic-languages/shell/shell.contribution"
  },
  {
    "label": "solidity",
    "entry": "vs/basic-languages/solidity/solidity.contribution"
  },
  {
    "label": "sophia",
    "entry": "vs/basic-languages/sophia/sophia.contribution"
  },
  {
    "label": "sparql",
    "entry": "vs/basic-languages/sparql/sparql.contribution"
  },
  {
    "label": "sql",
    "entry": "vs/basic-languages/sql/sql.contribution"
  },
  {
    "label": "st",
    "entry": "vs/basic-languages/st/st.contribution"
  },
  {
    "label": "swift",
    "entry": "vs/basic-languages/swift/swift.contribution"
  },
  {
    "label": "systemverilog",
    "entry": "vs/basic-languages/systemverilog/systemverilog.contribution"
  },
  {
    "label": "tcl",
    "entry": "vs/basic-languages/tcl/tcl.contribution"
  },
  {
    "label": "twig",
    "entry": "vs/basic-languages/twig/twig.contribution"
  },
  {
    "label": "typescript",
    "entry": [
      "vs/basic-languages/typescript/typescript.contribution",
      "vs/language/typescript/monaco.contribution"
    ],
    "worker": {
      "id": "vs/language/typescript/tsWorker",
      "entry": "vs/language/typescript/ts.worker"
    }
  },
  {
    "label": "typespec",
    "entry": "vs/basic-languages/typespec/typespec.contribution"
  },
  {
    "label": "vb",
    "entry": "vs/basic-languages/vb/vb.contribution"
  },
  {
    "label": "wgsl",
    "entry": "vs/basic-languages/wgsl/wgsl.contribution"
  },
  {
    "label": "xml",
    "entry": "vs/basic-languages/xml/xml.contribution"
  },
  {
    "label": "yaml",
    "entry": "vs/basic-languages/yaml/yaml.contribution"
  }
];
