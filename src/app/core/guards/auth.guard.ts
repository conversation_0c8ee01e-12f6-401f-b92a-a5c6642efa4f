import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router } from '@angular/router';
import { APP_ROUTES, AUTH_PATH, TRIGGER_KEYS } from '@core/constants';
import { IAiSelect, ICurrentAi } from '@core/models';
import { AgentIntroService, AuthService, TriggerService } from '@core/services';
import { AiStore, SocketStore, UserAiStore } from '@core/stores';
import { AiService, SocketService } from '@shared/services';
import { CommonUtils } from '@shared/utils';
import { of, switchMap, tap } from 'rxjs';

export const authGuard: CanActivateFn = (_: ActivatedRouteSnapshot, __) => {
  const router = inject(Router);
  const authService = inject(AuthService);
  const aiService = inject(AiService);
  const aiStore = inject(AiStore);
  const userAiStore = inject(UserAiStore);
  const triggerService = inject(TriggerService);
  const socketStore = inject(SocketStore);
  const socketService = inject(SocketService);
  const agentIntroService = inject(AgentIntroService);

  const storage = CommonUtils.isRemember() ? localStorage : sessionStorage;
  const currentAiId =
    userAiStore.currentAiId() || storage.getItem('current-ai-id') || '';

  return authService.check().pipe(
    tap((isAuthenticated) => {
      if (!isAuthenticated) {
        void router.navigate([`${APP_ROUTES.AUTH}/${AUTH_PATH.LOGIN}`]);
      }
    }),
    switchMap((isAuthenticated) => {
      if (!isAuthenticated) return of(false);

      return aiService.getListAis().pipe(
        tap((ais: ICurrentAi[]) => {
          aiStore.setAis(ais);
          aiStore.setAisFilter(ais);
        }),
        switchMap((ais: ICurrentAi[]) => {
          const defaultAiId = ais.find((ai) => ai.default)?.id;
          const selectedAiId = currentAiId || defaultAiId || ais[0]?.id || '';
          return aiService.selectAi(selectedAiId).pipe(
            switchMap((ai: IAiSelect) => {
              userAiStore.setCurrentAi(ai?.current_ai);
              storage.setItem('current-ai-id', ai.current_ai.id);

              socketStore.setSocketCsConnected(null);
              socketStore.setSocketMessageConnected(null);
              socketService.setupSocketConnectionCs(ai.current_ai.id, true);
              socketService.setupSocketConnectionMessage(
                ai.current_ai.id,
                true
              );
              socketStore.setHasCallStartFlow(false);
              triggerService.trigger(
                TRIGGER_KEYS.RE_INIT_PREVIEW,
                triggerService.get(TRIGGER_KEYS.RE_INIT_PREVIEW)() === null
                  ? 1
                  : triggerService.get(TRIGGER_KEYS.RE_INIT_PREVIEW)() + 1
              );

              agentIntroService.shouldShowAgentIntro().subscribe(shouldShow => {
                if (shouldShow) {
                  setTimeout(() => {
                    agentIntroService.showAgentIntroDialog();
                  }, 1000);
                }
              });

              return of(true);
            })
          );
        })
      );
    })
  );
};
