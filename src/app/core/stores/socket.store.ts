import { patchState, signalStore, withMethods, withState } from '@ngrx/signals';
import { ISocketStore } from './models';

const initialState: ISocketStore = {
  socketCsConnected: null,
  socketMessageConnected: null,
  hasCallStartFlow: false,
};

export const SocketStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withMethods((store) => ({
    setSocketCsConnected(socketCsConnected: string | null): void {
      patchState(store, (state) => ({ ...state, socketCsConnected }));
    },
    setSocketMessageConnected(socketMessageConnected: string | null): void {
      patchState(store, (state) => ({ ...state, socketMessageConnected }));
    },
    setHasCallStartFlow(hasCallStartFlow: boolean): void {
      patchState(store, (state) => ({ ...state, hasCallStartFlow }));
    },
  }))
);
