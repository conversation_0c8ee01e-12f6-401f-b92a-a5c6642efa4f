import { IAccount, ICurrentAi } from '@core/models';
import { patchState, signalStore, withMethods, withState } from '@ngrx/signals';
import { IUserAiStore } from './models';

const initialState: IUserAiStore = {
  currentAiId: null,
  currentUser: null,
  currentAi: null,
};

export const UserAiStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withMethods((store) => ({
    setCurrentAiId(aiId: string | null): void {
      patchState(store, (state) => ({ ...state, currentAiId: aiId }));
    },
    setCurrentUser(user: IAccount | null): void {
      patchState(store, (state) => ({ ...state, currentUser: user }));
    },
    setCurrentAi(ai: ICurrentAi | null): void {
      patchState(store, (state) => ({ ...state, currentAi: ai }));
    },
  }))
);
