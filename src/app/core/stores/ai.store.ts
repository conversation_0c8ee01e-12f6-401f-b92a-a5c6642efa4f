import { ICurrentAi } from '@core/models';
import { patchState, signalStore, withMethods, withState } from '@ngrx/signals';
import { IAiStore } from './models';

const initialState: IAiStore = {
  ais: [],
  aisFilter: [],
};

export const AiStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withMethods((store) => ({
    setAis(ais: Array<Partial<ICurrentAi>>): void {
      patchState(store, (state) => ({ ...state, ais: ais }));
    },
    setAisFilter(ais: Array<Partial<ICurrentAi>>): void {
      patchState(store, (state) => ({ ...state, aisFilter: ais }));
    },
  }))
);
