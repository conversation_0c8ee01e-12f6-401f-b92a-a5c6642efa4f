import { inject, Injectable } from '@angular/core';
import { UserAiStore } from '@core/stores';
import { DxDialog } from '@dx-ui/ui';
import { AgentIntroDialogComponent } from '@shared/components';
import { AgentDevService, SettingsService } from '@shared/services';
import { CommonUtils } from '@shared/utils';
import { map, Observable, switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AgentIntroService {
  private dialogService = inject(DxDialog);
  private agentDevService = inject(AgentDevService);
  private settingsService = inject(SettingsService);
  private userAiStore = inject(UserAiStore);

  private readonly AGENT_INTRO_SHOWN_KEY_PREFIX = 'agent-intro-shown';

  shouldShowAgentIntro(): Observable<boolean> {
    if (this.hasIntroBeenShown()) {
      return new Observable<boolean>((subscriber) => {
        subscriber.next(false);
        subscriber.complete();
      });
    }

    return this.settingsService.getDetailSetting().pipe(
      switchMap((settings) => {
        const agentModeEnabled = Boolean(
          settings.settings?.basic?.use_agent?.enabled
        );

        if (agentModeEnabled) {
          return new Observable<boolean>((subscriber) => {
            subscriber.next(false);
            subscriber.complete();
          });
        }

        return this.agentDevService.getAll().pipe(
          map((agents) => agents.filter((agent) => !agent.is_default)),
          map((agents) => agents.length === 0)
        );
      })
    );
  }

  shouldShowAiAgentCreation(): Observable<boolean> {
    return this.settingsService.getDetailSetting().pipe(
      switchMap((settings) => {
        const agentModeEnabled = Boolean(
          settings.settings?.basic?.use_agent?.enabled
        );

        if (agentModeEnabled) {
          return new Observable<boolean>((subscriber) => {
            subscriber.next(true);
            subscriber.complete();
          });
        }

        return this.agentDevService.getAll().pipe(
          map((agents) => agents.filter((agent) => !agent.is_default)),
          map((agents) => agents.length === 0)
        );
      })
    );
  }

  showAgentIntroDialog(): void {
    this.dialogService.open(AgentIntroDialogComponent, {
      width: '26vw',
      minWidth: '340px',
    });
    this.markIntroAsShown();
  }

  private getAgentIntroKey(): string {
    const currentAiId = this.userAiStore.currentAiId();
    return `${this.AGENT_INTRO_SHOWN_KEY_PREFIX}-${currentAiId}`;
  }

  private hasIntroBeenShown(): boolean {
    const agentIntroKey = this.getAgentIntroKey();
    return (
      (CommonUtils.isRemember()
        ? localStorage.getItem(agentIntroKey)
        : sessionStorage.getItem(agentIntroKey)) === 'true'
    );
  }

  private markIntroAsShown(): void {
    const agentIntroKey = this.getAgentIntroKey();
    CommonUtils.isRemember()
      ? localStorage.setItem(agentIntroKey, 'true')
      : sessionStorage.setItem(agentIntroKey, 'true');
  }
}
