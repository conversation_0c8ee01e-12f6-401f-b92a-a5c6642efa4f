import { Injectable, Renderer2, RendererFactory2, inject, DestroyRef, signal, effect } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Injectable({
  providedIn: 'root'
})
export class ImageZoomService {
  private renderer = inject(RendererFactory2).createRenderer(null, null);
  private destroyRef = inject(DestroyRef);
  
  // Signals for reactive state management
  private isZoomed = signal(false);
  private currentImageSrc = signal<string | null>(null);
  private currentAltText = signal<string | null>(null);
  
  private overlay!: HTMLElement;
  private zoomedImageContainer!: HTMLElement;
  private zoomedImage!: HTMLElement;
  private altTextElement!: HTMLElement;
  private container!: HTMLElement;
  
  private mutationObserver?: MutationObserver;
  private readonly processedImages = new WeakSet<Element>();

  constructor() {
    this.initZoomElements();
    this.setupReactiveEffects();
  }

  private setupReactiveEffects() {
    // React to zoom state changes
    effect(() => {
      const zoomed = this.isZoomed();
      this.renderer.setStyle(this.container, 'display', zoomed ? 'flex' : 'none');
    });

    // React to image source changes
    effect(() => {
      const src = this.currentImageSrc();
      if (src) {
        this.renderer.setAttribute(this.zoomedImage, 'src', src);
      }
    });

    // React to alt text changes
    effect(() => {
      const altText = this.currentAltText();
      if (altText) {
        this.renderer.setProperty(this.altTextElement, 'innerText', altText);
        this.renderer.setStyle(this.altTextElement, 'display', 'block');
      } else {
        this.renderer.setStyle(this.altTextElement, 'display', 'none');
      }
    });
  }

  private initZoomElements() {
    // Create container element
    this.container = this.renderer.createElement('div');
    this.renderer.addClass(this.container, 'image-container');

    // Create overlay element
    this.overlay = this.renderer.createElement('div');
    this.renderer.addClass(this.overlay, 'overlay');
    this.renderer.listen(this.overlay, 'click', () => this.closeZoom());

    // Create zoomed image container element
    this.zoomedImageContainer = this.renderer.createElement('div');
    this.renderer.addClass(this.zoomedImageContainer, 'zoomed-image-container');

    // Create zoomed image element
    this.zoomedImage = this.renderer.createElement('img');
    this.renderer.addClass(this.zoomedImage, 'zoomed-image');

    // Create alt text element
    this.altTextElement = this.renderer.createElement('div');
    this.renderer.addClass(this.altTextElement, 'alt-text');

    // Append elements
    this.renderer.appendChild(this.zoomedImageContainer, this.zoomedImage);
    this.renderer.appendChild(this.zoomedImageContainer, this.altTextElement);
    this.renderer.appendChild(this.overlay, this.zoomedImageContainer);
    this.renderer.appendChild(this.container, this.overlay);
    this.renderer.appendChild(document.body, this.container);

    // Add CSS custom properties for better theming
    this.addCustomStyles();
  }

  private addCustomStyles() {
    const style = this.renderer.createElement('style');
    this.renderer.setProperty(style, 'textContent', `
      .image-container {
        --zoom-overlay-bg: rgba(0, 0, 0, 0.9);
        --zoom-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        --zoom-border-radius: 8px;
        --zoom-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
      
      .overlay {
        background: var(--zoom-overlay-bg);
        transition: var(--zoom-transition);
        backdrop-filter: blur(4px);
      }
      
      .zoomed-image {
        border-radius: var(--zoom-border-radius);
        box-shadow: var(--zoom-shadow);
        transition: var(--zoom-transition);
        outline: none;
      }
      
      .alt-text {
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 12px;
        border-radius: var(--zoom-border-radius);
        font-size: 14px;
        margin-top: 12px;
      }
    `);
    this.renderer.appendChild(document.head, style);
  }

  init() {
    // Use MutationObserver for better performance instead of setInterval
    this.setupMutationObserver();
    
    // Initial scan for existing images
    this.addZoomListeners();
  }

  private setupMutationObserver() {
    this.mutationObserver = new MutationObserver((mutations) => {
      let shouldScan = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.matches('img.zoom') || element.querySelector('img.zoom')) {
                shouldScan = true;
              }
            }
          });
        }
      });
      
      if (shouldScan) {
        this.addZoomListeners();
      }
    });

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Cleanup on destroy
    this.destroyRef.onDestroy(() => {
      this.mutationObserver?.disconnect();
    });
  }

  private addZoomListeners() {
    document.querySelectorAll('img.zoom').forEach(image => {
      if (!this.processedImages.has(image)) {
        this.renderer.listen(image, 'click', (event) => this.zoomImage(event));
        this.processedImages.add(image);
      }
    });
  }

  private zoomImage(event: Event) {
    const target = event.target as HTMLImageElement;
    event.stopPropagation();
    
    // Update signals
    this.currentImageSrc.set(target.src);
    this.currentAltText.set(target.alt || null);
    this.isZoomed.set(true);
  }

  private closeZoom() {
    this.isZoomed.set(false);
    this.currentImageSrc.set(null);
    this.currentAltText.set(null);
  }

  // Public API methods
  public getZoomState() {
    return this.isZoomed.asReadonly();
  }

  public getCurrentImage() {
    return this.currentImageSrc.asReadonly();
  }
}
