<!-- Component Container with Positioning Context -->
<div class="build-basic-flow-container">
  <div [id]="rootId"></div>

  <!-- Debug Panel Overlay -->
  <div
    *ngIf="debugPanelVisible()"
    class="debug-panel-overlay"
  >
    <div class="debug-panel-header">
      <h3>Debug Panel</h3>
      <div style="display: flex; gap: 8px; align-items: center;">
        <button
          class="debug-panel-refresh"
          (click)="clearDebugData()"
          title="Clear all debug entries"
        >
          <app-svg-icon type="icRefresh"></app-svg-icon>
        </button>
        <button
          class="debug-panel-close"
          (click)="closeDebugPanel()"
        >
          <app-svg-icon type="icClose"></app-svg-icon>
        </button>
      </div>
    </div>
    <div class="debug-panel-content">
      <div class="debug-header">
        <h4>Basic Flow Debug Log</h4>
        <span class="debug-count">{{ debugData().length }} entries</span>
      </div>

      <div class="debug-list">
        <div
          *ngFor="let item of debugData()"
          class="debug-item"
          [class.expanded]="item.expanded"
        >
          <div class="debug-item-header" (click)="toggleDebugItem(item.id)">
            <div class="debug-item-info">
              <span class="debug-time">{{ item.time }}</span>
              <span class="debug-title">{{ item.title }}</span>
            </div>
            <button class="debug-expand-btn" [class.expanded]="item.expanded">
              <span class="expand-icon">▼</span>
            </button>
          </div>

          <div class="debug-item-content" *ngIf="item.expanded">
            <div class="debug-secondary-fields">
              <div class="debug-field">
                <span class="debug-field-label">Conversation ID:</span>
                <span class="debug-field-value">{{ formatDebugValue(item.conversation_id) }}</span>
              </div>
              <div class="debug-field">
                <span class="debug-field-label">Node ID:</span>
                <span class="debug-field-value">{{ formatDebugValue(item.node_id) }}</span>
              </div>
              <div class="debug-field">
                <span class="debug-field-label">Edge:</span>
                <div class="debug-field-value">
                  <div *ngIf="item.parsedEdge; else rawEdge" class="edge-formatted">
                    <div class="edge-connection">
                      <div class="edge-primary">
                        <span class="edge-label">Connection:</span>
                        <span class="edge-value">{{ item.parsedEdge.source }} → {{ item.parsedEdge.target }}</span>
                      </div>
                      <div class="edge-primary">
                        <span class="edge-label">Type:</span>
                        <span class="edge-value edge-type">{{ item.parsedEdge.type }}</span>
                      </div>
                    </div>

                    <div class="edge-handles" *ngIf="item.parsedEdge.sourceHandle || item.parsedEdge.targetHandle">
                      <div class="edge-secondary" *ngIf="item.parsedEdge.sourceHandle">
                        <span class="edge-label">Source Handle:</span>
                        <span class="edge-value">{{ item.parsedEdge.sourceHandle }}</span>
                      </div>
                      <div class="edge-secondary" *ngIf="item.parsedEdge.targetHandle">
                        <span class="edge-label">Target Handle:</span>
                        <span class="edge-value">{{ item.parsedEdge.targetHandle }}</span>
                      </div>
                    </div>

                    <div class="edge-properties" *ngIf="item.parsedEdge.animated !== undefined || item.parsedEdge.style">
                      <div class="edge-secondary" *ngIf="item.parsedEdge.animated !== undefined">
                        <span class="edge-label">Animated:</span>
                        <span class="edge-value">{{ item.parsedEdge.animated ? 'Yes' : 'No' }}</span>
                      </div>
                      <div class="edge-secondary" *ngIf="item.parsedEdge.style">
                        <span class="edge-label">Style:</span>
                        <span class="edge-value">{{ formatStyleObject(item.parsedEdge.style) }}</span>
                      </div>
                    </div>

                    <div class="edge-data" *ngIf="item.parsedEdge.data">
                      <div class="edge-secondary">
                        <span class="edge-label">Data:</span>
                        <span class="edge-value">{{ item.parsedEdge.data | json }}</span>
                      </div>
                    </div>
                  </div>
                  <ng-template #rawEdge>
                    <span class="edge-raw">{{ formatDebugValue(item.edge) }}</span>
                  </ng-template>
                </div>
              </div>
              <div class="debug-field">
                <span class="debug-field-label">Is Prompt:</span>
                <span class="debug-field-value">{{ item.is_prompt ? 'Yes' : 'No' }}</span>
              </div>
              <div class="debug-field">
                <span class="debug-field-label">Output Key:</span>
                <span class="debug-field-value">{{ item.output_key }}</span>
              </div>
              <div class="debug-field">
                <span class="debug-field-label">Result:</span>
                <span class="debug-field-value">{{ item.result }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
