import {
  AfterViewInit,
  Component,
  effect,
  ElementRef,
  inject,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
  APP_ROUTES,
  STUDIO_PATH,
  STUDIO_STATUS,
  TRIGGER_KEYS,
} from '@core/constants';
import { TriggerService } from '@core/services';
import { StudioStore, UIStore } from '@core/stores';
import { DxDialog } from '@dx-ui/ui';
import { IFlowDev } from '@shared/models';
import { DebugMessageService, DebugMessage } from '@shared/services';
import { SvgIconComponent } from '@shared/components/svg-icon/svg-icon.component';
import EditorBasicFlowView from '@views/flow-editor-v1/views/EditorBasicFlowView';
import { PublishFlowDialogComponent } from '@views/studio/components/publish-flow-dialog/publish-flow-dialog.component';
import * as React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { Subject, Subscription } from 'rxjs';
import { v4 as uuid } from 'uuid';

// Interface for parsed edge data
interface ParsedEdgeData {
  source: string;
  target: string;
  type: string;
  sourceHandle?: string;
  targetHandle?: string;
  id?: string;
  data?: any;
  animated?: boolean;
  style?: any;
}

// Interface for UI debug item
interface DebugItem {
  id: string;
  time: string;
  title: string;
  expanded: boolean;
  // Secondary fields (expandable)
  conversation_id: string;
  node_id: string;
  edge: string | null;
  parsedEdge: ParsedEdgeData | null;
  is_prompt: boolean;
  output_key: string;
  result: string;
}

@Component({
  selector: 'app-build-basic-flow',
  standalone: true,
  imports: [CommonModule, SvgIconComponent],
  templateUrl: './build-basic-flow.component.html',
  styleUrl: './build-basic-flow.component.css',
})
export class BuildBasicFlowComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  flowId = input.required<number>();

  rootId: string = 'flow-canvas';
  root: Root | undefined;
  protected destroy$ = new Subject<void>();

  // Debug panel state (separate from existing debug mode)
  debugPanelVisible = signal(false);

  // Real-time WebSocket debug data
  debugData = signal<DebugItem[]>([]);

  // Conversation ID for message filtering
  private UUID: string = uuid();

  // Service subscription for debug messages
  private debugMessageSubscription: Subscription = new Subscription();

  private messageListener!: (event: MessageEvent) => void;

  private router: Router = inject(Router);
  private el: ElementRef = inject(ElementRef);
  private dialog = inject(DxDialog);
  private studioStore = inject(StudioStore);
  private uiStore = inject(UIStore);
  private triggerService = inject(TriggerService);
  private debugMessageService = inject(DebugMessageService);

  private previousStatus: string | null = this.studioStore.status();

  private studioStoreEffect = effect(() => {
    const currentStatus = this.studioStore.status();
    if (currentStatus !== this.previousStatus) {
      this.previousStatus = currentStatus;
      void this.router.navigate(['/studio/build']);
    }
  });

  private themeEffect = effect(() => {
    const currentTheme = this.uiStore.theme();
    postMessage({
      type: 'theme',
      data: { theme: currentTheme },
    });
  });

  ngOnInit(): void {
    this.messageListener = (event) => {
      if (event && event?.data && event?.data.type === 'publish_flow') {
        this.publishFlow(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'revert_flow') {
        this.revertFlow(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'debug_mode') {
        this.studioStore.setFlowDebugMode(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'toggle_debug_panel') {
        this.debugPanelVisible.set(event.data.data);
      }
      if (
        event &&
        event?.data &&
        event?.data.type === 'back_to_list_basic_flow'
      ) {
        void this.router.navigate(
          [
            `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW}`,
          ],
          {
            queryParams: {
              key_word: '',
              trigger_type: event?.data.data.type,
            },
          }
        );
      }
    };
    window.addEventListener('message', this.messageListener);

    // Subscribe to debug messages from the service
    this.setupDebugMessageSubscription();

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'false');
  }

  ngAfterViewInit(): void {
    this.render();
    postMessage({
      type: 'studio_status',
      data: { status: this.studioStore.status() },
    });
    // Send conversation_id to React component for WebSocket communication
    postMessage({
      type: 'conversation_id',
      data: this.UUID
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up debug message subscription
    this.debugMessageSubscription.unsubscribe();

    if (this.root) {
      this.root.unmount();
      this.root = undefined;
    }

    // Reset debug mode when component is destroyed
    this.studioStore.setFlowDebugMode(false);

    // Reset debug panel state
    this.debugPanelVisible.set(false);

    // Remove message listener
    window.removeEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'true');
  }

  private publishFlow(flow: IFlowDev) {
    if (!flow) return;
    if (this.studioStore.status() === STUDIO_STATUS.LIVE) return;
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      this.dialog
        .open(PublishFlowDialogComponent, {
          width: '40dvw',
          minWidth: '340px',
          data: {
            flow_id: flow.id,
            trigger_type: flow.trigger_type,
            isMultiple: false,
          },
        })
        .afterClosed()
        .subscribe((result) => {
          void this.router.navigateByUrl(
            `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW}`
          );
          if (result) {
            setTimeout(() => {
              this.studioStore.setStudioStatus(STUDIO_STATUS.LIVE);
            }, 200);
          }
        });
    }
  }

  private revertFlow(flow: IFlowDev) {
    // Implementation for revert flow functionality
    console.log('Revert flow:', flow);
  }

  closeDebugPanel(): void {
    console.log('Closing debug panel');
    this.debugPanelVisible.set(false);
    // Notify React component that debug panel was closed
    window.postMessage({
      type: 'debug_panel_closed',
      data: false
    }, '*');
  }

  clearDebugData(): void {
    console.log('Clearing debug data');
    this.debugData.set([]);
  }

  toggleDebugItem(itemId: string): void {
    this.debugData.update(items =>
      items.map(item =>
        item.id === itemId
          ? { ...item, expanded: !item.expanded }
          : item
      )
    );
  }

  // Helper method to format null/empty values for display
  formatDebugValue(value: string | null | undefined): string {
    if (value === null || value === undefined || value === '') {
      return 'N/A';
    }
    return value;
  }

  // Helper method to parse edge JSON data
  parseEdgeData(edgeString: string | null): ParsedEdgeData | null {
    if (!edgeString || edgeString === 'null' || edgeString === 'undefined') {
      return null;
    }

    try {
      const parsed = JSON.parse(edgeString);
      return {
        source: parsed.source || 'N/A',
        target: parsed.target || 'N/A',
        type: parsed.type || 'N/A',
        sourceHandle: parsed.sourceHandle,
        targetHandle: parsed.targetHandle,
        id: parsed.id,
        data: parsed.data,
        animated: parsed.animated,
        style: parsed.style
      };
    } catch (error) {
      console.warn('Failed to parse edge data:', edgeString, error);
      return null;
    }
  }

  // Helper method to format style object for display
  formatStyleObject(style: any): string {
    if (!style || typeof style !== 'object') {
      return 'N/A';
    }

    const styleEntries = Object.entries(style)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${key}: ${value}`);

    return styleEntries.length > 0 ? styleEntries.join(', ') : 'N/A';
  }

  // Helper method to normalize null values (convert string "null" to actual null)
  private normalizeNullValue(value: string | null | undefined): string | null {
    if (value === null || value === undefined || value === '' || value === 'null' || value === 'undefined') {
      return null;
    }
    return value;
  }

  private setupDebugMessageSubscription(): void {
    // Subscribe to debug messages from the service
    this.debugMessageSubscription = this.debugMessageService.debugMessages$.subscribe({
      next: (message) => {
        console.log('📨 Received debug message from service:', message);
        this.handleDebugMessage(message);
      },
      error: (error) => {
        console.error('❌ Error receiving debug message from service:', error);
      }
    });
  }

  private handleDebugMessage(message: DebugMessage): void {
    try {
      const normalizedEdge = this.normalizeNullValue(message.edge);
      const debugItem: DebugItem = {
        id: `${message.node_id}_${Date.now()}`, // Create unique ID
        time: message.start_time || new Date().toISOString(),
        title: message.name || 'Unknown',
        expanded: false,
        // Secondary fields
        conversation_id: message.conversation_id,
        node_id: message.node_id,
        edge: normalizedEdge,
        parsedEdge: this.parseEdgeData(normalizedEdge),
        is_prompt: Boolean(message.is_prompt),
        output_key: this.normalizeNullValue(message.output_key) || '',
        result: this.normalizeNullValue(message.result) || ''
      };
      // Add new debug message to the beginning of the array
      this.debugData.update(items => {
        const newItems = [debugItem, ...items];
        return newItems;
      });
    } catch (error) {
      console.error('❌ Error handling debug message:', error, message);
    }
  }

  private render() {
    if (this.flowId()) {
      const reactRoot = this.el.nativeElement.querySelector(`#${this.rootId}`);
      if (reactRoot) {
        if (this.root) {
          this.root.unmount();
        }

        this.root = createRoot(reactRoot);
        this.root.render(
          React.createElement(EditorBasicFlowView, { flowId: this.flowId() })
        );

        postMessage({
          type: 'theme',
          data: { theme: this.uiStore.theme() },
        });
      }
    }
  }
}
