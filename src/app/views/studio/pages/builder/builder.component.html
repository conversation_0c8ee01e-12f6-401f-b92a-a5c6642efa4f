@if (!isLoading()) {
<div class="flex flex-col h-full">
  @if (!shouldHideHeader()) {
  <div class="flex items-center justify-between pb-2 text-base-content dark:text-dark-base-content"  [bdcWalkTriggerFor]="flow_guided">
    <h1
      class="text-[28px] font-bold"
    >
      Flow Builder
      <a
        href="https://docs.dxconnect.lifesup.ai/Diving%20Deeper/Studio/builder/"
        target="_blank"
        class="text-[15px] ml-2 font-medium underline cursor-pointer hover:opacity-80 hover:italic"
      >
        <app-svg-icon
          type="icHelp"
          class="w-5 h-5"
        ></app-svg-icon>
      </a>
    </h1>

    <div class="flex items-center space-x-4" [bdcWalkTriggerFor]="agent_flow">
      <span
        class="text-[15px] text-neutral-content dark:text-dark-neutral-content"
        >Mode:</span
      >
      <div
        class="p-1 rounded-xl cursor-pointer bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
        (click)="toggleMode()"
      >
        <div
          class="flex flex-row overflow-hidden text-neutral-content dark:text-dark-neutral-content bg-transparent"
        >
          <div
            class="flex items-center justify-center space-x-2 rounded-lg px-4 py-1 min-w-[100px]"
            [ngClass]="{
              'bg-base-400 dark:bg-dark-base-400 inset-shadow-xs dark:inset-shadow-dark-decoration-100 shadow-md':
                mode() === 'basic'
            }"
          >
            <app-svg-icon
              type="icBasicFlow"
              class="w-5 h-5"
              [ngClass]="[
                mode() === 'basic'
                  ? '!text-base-content dark:!text-dark-base-content'
                  : '!text-neutral-content dark:!text-dark-neutral-content'
              ]"
            ></app-svg-icon>
            <div
              class="text-sm"
              [ngClass]="[
                mode() === 'basic' ? 'font-semibold' : 'font-medium',
                mode() === 'basic'
                  ? 'text-base-content dark:text-dark-base-content'
                  : 'text-neutral-content dark:text-dark-neutral-content'
              ]"
            >
              Basic
            </div>
          </div>
          <div
            class="flex items-center justify-center space-x-2 rounded-lg px-4 py-1.5 min-w-[100px]"
            [ngClass]="{
              'bg-base-400 dark:bg-dark-base-400 inset-shadow-xs inset-shadow-decoration-100 shadow-md':
                mode() === 'agent'
            }"
          >
            <app-svg-icon
              type="icAgentFlow"
              class="w-5 h-5"
              [ngClass]="[
                mode() === 'agent'
                  ? '!text-base-content dark:!text-dark-base-content'
                  : '!text-neutral-content dark:!text-dark-neutral-content'
              ]"
            ></app-svg-icon>
            <div
              class="text-sm"
              [ngClass]="[
                mode() === 'agent' ? 'font-semibold' : 'font-medium',
                mode() === 'agent'
                  ? 'text-base-content dark:text-dark-base-content'
                  : 'text-neutral-content dark:text-dark-neutral-content'
              ]"
            >
              Agent
            </div>
          </div>
        </div>
      </div>
      <!--<span
        class="text-[15px] text-neutral-content dark:text-dark-neutral-content"
        >Filter:</span
      >
      <dx-form-field
        [style.margin-bottom]="0"
        [style.&#45;&#45;dx-form-field-label-offset-y]="0"
        [subscriptHidden]="true"
      >
        <dx-select
          [formControl]="filterType"
          (valueChange)="onFilterChange($event)"
        >
          <dx-option value="basic/agent">Basic/Agent</dx-option>
          <dx-option value="event">Event</dx-option>
        </dx-select>
      </dx-form-field>-->
      <app-flow-env-select></app-flow-env-select>
    </div>
  </div>
  }

  <div class="flex-1 overflow-auto">
    <router-outlet></router-outlet>
  </div>
</div>
}

<bdc-walk-popup #flow_guided
                name="flow_guided"
                header="Flow Builder"
                xPosition="before"
                [horizontal]="true"
                sideNoteText="1/2"
                [showButton]="true"
                buttonText="Next"
                [mustCompleted]="{nav_guided_2: true}"
                [onButtonCompleteTask]="{flow_guided: true}"
                yPosition="below">
  Start building your flow here. <b>Publish, Edit</b> and <b>Edit</b>  your flow here.
</bdc-walk-popup>
<bdc-walk-popup #agent_flow
                name="flow_guided_2"
                header="Flow Agent"
                xPosition="before"
                [horizontal]="true"
                sideNoteText="2/2"
                [showButton]="true"
                buttonText="End"
                [mustCompleted]="{flow_guided: true}"
                [onButtonCompleteTask]="{flow_guided_2: true}">
  Goto <b>Agent</b>/<b>Flow</b> to setup your flow.
</bdc-walk-popup>
