import {
  CdkDragDrop,
  CdkDragEnd,
  CdkDragStart,
  DragDropModule,
} from '@angular/cdk/drag-drop';

import { CdkConnectedOverlay, CdkOverlayOrigin } from '@angular/cdk/overlay';
import { NgClass } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { ActivatedRoute, Params, Router } from '@angular/router';
import {
  AGENT_FLOW_PATH,
  APP_ROUTES,
  STUDIO_PATH,
  STUDIO_STATUS,
} from '@core/constants';
import { AgentIntroService } from '@core/services';
import { StudioStore, UIStore } from '@core/stores';
import {
  DxButton,
  DxCheckbox,
  DxDialog,
  DxFormField,
  DxInput,
  DxLoadingButton,
  DxPrefix,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroPlusMini,
  heroTrashMini,
  heroXCircleMini,
} from '@ng-icons/heroicons/mini';
import {
  heroArrowRight,
  heroChevronDown,
  heroChevronUp,
  heroEllipsisHorizontal,
  heroInformationCircle,
  heroSparkles,
  heroWrench,
  heroXMark,
} from '@ng-icons/heroicons/outline';
import {
  ConfirmDialogComponent,
  MobileDrawerComponent,
  MobileHeaderComponent,
  SvgIconComponent,
} from '@shared/components';
import {ClickOutsideDirective, MHeaderLeftDirective} from '@shared/directives';
import {
  IAgent,
  IAgentDev,
  IAgentToolDev,
  ITool,
  IToolDev,
} from '@shared/models';
import {
  AgentDevService,
  AgentService,
  AgentToolDevService,
  ToolDevService,
  ToolService,
} from '@shared/services';
import { AddOrEditAgentComponent } from '@views/studio/components/add-or-edit-agent/add-or-edit-agent.component';
import { AddToolComponent } from '@views/studio/components/add-tool/add-tool.component';
import { DetailAgentComponent } from '@views/studio/components/detail-agent/detail-agent.component';
import { forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import {BdcWalkPopupComponent, BdcWalkTriggerDirective} from 'bdc-walkthrough';

@Component({
  selector: 'app-agent-tool-mapping',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    NgIconsModule,
    DragDropModule,
    DxFormField,
    DxInput,
    SvgIconComponent,
    NgClass,
    DxButton,
    CdkOverlayOrigin,
    CdkConnectedOverlay,
    ClickOutsideDirective,
    MobileHeaderComponent,
    DxPrefix,
    MobileDrawerComponent,
    DxCheckbox,
    DxLoadingButton,
    MHeaderLeftDirective,
    BdcWalkPopupComponent,
    BdcWalkTriggerDirective,
  ],
  templateUrl: './agent-tool-mapping.component.html',
  styleUrl: './agent-tool-mapping.component.css',
  providers: [
    provideIcons({
      heroPlusMini,
      heroXCircleMini,
      heroTrashMini,
      heroArrowRight,
      heroChevronDown,
      heroChevronUp,
      heroWrench,
      heroEllipsisHorizontal,
      heroInformationCircle,
      heroSparkles,
      heroXMark,
    }),
  ],
  animations: [
    trigger('expandCollapse', [
      state('collapsed', style({
        height: '0px',
        opacity: 0,
        overflow: 'hidden',
        marginTop: '0px',
        marginBottom: '0px'
      })),
      state('expanded', style({
        height: '*',
        opacity: 1,
        overflow: 'hidden',
        marginTop: '12px',
        marginBottom: '0px'
      })),
      transition('collapsed <=> expanded', [
        animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ])
    ]),
    trigger('chevronRotate', [
      state('down', style({
        transform: 'rotate(0deg)'
      })),
      state('up', style({
        transform: 'rotate(180deg)'
      })),
      transition('down <=> up', [
        animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)')
      ])
    ])
  ]
})
export class AgentToolMappingComponent implements OnInit {
  agents = signal<Array<(IAgentDev | IAgent) & { tools?: IToolDev[] }>>([]);
  tools = signal<Array<IToolDev | ITool>>([]);
  agentToolDevs = signal<Array<IAgentToolDev>>([]);
  searchAgentTerm = signal<string>('');
  searchToolTerm = signal<string>('');
  selectingAgentId = signal<number | null>(null);

  listToolIdNeedAssign = signal<number[]>([]);
  listToolIdNeedUnAssign = signal<number[]>([]);
  assigningTool = signal<boolean>(false);
  selectedAgent = signal<IAgentDev | IAgent | null>(null);
  selectedAgentTools = signal<Array<IToolDev | ITool>>([]);
  isDragging = signal<boolean>(false);
  connectedDropLists = signal<string[]>([]);
  openToolAction = signal<number | undefined>(undefined);
  openToolAgentAction = signal<number | undefined>(undefined);
  openAgentAction = signal<number | undefined>(undefined);

  // AI-Powered Agent Creation
  showAiAgentCreation = signal<boolean>(false);
  aiAgentCreationCollapsed = signal<boolean>(false);
  agentDescription = signal<string>('');
  isGeneratingAgent = signal<boolean>(false);

  aiModels = signal<any[]>([
    { value: 'gpt-4o-mini', type: 'openai', label: 'gpt-4o-mini' },
    { value: 'gpt-4o', type: 'openai', label: 'gpt-4o' },
    { value: 'gpt-4.1-mini', type: 'openai', label: 'gpt-4.1-mini' },
    { value: 'gpt-4.1-nano', type: 'openai', label: 'gpt-4.1-nano' },
    { value: 'gpt-4.1', type: 'openai', label: 'gpt-4.1' },
    { value: 'gemini-1.5-flash', type: 'gemini', label: 'gemini-1.5-flash' },
    { value: 'gemini-2.0-flash', type: 'gemini', label: 'gemini-2.0-flash' },
  ]);
  viewListTool = signal<boolean>(false);
  filteredAgents = computed(() => {
    return this.agents().filter((agent) =>
      agent.name.toLowerCase().includes(this.searchAgentTerm().toLowerCase())
    );
  });
  selectingAgent = computed(() => {
    const agent = this.agents().find((a) => a.id === this.selectingAgentId());
    return agent || null;
  });
  filteredTools = computed<(IToolDev | ITool)[]>(() => {
    return this.tools().filter((tool) =>
      tool.name.toLowerCase().includes(this.searchToolTerm().toLowerCase())
    );
  });

  modelConfig: { name: string; temperature: number } = {
    name: 'openai',
    temperature: 0,
  };
  readonly STUDIO_STATUS = STUDIO_STATUS;
  private deleteZoneId = 'delete-zone';
  private messageListener!: (event: MessageEvent) => void;

  studioStore = inject(StudioStore);
  uiStore = inject(UIStore);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private agentDevService = inject(AgentDevService);
  private toolDevService = inject(ToolDevService);
  private agentToolDevService = inject(AgentToolDevService);
  private toolService = inject(ToolService);
  private agentService = inject(AgentService);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);
  private agentIntroService = inject(AgentIntroService);
  title: string = 'Agent Management';
  backFn = () => this.router.navigate([APP_ROUTES.MENU]);

  constructor() {
    effect(() => {
      const agentIds = this.agents().map((agent) => `agent-drop-${agent.id}`);
      this.connectedDropLists.set([...agentIds, this.deleteZoneId]);
    });

    effect(() => {
      const status = this.studioStore.status();
      this.loadData();
    });
    effect(() => {
      // Lắng nghe thay đổi từ cả hai signal
      this.updateUrlParams();
    });

    effect(() => {
      // Check if should show AI Agent Creation section
      this.checkShouldShowAiAgentCreation();
    });

    // Auto-collapse on window resize/zoom
    this.setupResponsiveCollapse();
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params: Params) => {
      this.searchAgentTerm.set(params['agent'] ?? '');
      this.searchToolTerm.set(params['tool'] ?? '');
    });

    this.studioStore.setStudioStatus(STUDIO_STATUS.DEV);
  }

  loadData(): void {
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      forkJoin({
        agents: this.agentDevService.getAll(),
        tools: this.toolDevService.getAll(),
        agentToolDevs: this.agentToolDevService.getAll(),
      }).subscribe({
        next: ({ agents, tools, agentToolDevs }) => {
          const toolMap = new Map<number, IToolDev>();
          tools.forEach((tool) => toolMap.set(tool.id as number, tool));

          const agentToolMap = new Map<number, IToolDev[]>();
          for (const agentToolDev of agentToolDevs) {
            const tool = toolMap.get(agentToolDev.tool_dev_id);
            if (tool) {
              if (!agentToolMap.has(agentToolDev.agent_dev_id)) {
                agentToolMap.set(agentToolDev.agent_dev_id, []);
              }
              agentToolMap.get(agentToolDev.agent_dev_id)!.push(tool);
            }
          }

          const enrichedAgents = agents.map((agent) => ({
            ...agent,
            tools: agentToolMap.get(agent.id as number) || [],
          }));

          this.agents.set(enrichedAgents);
          this.tools.set(tools);
          this.agentToolDevs.set(agentToolDevs);
        },
        error: () => {
          this.snackBar.open('Failed to load data', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
    }

    if (this.studioStore.status() === STUDIO_STATUS.LIVE) {
      // TODO: Get agents, tools and agentToolVersions from API
    }
  }

  selectAgent(agentId: number | undefined): void {
    if (agentId) {
      this.selectingAgentId.update((current) =>
        current === agentId ? null : agentId
      );

      const agent = this.agents().find((a) => a.id === agentId);
      this.selectedAgent.set(agent || null);
      this.selectedAgentTools.set(agent?.tools || []);
    }
  }

  onDragStarted(event: CdkDragStart): void {
    this.isDragging.set(true);
    const deleteZone = document.getElementById(this.deleteZoneId);
    if (deleteZone) {
      deleteZone.classList.remove('hidden');
    }
  }

  onDragEnded(event: CdkDragEnd): void {
    this.isDragging.set(false);
    setTimeout(() => {
      const deleteZone = document.getElementById(this.deleteZoneId);
      if (deleteZone) {
        deleteZone.classList.add('hidden');
      }
    }, 300);
  }

  onDrop(event: CdkDragDrop<any>, agentId: number): void {
    // If dropping to delete zone
    if (event.container.id === this.deleteZoneId) {
      // Only handle when dragging from an agent, not from available tools
      if (event.previousContainer.id.startsWith('agent-drop-')) {
        const tool = event.previousContainer.data[event.previousIndex];
        const prevAgentId = parseInt(
          event.previousContainer.id.replace('agent-drop-', '')
        );

        this.removeToolFromAgent(prevAgentId, tool.id || 0);
      }
      return;
    }

    // Check if the target agent is a RAG agent
    const targetAgent = this.agents().find((a) => a.id === agentId);
    if (targetAgent && this.isRAGAgent(targetAgent as IAgentDev)) {
      this.snackBar.open('RAG agents cannot receive tool assignments', '', {
        panelClass: 'dx-snack-bar-warning',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
      return;
    }

    // Only handle when dragging from Available Tools (tool-source) to an agent
    if (event.previousContainer.id === 'tool-source') {
      const tool = event.previousContainer.data[event.previousIndex];

      if (!tool?.id) {
        this.snackBar.open('Invalid tool', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        return;
      }

      // Check if tool is already assigned to this agent
      const agent = this.agents().find((a) => a.id === agentId);
      if (agent?.tools?.some((t: ITool) => t.id === tool.id)) {
        this.snackBar.open('This tool is already assigned to this agent', '', {
          panelClass: 'dx-snack-bar-warning',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        return;
      }

      // Call API to assign tool to agent
      this.agentToolDevService.assign(agentId, tool.id).subscribe({
        next: () => {
          // Update UI - add tool to agent
          this.agents.update((agents) => {
            return agents.map((a) => {
              if (a.id === agentId) {
                // Clone the agent to avoid direct state mutation
                const updatedAgent = { ...a };
                if (!updatedAgent.tools) {
                  updatedAgent.tools = [];
                }

                // Clone tool to avoid reference issues
                const toolClone = { ...tool };

                // Add to agent tools at specified position
                updatedAgent.tools = [...updatedAgent.tools];
                updatedAgent.tools.splice(event.currentIndex, 0, toolClone);
                return updatedAgent;
              }
              return a;
            });
          });

          this.snackBar.open('Tool assigned to agent successfully', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
      return;
    }

    // Drag and drop between different agents
    if (
      event.previousContainer.id !== event.container.id &&
      event.previousContainer.id.startsWith('agent-drop-') &&
      event.container.id.startsWith('agent-drop-')
    ) {
      const tool = event.previousContainer.data[event.previousIndex];
      const prevAgentId = parseInt(
        event.previousContainer.id.replace('agent-drop-', '')
      );

      // Check if tool is already assigned to target agent
      const targetAgent = this.agents().find((a) => a.id === agentId);
      if (targetAgent?.tools?.some((t: ITool) => t.id === tool.id)) {
        this.snackBar.open(
          'This tool is already assigned to target agent',
          '',
          {
            panelClass: 'dx-snack-bar-warning',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          }
        );
        return;
      }

      // Remove from old agent
      this.agentToolDevService.unassign(prevAgentId, tool.id || 0).subscribe({
        next: () => {
          // Update UI - remove from old agent
          this.agents.update((agents) => {
            return agents.map((a) => {
              if (a.id === prevAgentId) {
                const updatedAgent = { ...a };
                if (updatedAgent.tools) {
                  updatedAgent.tools = updatedAgent.tools.filter(
                    (t: ITool) => t.id !== tool.id
                  );
                }
                return updatedAgent;
              }
              return a;
            });
          });

          // Add to new agent
          this.agentToolDevService.assign(agentId, tool.id).subscribe({
            next: () => {
              // Update UI - add to new agent
              this.agents.update((agents) => {
                return agents.map((a) => {
                  if (a.id === agentId) {
                    const updatedAgent = { ...a };
                    if (!updatedAgent.tools) {
                      updatedAgent.tools = [];
                    }

                    // Clone tool to avoid reference issues
                    const toolClone = { ...tool };

                    // Add to agent tools at specified position
                    updatedAgent.tools = [...updatedAgent.tools];
                    updatedAgent.tools.splice(event.currentIndex, 0, toolClone);
                    return updatedAgent;
                  }
                  return a;
                });
              });
              this.snackBar.open('Tool moved to new agent', '', {
                panelClass: 'dx-snack-bar-success',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
            },
            error: (_) => {
              this.snackBar.open('Unable to move tool to new agent', '', {
                panelClass: 'dx-snack-bar-error',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
            },
          });
        },
        error: (_) => {
          this.snackBar.open('Unable to move tool', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
      return;
    }
  }

  onEditFlowTool(tool: IToolDev | ITool) {
    void this.router.navigate([
      `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.TOOL}/${tool.id}`,
    ]);
  }

  assignTool() {
    console.log(
      'Assign tool:',
      this.listToolIdNeedAssign(),
      'Unassign tool:',
      this.listToolIdNeedUnAssign()
    );
    if (!this.selectedAgent()) return;

    // Check if the selected agent is a RAG agent
    if (
      this.selectedAgent() &&
      this.isRAGAgent(this.selectedAgent() as IAgentDev)
    ) {
      this.snackBar.open('RAG agents cannot receive tool assignments', '', {
        panelClass: 'dx-snack-bar-warning',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
      return;
    }

    this.assigningTool.set(true);
    const agentId = this.selectedAgent()?.id || 0;
    /*xử lý api bất đồng bộ*/
    const assignCalls = this.listToolIdNeedAssign().map((toolId) =>
      this.agentToolDevService
        .assign(agentId, toolId)
        .pipe(catchError(() => of('assign_error')))
    );

    const unassignCalls = this.listToolIdNeedUnAssign().map((toolId) =>
      this.agentToolDevService
        .unassign(agentId, toolId)
        .pipe(catchError(() => of('unassign_error')))
    );

    const allCalls = [...assignCalls, ...unassignCalls];

    if (allCalls.length > 0) {
      forkJoin(allCalls).subscribe({
        next: (results) => {
          const hasError = results.some(
            (r) => r === 'assign_error' || r === 'unassign_error'
          );
          this.snackBar.open(
            hasError ? 'Assign tools failed' : 'Assign tools successfully',
            '',
            {
              panelClass: hasError
                ? 'dx-snack-bar-error'
                : 'dx-snack-bar-success',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            }
          );
          if (!hasError) {
            this.assigningTool.set(false);
            this.closeListToolAssign();
            this.loadData();
          }
        },
        error: (error) => {
          // console.error('Error in assignTool:', error);
          this.snackBar.open('Assign tools failed', '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.assigningTool.set(false);
          this.closeListToolAssign();
          this.loadData();
        },
      });
    } else {
      // Trường hợp không có API nào để gọi
      this.assigningTool.set(false);
      this.closeListToolAssign();
    }
  }
  onSelectTool(selected: boolean, tool: IToolDev) {
    // Check if the selecting agent is a RAG agent
    if (
      this.selectingAgent() &&
      this.isRAGAgent(this.selectingAgent() as IAgentDev)
    ) {
      this.snackBar.open('RAG agents cannot receive tool assignments', '', {
        panelClass: 'dx-snack-bar-warning',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
      return;
    }

    const hasTool = this.selectingAgent()?.tools?.some((t) => t.id === tool.id);
    const toolId = tool?.id || 0;

    if (selected) {
      this.listToolIdNeedAssign.update((list) => {
        list.push(toolId);
        return list;
      });
      if (hasTool) return;
    } else if (hasTool) {
      this.listToolIdNeedAssign.update((list) =>
        list.filter((id) => id !== tool.id)
      );
      this.listToolIdNeedUnAssign.update((list) => {
        list.push(toolId);
        return list;
      });
    }
  }

  removeToolFromAgent(agentId: number, toolId: number): void {
    this.agentToolDevService.unassign(agentId, toolId).subscribe({
      next: () => {
        this.agents.update((agents) => {
          return agents.map((a) => {
            if (a.id === agentId) {
              const updatedAgent = { ...a };
              if (updatedAgent.tools) {
                updatedAgent.tools = updatedAgent.tools.filter(
                  (tool: ITool) => tool.id !== toolId
                );
              }
              return updatedAgent;
            }
            return a;
          });
        });

        this.snackBar.open('Tool removed from agent successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
      error: (_) => {
        this.snackBar.open('Unable to remove tool from agent', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }

  openCreateToolDialog() {
    this.dialog
      .open(AddToolComponent, {
        width: '50vw',
        minWidth: '340px',
        data: { isEdit: false },
      })
      .afterClosed()
      .subscribe(() => {
        this.loadData();
      });
  }

  openCreateAgentDialog() {
    this.dialog
      .open(AddOrEditAgentComponent, {
        height: '80dvh',
        width: '50dvw',
        minWidth: '340px',
        data: { isEdit: false },
      })
      .afterClosed()
      .subscribe((val) => {
        this.loadData();
      });
  }

  onPublishTool(tool: IToolDev) {
    // chưa có hàm này ở màn tools
    console.log('Publish tool:', tool);
  }

  onEditTool(tool: IToolDev) {
    this.dialog
      .open(AddToolComponent, {
        width: '50vw',
        minWidth: '340px',
        data: {
          id: tool.id,
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters,
          flow_data: tool.flow_data,
          isEdit: true,
        },
      })
      .afterClosed()
      .subscribe(() => this.loadData());
  }

  onDeleteTool(tool: IToolDev) {
    if (tool.id) {
      const id = tool.id;
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete Tool',
            content: `Are you sure you want to delete tool "${tool.name}"?`,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            isDelete: true,
          },
        })
        .afterClosed()
        .subscribe((result: any) => {
          if (!!result && this.studioStore.status() === STUDIO_STATUS.DEV) {
            this.toolDevService.delete(id).subscribe({
              next: () => {
                this.snackBar.open('Tool deleted successfully', '', {
                  panelClass: 'dx-snack-bar-success',
                  duration: 5000,
                  verticalPosition: 'top',
                  horizontalPosition: 'right',
                });
                this.loadData();
              },
            });
          }
          if (result && this.studioStore.status() === STUDIO_STATUS.LIVE) {
            this.toolService.delete(id).subscribe({
              next: () => {
                this.snackBar.open('Tool deleted successfully', '', {
                  panelClass: 'dx-snack-bar-success',
                  duration: 5000,
                  verticalPosition: 'top',
                  horizontalPosition: 'right',
                });
                this.loadData();
              },
            });
          }
        });
    }
  }

  onViewAgent(agent: IAgentDev | IAgent): void {
    this.dialog.open(DetailAgentComponent, {
      width: '50dvw',
      minWidth: '340px',
      data: { agent },
    });
  }

  onEditAgent(agent: IAgentDev) {
    let temperature = 0;
    let modelConfigName = '';

    try {
      if (agent.model_config) {
        const modelCfg = JSON.parse(agent.model_config);
        this.modelConfig = {
          name: modelCfg.name || this.aiModels()[0].value,
          temperature: modelCfg.temperature || 0,
        };
        temperature = modelCfg.temperature || 0;
        modelConfigName = modelCfg.name || '';
      } else {
        this.modelConfig = {
          name: this.aiModels()[0].value,
          temperature: 0,
        };
      }
    } catch (e) {
      temperature = 0;
      modelConfigName = '';
      this.modelConfig = {
        name: this.aiModels()[0].value,
        temperature: 0,
      };
    }

    // Find the model using model_config.name instead of llm_type
    const modelForName = this.aiModels().find(
      (model) => model.value === modelConfigName
    );
    let selectedValue = modelForName?.value || this.aiModels()[0].value;

    this.dialog
      .open(AddOrEditAgentComponent, {
        width: '50vw',
        minWidth: '340px',
        data: {
          agent: agent,
          model_config: this.modelConfig,
          temperature: temperature,
          selectedModel: selectedValue,
          isEdit: true,
        },
      })
      .afterClosed()
      .subscribe((val) => {
        this.loadData();
      });
  }

  onDeleteAgent(agent: IAgentDev | IAgent): void {
    if (agent.id) {
      const id = agent.id;
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete Agent',
            content: `Are you sure you want to delete agent "${agent.name}"?`,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            isDelete: true,
          },
        })
        .afterClosed()
        .subscribe((result: any) => {
          if (!!result && this.studioStore.status() === STUDIO_STATUS.DEV) {
            this.agentDevService.delete(id).subscribe({
              next: () => {
                this.snackBar.open('Agent deleted successfully', '', {
                  panelClass: 'dx-snack-bar-success',
                  duration: 5000,
                  verticalPosition: 'top',
                  horizontalPosition: 'right',
                });
                this.loadData();
              },
            });
          }
          if (result && this.studioStore.status() === STUDIO_STATUS.LIVE) {
            this.agentService.delete(id).subscribe({
              next: () => {
                this.snackBar.open('Agent deleted successfully', '', {
                  panelClass: 'dx-snack-bar-success',
                  duration: 5000,
                  verticalPosition: 'top',
                  horizontalPosition: 'right',
                });
                this.loadData();
              },
            });
          }
        });
    }
  }
  closeListToolAssign() {
    this.viewListTool.set(!this.viewListTool());
    this.selectedAgentTools.set([]);
    this.listToolIdNeedAssign.set([]);
    this.listToolIdNeedUnAssign.set([]);
    this.searchToolTerm.set('');
  }

  updateUrlParams() {
    const queryParams: any = {};
    queryParams['agent'] = this.searchAgentTerm();
    queryParams['tool'] = this.searchToolTerm();

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge',
      replaceUrl: true,
    });
  }
  isSelectedTool(tool: ITool) {
    return this.selectingAgent()?.tools?.some((t) => t.id === tool.id) || false;
  }

  getEnterPredicate(agent: IAgentDev) {
    return () => !this.isRAGAgent(agent); // Return false nếu là RAG agent để ngăn drop
  }
  isRAGAgent(agent: IAgentDev) {
    return agent.name.toLowerCase().includes(' rag ');
  }

  navigateToFlowEvent(): void {
    void this.router.navigate(
      [
        `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW_EVENT}`,
      ],
      {
        queryParams: { from: STUDIO_PATH.AGENT_FLOW },
      }
    );
  }

  checkShouldShowAiAgentCreation(): void {
    this.agentIntroService
      .shouldShowAiAgentCreation()
      .subscribe((shouldShow) => {
        console.log(shouldShow);
        this.showAiAgentCreation.set(shouldShow);
      });
  }

  getCharacterCount(): number {
    return this.agentDescription().length;
  }

  onAgentDescriptionChange(value: string): void {
    if (value.length <= 1000) {
      this.agentDescription.set(value);
    }
  }

  onGenerateAgentAndTools(): void {
    if (!this.agentDescription().trim()) {
      this.snackBar.open("Please describe your chatbot's purpose", '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
      return;
    }

    this.isGeneratingAgent.set(true);

    // TODO: Implement actual AI generation API call
    // For now, simulate the generation process
    setTimeout(() => {
      this.snackBar.open('Agent and tools generated successfully!', '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });

      this.isGeneratingAgent.set(false);
      this.showAiAgentCreation.set(false);
      this.agentDescription.set('');

      // Reload data to show new agent
      this.loadData();
    }, 3000);
  }

  onLearnMoreAgentCreation(): void {
    window.open(
      'https://docs.dxconnect.lifesup.ai/Diving%20Deeper/Studio/builder/Agent%20Flow%20Usage%20Guide/agent-flow-guide',
      '_blank'
    );
  }

  toggleAiAgentCreationCollapse(): void {
    this.aiAgentCreationCollapsed.update(collapsed => !collapsed);
  }

  private setupResponsiveCollapse(): void {
    if (typeof window !== 'undefined') {
      const checkAndCollapse = () => {
        const screenWidth = window.innerWidth;
        const zoomLevel = window.devicePixelRatio;
        
        // Auto-collapse if screen is small or zoom is high
        if (screenWidth < 1400 || zoomLevel > 1.25) {
          this.aiAgentCreationCollapsed.set(true);
        }
      };

      // Check on initial load
      checkAndCollapse();

      // Listen for resize events
      window.addEventListener('resize', checkAndCollapse);
    }
  }
}
