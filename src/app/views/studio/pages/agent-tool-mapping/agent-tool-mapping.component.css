.list-tools, .list-agents {
  max-height: calc(100dvh - 320px);
}

.with-ai-creation .list-tools,
.with-ai-creation .list-agents {
  max-height: calc(100dvh - 614px);
}

.with-ai-creation.ai-collapsed .list-tools,
.with-ai-creation.ai-collapsed .list-agents {
  max-height: calc(100dvh - 454px);
}

.list-agents::-webkit-scrollbar,
.list-tools::-webkit-scrollbar {
  display: none !important;
}

/* Drag and drop styles */
.cdk-drag-preview {
  border-radius: 16px;
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0.3;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.drop-area.cdk-drop-list-dragging .cdk-drag:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* <PERSON><PERSON><PERSON> thả khi đang kéo */
.drop-area.cdk-drop-list-receiving {
  background-color: rgba(var(--light-primary-rgb), 0.1);
  border: 2px dashed var(--light-primary);
}

/* Vùng kéo thả */
.cdk-drop-list {
  transition: background 150ms ease;
}

/* Hiệu ứng hover trên các item có thể kéo */
/* [cdkDrag]:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
} */

/* Hiệu ứng khi đang kéo */
.cdk-drag-dragging {
  cursor: grabbing !important;
}

/* Hiển thị vùng xóa khi đang kéo */
.cdk-drag-dragging + .delete-zone,
.cdk-drop-list-dragging + .delete-zone,
.cdk-drop-list-dragging .delete-zone,
.cdk-drag-dragging ~ .delete-zone {
  display: block !important;
}

/* Hiệu ứng khi kéo thả vào vùng xóa */
#delete-zone.cdk-drop-list-receiving {
  background-color: rgba(239, 68, 68, 0.1); /* Màu đỏ với độ trong suốt */
  border-color: rgb(239, 68, 68); /* Màu đỏ đậm */
}

/* Hiệu ứng khi kéo thả qua vùng xóa */
#delete-zone.cdk-drop-list-dragging-over {
  background-color: rgba(239, 68, 68, 0.2); /* Màu đỏ đậm hơn khi hover */
}


/* Loading animation */
.loader {
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 3px solid var(--light-primary);
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dark .loader {
  border-top-color: var(--dark-primary);
}

/* Kiểu dáng vùng xóa */
.delete-zone {
  min-height: 60px;
  transition: all 0.3s ease;
}

/* Hiệu ứng các item trong agent có thể kéo thả */
.agent-tool-item {
  position: relative;
}

.agent-tool-item .cdkDragHandle {
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.agent-tool-item:hover .cdkDragHandle {
  opacity: 1;
}
