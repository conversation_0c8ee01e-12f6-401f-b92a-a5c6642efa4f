@if (!uiStore.isHandset()){
<div class="h-full flex flex-col overflow-hidden">
  <div class="flex items-start justify-between">
    <h1
      class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
    >
      Triggers
    </h1>
    <app-flow-env-select></app-flow-env-select>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-7 gap-y-4 md:gap-x-4 h-full mt-6">
    <!--    list event-->
    <div class="col-span-2 flex flex-col w-full">
      <div
        class="rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
      >
        <div
          class="p-4 flex flex-wrap 2xl:flex-nowrap gap-2 2xl:gap-0 2xl:space-x-2 items-center justify-between border-b border-primary-border dark:border-dark-primary-border"
        >
          <dx-form-field
            class="w-full"
            id="search"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
            <input
              dxInput
              [(ngModel)]="searchModel.keyword"
              (ngModelChange)="searchSubject.next($event)"
              [type]="'text'"
              placeholder="Search by Name"
            />
          </dx-form-field>
          <button
            class="flex-shrink-0 w-full 2xl:w-fit"
            dx-button="filled"
            [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
            (click)="createEvent()"
          >
            <div class="flex items-center justify-between space-x-1">
              <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>
              <span class="text-sm">Add trigger</span>
            </div>
          </button>
        </div>

        <div class="h-full list-events overflow-y-auto">
          @for (data of listEvent(); track $index; let first = $first, last =
          $last) {
          <div
            class="p-4 space-x-3 flex items-center justify-between hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer group"
            [ngClass]="{
              'border-b border-primary-border dark:border-dark-primary-border':
                !last
            }"
            [id]="'event-dev-env-' + $index"
            (click)="selectEvent(data.id)"
          >
            <div
              class="flex space-x-3 items-center justify-start !text-neutral-content dark:!text-dark-neutral-content"
            >
              <app-svg-icon
                type="icWebDev"
                class="w-5 h-5 group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
              ></app-svg-icon>
              <div
                class="truncate max-w-[15dvw] text-ellipsis group-hover:text-base-content group-hover:dark:text-dark-base-content"
                [ngClass]="{
                  '!text-primary-hover dark:!text-dark-primary-hover':
                    data.id === eventSelectedId()
                }"
              >
                {{ data.name }}
              </div>
            </div>
            <div
              class="flex space-x-3 items-center justify-end !text-neutral-content dark:!text-dark-neutral-content"
            >
              <app-svg-icon
                type="icEdit"
                class="w-6 h-6 cursor-pointer hover:text-primary-hover dark:hover:text-dark-primary-hover"
                (click)="editEvent(data, $event)"
              ></app-svg-icon>
              <app-svg-icon
                type="icTrash"
                class="w-6 h-6 cursor-pointer"
                (click)="deleteEvent(data, $event)"
              ></app-svg-icon>
            </div>
          </div>
          } @empty {
          <div class="flex items-center justify-center py-4">
            <div
              class="text-neutral-content dark:text-dark-neutral-content italic text-[15px]"
            >
              No data available
            </div>
          </div>
          }
        </div>
      </div>
    </div>

    <!--    edit event-->
    <div
      class="col-span-5 flex flex-col w-full h-full p-6 space-y-4 rounded-2xl border border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
    >
      @if (listEvent().length === 0) {
      <div class="flex items-center justify-center h-full py-4">
        <div
          class="text-neutral-content dark:text-dark-neutral-content italic text-[15px]"
        >
          Start adding your first trigger event to select
        </div>
      </div>
      } @else {
      <div
        class="flex items-center justify-between text-2xl font-bold text-base-content dark:text-dark-base-content"
      >
        {{ eventSelected()?.name }}
      </div>
      <div class="flex-1 grow overflow-y-auto flex flex-col space-y-4 sm:space-y-8">
        <div class="flex space-x-2 items-end justify-start">
          <dx-form-field
            class="w-full"
            [subscriptHidden]="true"
            [style.margin-bottom]="0"
          >
            <dx-label class="text-sm"
              >Event Trigger ID (Copy and paste this ID into the connector for this use
              case.)</dx-label
            >
            <input
              dxInput
              [(ngModel)]="eventSelectedId"
              [type]="'text'"
              [readonly]="true"
              placeholder="Enter name"
            />
          </dx-form-field>
          <button
            dxButton="elevated"
            class="flex-none items-center justify-center"
            [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
            (click)="copyEventId()"
          >
            <app-svg-icon
              dxPrefix
              type="icCopy"
              class="w-6 h-6 !text-primary dark:!text-dark-primary mt-1"
            ></app-svg-icon>
          </button>
        </div>

        <hr class="text-primary-border dark:text-dark-primary-border" />

        <div class="w-full h-full relative flex flex-col mb-2">
          <div
            class="mb-[6px] text-[15px] text-neutral-content dark:text-dark-neutral-content"
          >
            Config
          </div>
          <div
            class="flex-1 mb-14 bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
          >
            <ngx-monaco-editor
              [options]="editorOptions()"
              [(ngModel)]="configData"
              (ngModelChange)="onChangeBody(true)"
            ></ngx-monaco-editor>
          </div>
          <div
            class="absolute bottom-0 left-0 right-0 flex items-center justify-end sm:justify-start"
          >
            <button
              dxLoadingButton="filled"
              [loading]="isCreateOrUpdateEvent()"
              [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
              (click)="saveEvent()"
            >
              Save
            </button>
          </div>
        </div>
      </div>
      }
    </div>
  </div>
</div>
} @else {
<app-mobile-header [title]="title" [backFn]="backFn">
  <app-flow-env-select mHeaderRight></app-flow-env-select>
</app-mobile-header>
<div
  class="w-full min-h-screen flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
>
  <div
    (click)="viewListEvent.set(true)"
    class="px-2 py-3 flex items-center justify-between bg-base-400 dark:bg-dark-base-400 rounded-xl border border-primary-border dark:border-dark-primary-border"
  >
    <app-svg-icon
      type="icEvent"
      class="w-5 h-5 !text-primary dark:!text-dark-primary"
    ></app-svg-icon>
    <div
      class="flex-1 text-center text-normal font-bold text-base-content dark:text-dark-base-content"
    >
      {{ this.eventSelected()?.name || "Select an event" }}
    </div>
    <app-svg-icon
      type="icChevronDown"
      class="w-5 h-5 !text-primary dark:!text-dark-primary cursor-pointer"
    ></app-svg-icon>
  </div>
  <div
    class="col-span-5 flex-1 flex flex-col w-full h-full p-4 space-y-4 rounded-2xl border border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    @if (listEvent().length === 0) {
    <div class="flex items-center justify-center h-full py-4">
      <div
        class="text-neutral-content dark:text-dark-neutral-content italic text-[15px]"
      >
        Start adding your first event to select
      </div>
    </div>
    } @else {
    <div
      class="flex items-center justify-between text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ eventSelected()?.name }}
    </div>
    <div class="flex-1 overflow-y-auto flex flex-col space-y-4 sm:space-y-8">
      <div class="text-sm text-neutral-content dark:text-dark-neutral-content">
        Event ID (Copy and paste this ID into the connector for this use case.)
      </div>
      <div class="flex space-x-2 items-end justify-start">
        <dx-form-field
          class="w-full"
          [subscriptHidden]="true"
          [style.margin-bottom]="0"
        >
          <input
            dxInput
            [(ngModel)]="eventSelectedId"
            [type]="'text'"
            [readonly]="true"
            placeholder="Enter name"
          />
        </dx-form-field>
        <button
          class="flex-shrink-0 flex items-center justify-center !w-[46.5px] !h-[46.5px] rounded-xl bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border cursor-pointer hover:opacity-70"
          [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
          (click)="copyEventId()"
        >
          <app-svg-icon
            type="icCopy"
            class="text-2xl w-6 h-6 !text-primary dark:!text-dark-primary"
          ></app-svg-icon>
        </button>
      </div>

      <hr class="text-primary-border dark:text-dark-primary-border" />

      <div class="flex-1 w-full h-full relative flex flex-col mb-2">
        <div
          class="mb-[6px] text-[15px] text-neutral-content dark:text-dark-neutral-content"
        >
          Config
        </div>
        <div
          class="flex-1 mb-14 bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
        >
          <ngx-monaco-editor
            [options]="editorOptions()"
            [(ngModel)]="configData"
            (ngModelChange)="onChangeBody(true)"
          ></ngx-monaco-editor>
        </div>
        <div
          class="absolute bottom-0 left-0 right-0 flex items-center justify-end sm:justify-start"
        >
          <button
            dxLoadingButton="filled"
            [loading]="isCreateOrUpdateEvent()"
            [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
            (click)="saveEvent()"
          >
            Save
          </button>
        </div>
      </div>
    </div>
    }
  </div>
</div>

<app-mobile-drawer
  [visible]="viewListEvent()"
  [direction]="'btt'"
  [size]="'60%'"
  (visibleChange)="viewListEvent.set(false)"
>
  <div class="w-full h-full flex flex-col">
    <div
      class="w-full h-12 px-4 py-3 flex items-center justify-between bg-base-200 dark:bg-dark-base-200"
    >
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
        (click)="viewListEvent.set(false)"
      ></app-svg-icon>
      <div
        class="text-[16px] font-bold text-base-content dark:text-dark-base-content"
      >
        Event
      </div>
      <div class="flex items-center justify-end w-6 h-6">
        @if (studioStoreStatus() !== STUDIO_STATUS.LIVE) {
        <app-svg-icon
          type="icPlus"
          class="w-6 h-6 !text-primary"
          (click)="createEvent()"
        ></app-svg-icon>
        }
      </div>
    </div>

    <div
      class="flex-1 w-full p-4 flex flex-col gap-y-3 bg-base-100 dark:bg-dark-base-100"
    >
      <div class="col-span-2 flex flex-col w-full">
        <div>
          <div
            class="lg:p-4 flex flex-wrap 2xl:flex-nowrap gap-2 2xl:gap-0 2xl:space-x-2 items-center justify-between lg:border-b border-primary-border dark:border-dark-primary-border"
          >
            <dx-form-field
              class="w-full"
              id="search"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <app-svg-icon
                dxPrefix
                type="icSearch"
                class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
              ></app-svg-icon>
              <input
                dxInput
                [(ngModel)]="searchModel.keyword"
                (ngModelChange)="searchSubject.next($event)"
                [type]="'text'"
                placeholder="Search by Name"
              />
            </dx-form-field>
          </div>

          <div class="h-full list-events overflow-y-auto">
            @for (data of listEvent(); track $index; let first = $first, last =
            $last) {
            <div
              class="py-4 lg:p-4 space-x-3 flex items-center justify-between hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer group"
              [ngClass]="{
                'border-b border-primary-border dark:border-dark-primary-border':
                  !last
              }"
              [id]="'event-dev-env-' + $index"
              (click)="selectEvent(data.id); viewListEvent.set(false)"
            >
              <div
                class="flex space-x-3 items-center justify-start !text-neutral-content dark:!text-dark-neutral-content"
              >
                <app-svg-icon
                  type="icWebDev"
                  class="w-5 h-5 group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
                  [ngClass]="{
                    '!text-primary-hover dark:!text-dark-primary-hover':
                      data.id === eventSelectedId()
                  }"
                ></app-svg-icon>
                <div
                  class="truncate max-w-[65dvw] text-ellipsis group-hover:text-base-content group-hover:dark:text-dark-base-content"
                >
                  {{ data.name }}
                </div>
              </div>
              <div
                class="flex space-x-3 items-center justify-end !text-neutral-content dark:!text-dark-neutral-content"
              >
                <app-svg-icon
                  type="icEdit"
                  class="w-6 h-6 cursor-pointer hover:text-primary-hover dark:hover:text-dark-primary-hover"
                  (click)="editEvent(data, $event)"
                ></app-svg-icon>
                <app-svg-icon
                  type="icTrash"
                  class="w-6 h-6 cursor-pointer"
                  (click)="deleteEvent(data, $event)"
                ></app-svg-icon>
              </div>
            </div>
            } @empty {
            <div class="flex items-center justify-center py-4">
              <div
                class="text-neutral-content dark:text-dark-neutral-content italic text-[15px]"
              >
                No data available
              </div>
            </div>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</app-mobile-drawer>

}
