import {
  AfterViewInit,
  Component,
  effect,
  ElementRef,
  inject,
  input,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { SvgIconComponent } from '@shared/components/svg-icon/svg-icon.component';
import { Router } from '@angular/router';
import {
  AGENT_FLOW_PATH,
  APP_ROUTES,
  STUDIO_PATH,
  TRIGGER_KEYS,
} from '@core/constants';
import { TriggerService } from '@core/services';
import { StudioStore, UIStore } from '@core/stores';
import { DebugMessageService, DebugMessage } from '@shared/services';
import EditorFlowView from '@views/flow-editor/views/EditorFlowView';
import * as React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { Subject, Subscription } from 'rxjs';
import { v4 as uuid } from 'uuid';

// Interface for UI debug item
interface DebugItem {
  id: string;
  time: string;
  title: string;
  expanded: boolean;
  // Secondary fields (expandable)
  conversation_id: string;
  node_id: string;
  edge: string | null;
  is_prompt: boolean;
  output_key: string;
  result: string;
}

@Component({
  selector: 'app-build-tool-flow',
  standalone: true,
  imports: [CommonModule, SvgIconComponent],
  templateUrl: './build-tool-flow.component.html',
  styleUrl: './build-tool-flow.component.css',
})

export class BuildToolFlowComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  toolId = input.required<number>();

  rootId: string = 'flow-canvas';
  root: Root | undefined;
  protected destroy$ = new Subject<void>();

  // Debug panel state (separate from existing debug mode)
  debugPanelVisible = signal(false);

  // Real-time WebSocket debug data
  debugData = signal<DebugItem[]>([]);

  // Conversation ID for message filtering
  private UUID: string = uuid();

  // Service subscription for debug messages
  private debugMessageSubscription: Subscription = new Subscription();

  private messageListener!: (event: MessageEvent) => void;

  private router: Router = inject(Router);
  private el: ElementRef = inject(ElementRef);
  private studioStore = inject(StudioStore) as any;
  private uiStore = inject(UIStore) as any;
  private triggerService = inject(TriggerService) as any;
  private debugMessageService = inject(DebugMessageService);

  private previousStatus: string | null = this.studioStore.status();

  private studioStoreEffect = effect(() => {
    const currentStatus = this.studioStore.status();
    if (currentStatus !== this.previousStatus) {
      this.previousStatus = currentStatus;
      void this.router.navigate(['/studio/build']);
    }
  });

  private themeEffect = effect(() => {
    const currentTheme = this.uiStore.theme();
    postMessage({
      type: 'theme',
      data: { theme: currentTheme },
    });
  });



  ngOnInit(): void {
    this.messageListener = (event) => {
      if (event && event?.data && event?.data.type === 'debug_mode') {
        this.studioStore.setFlowDebugMode(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'toggle_debug_panel') {
        this.debugPanelVisible.set(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'back_to_agent_tool') {
        void this.router.navigate(
          [
            `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.AGENT_TOOL_MAPPING}`,
          ],
          {
            queryParams: {
              agent: '',
              tool: '',
            },
          }
        );
      }
    };
    window.addEventListener('message', this.messageListener);

    // Subscribe to debug messages from the service
    this.setupDebugMessageSubscription();

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'false');
  }

  ngAfterViewInit(): void {
    this.render();
    postMessage({
      type: 'studio_status',
      data: { status: this.studioStore.status() },
    });
    // Send conversation_id to React component for WebSocket communication
    postMessage({
      type: 'conversation_id',
      data: this.UUID
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up debug message subscription
    this.debugMessageSubscription.unsubscribe();

    if (this.root) {
      this.root.unmount();
      this.root = undefined;
    }

    // Reset debug mode when component is destroyed
    this.studioStore.setFlowDebugMode(false);

    // Reset debug panel state
    this.debugPanelVisible.set(false);

    // Remove message listener
    window.removeEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'true');
  }

  closeDebugPanel(): void {
    console.log('Closing debug panel');
    this.debugPanelVisible.set(false);
    // Notify React component that debug panel was closed
    window.postMessage({
      type: 'debug_panel_closed',
      data: false
    }, '*');
  }

  clearDebugData(): void {
    console.log('Clearing debug data');
    this.debugData.set([]);
  }



  toggleDebugItem(itemId: string): void {
    this.debugData.update(items =>
      items.map(item =>
        item.id === itemId
          ? { ...item, expanded: !item.expanded }
          : item
      )
    );
  }

  // Helper method to format null/empty values for display
  formatDebugValue(value: string | null | undefined): string {
    if (value === null || value === undefined || value === '') {
      return 'N/A';
    }
    return value;
  }

  // Helper method to normalize null values (convert string "null" to actual null)
  private normalizeNullValue(value: string | null | undefined): string | null {
    if (value === null || value === undefined || value === '' || value === 'null' || value === 'undefined') {
      return null;
    }
    return value;
  }

  private setupDebugMessageSubscription(): void {
    // Subscribe to debug messages from the service
    this.debugMessageSubscription = this.debugMessageService.debugMessages$.subscribe({
      next: (message) => {
        console.log('📨 Received debug message from service:', message);
        this.handleDebugMessage(message);
      },
      error: (error) => {
        console.error('❌ Error receiving debug message from service:', error);
      }
    });
  }



  private handleDebugMessage(message: DebugMessage): void {
    try {

      const debugItem: DebugItem = {
        id: `${message.node_id}_${Date.now()}`, // Create unique ID
        time: message.start_time || new Date().toISOString(),
        title: message.name || 'Unknown',
        expanded: false,
        // Secondary fields
        conversation_id: message.conversation_id,
        node_id: message.node_id,
        edge: this.normalizeNullValue(message.edge),
        is_prompt: Boolean(message.is_prompt),
        output_key: this.normalizeNullValue(message.output_key) || '',
        result: this.normalizeNullValue(message.result) || ''
      };
      // Add new debug message to the beginning of the array
      this.debugData.update(items => {
        const newItems = [debugItem, ...items];
        return newItems;
      });
    } catch (error) {
      console.error('❌ Error handling debug message:', error, message);
    }
  }

  private render() {
    if (this.toolId()) {
      const reactRoot = this.el.nativeElement.querySelector(`#${this.rootId}`);
      if (reactRoot) {
        if (this.root) {
          this.root.unmount();
        }

        this.root = createRoot(reactRoot);
        this.root.render(
          React.createElement(EditorFlowView, { flowId: this.toolId() })
        );

        postMessage({
          type: 'theme',
          data: { theme: this.uiStore.theme() },
        });
      }
    }
  }
}
