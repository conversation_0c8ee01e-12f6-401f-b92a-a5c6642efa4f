/* Component Container Styles */
.build-tool-flow-container {
  position: relative;
  width: 100%;
  height: 100%; /* Full viewport height for the component */
  overflow: hidden; /* Prevent content from overflowing */
}

/* Debug Panel Overlay Styles - Contained within component */
.debug-panel-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%; /* 50% of component height as specified */
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 999; /* Lower z-index since it's contained */
  display: flex;
  flex-direction: column;

  /* Slide-up animation when element enters DOM */
  animation: debugPanelSlideUp 300ms ease-out forwards;

  /* Ensure the panel starts from the correct position */
  transform: translateY(100%);
  opacity: 0;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  overflow: hidden;
}

/* Keyframe animation for sliding up from bottom when appearing */
@keyframes debugPanelSlideUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0%);
    opacity: 1;
  }
}

/* Ensure smooth performance by using transform instead of position changes */
.debug-panel-overlay * {
  /* Prevent child elements from interfering with parent animation */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Optimize animation performance */
.debug-panel-overlay {
  /* Enable hardware acceleration for smooth animations */
  will-change: transform, opacity;
  /* Use GPU acceleration */
  transform: translateZ(0);
}

:host-context(.dark) .debug-panel-overlay {
  background: rgba(16, 24, 40, 0.98);
  border-top-color: #7241FF;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

:host-context(.dark) .debug-panel-header {
  border-bottom-color: #374151;
  background: #1f2937;
}

.debug-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

:host-context(.dark) .debug-panel-header h3 {
  color: #f9fafb;
}

.debug-panel-close,
.debug-panel-refresh {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.debug-panel-close app-svg-icon,
.debug-panel-refresh app-svg-icon {
  width: 20px;
  height: 20px;
}

.debug-panel-close:hover,
.debug-panel-refresh:hover {
  background: #f3f4f6;
  color: #374151;
}

:host-context(.dark) .debug-panel-close,
:host-context(.dark) .debug-panel-refresh {
  color: #9ca3af;
}

:host-context(.dark) .debug-panel-close:hover,
:host-context(.dark) .debug-panel-refresh:hover {
  background: #374151;
  color: #f3f4f6;
}

.debug-panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  color: #374151;
}

:host-context(.dark) .debug-panel-content {
  color: #d1d5db;
}

/* Debug List Styles */
.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

:host-context(.dark) .debug-header {
  border-bottom-color: #374151;
}

.debug-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

:host-context(.dark) .debug-header h4 {
  color: #f9fafb;
}

.debug-count {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
}

:host-context(.dark) .debug-count {
  color: #9ca3af;
  background: #374151;
}

.debug-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.debug-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.2s ease;
}

:host-context(.dark) .debug-item {
  border-color: #374151;
  background: #1f2937;
}

.debug-item:hover {
  border-color: #7241FF;
  box-shadow: 0 2px 4px rgba(114, 65, 255, 0.1);
}

.debug-item.expanded {
  border-color: #7241FF;
}

.debug-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
}

.debug-item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.debug-time {
  font-size: 11px;
  color: #6b7280;
  font-family: 'Courier New', monospace;
}

:host-context(.dark) .debug-time {
  color: #9ca3af;
}

.debug-title {
  font-size: 13px;
  font-weight: 500;
  color: #111827;
}

:host-context(.dark) .debug-title {
  color: #f9fafb;
}

.debug-expand-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.debug-expand-btn:hover {
  background: #f3f4f6;
}

:host-context(.dark) .debug-expand-btn:hover {
  background: #374151;
}

.expand-icon {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.2s ease;
}

:host-context(.dark) .expand-icon {
  color: #9ca3af;
}

.debug-expand-btn.expanded .expand-icon {
  transform: rotate(180deg);
}

.debug-item-content {
  padding: 0 16px 16px 16px;
  border-top: 1px solid #f3f4f6;
  animation: slideDown 0.2s ease-out;
}

:host-context(.dark) .debug-item-content {
  border-top-color: #374151;
}

/* Secondary Fields Styles */
.debug-secondary-fields {
  background: #f8fafc;
  border-radius: 6px;
  padding: 12px;
  margin-top: 8px;
}

:host-context(.dark) .debug-secondary-fields {
  background: #111827;
}

.debug-field {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid #e5e7eb;
  gap: 12px;
}

.debug-field:last-child {
  border-bottom: none;
}

:host-context(.dark) .debug-field {
  border-bottom-color: #374151;
}

.debug-field-label {
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
  min-width: 100px;
  flex-shrink: 0;
}

:host-context(.dark) .debug-field-label {
  color: #9ca3af;
}

.debug-field-value {
  font-size: 11px;
  color: #374151;
  font-family: 'Courier New', monospace;
  word-break: break-all;
  text-align: right;
  flex: 1;
}

:host-context(.dark) .debug-field-value {
  color: #d1d5db;
}

/* Edge Formatting Styles */
.edge-formatted {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.edge-connection {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  background: #f1f5f9;
  border-radius: 4px;
  border-left: 3px solid #7241FF;
}

:host-context(.dark) .edge-connection {
  background: #0f172a;
  border-left-color: #7241FF;
}

.edge-handles,
.edge-properties,
.edge-data {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 6px 8px;
  background: #f8fafc;
  border-radius: 4px;
  border-left: 2px solid #e2e8f0;
}

:host-context(.dark) .edge-handles,
:host-context(.dark) .edge-properties,
:host-context(.dark) .edge-data {
  background: #1e293b;
  border-left-color: #475569;
}

.edge-primary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.edge-secondary {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  font-size: 10px;
}

.edge-label {
  font-weight: 600;
  color: #64748b;
  min-width: 80px;
  flex-shrink: 0;
}

:host-context(.dark) .edge-label {
  color: #94a3b8;
}

.edge-value {
  font-family: 'Courier New', monospace;
  color: #1e293b;
  word-break: break-all;
  text-align: right;
  flex: 1;
}

:host-context(.dark) .edge-value {
  color: #e2e8f0;
}

.edge-raw {
  font-family: 'Courier New', monospace;
  color: #6b7280;
  font-style: italic;
}

:host-context(.dark) .edge-raw {
  color: #9ca3af;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
  }
}
