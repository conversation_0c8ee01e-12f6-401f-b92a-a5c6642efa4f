@if (!uiStore.isHandset()) {
<div class="h-full flex flex-col overflow-hidden">
  <div class="flex items-start justify-between">
    <h1
      class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
    >
      API
      <a
        href="https://docs.dxconnect.lifesup.ai/studio/api"
        target="_blank"
        class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
      >
        Learn more
      </a>
    </h1>
    <app-flow-env-select></app-flow-env-select>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-7 gap-y-4 md:gap-x-4 h-full mt-6">
    <div class="col-span-2 flex flex-col w-full">
      <!--      list api-->
      <div
        class="rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
      >
        <div
          class="p-4 flex flex-wrap 2xl:flex-nowrap gap-2 2xl:gap-0 2xl:space-x-2 items-center justify-between border-b border-primary-border dark:border-dark-primary-border"
        >
          <dx-form-field
            class="w-full"
            id="search"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
            <input
              dxInput
              [(ngModel)]="searchModel.key_word"
              (ngModelChange)="searchSubject.next($event)"
              [type]="'text'"
              placeholder="Search by Name"
            />
          </dx-form-field>
          <button
            class="flex-shrink-0 w-full 2xl:w-fit"
            dx-button="filled"
            [disabled]="studioStatus() === STUDIO_STATUS.LIVE"
            (click)="createApi()"
          >
            <div class="flex items-center justify-between space-x-1">
              <app-svg-icon
                type="icPlus"
                class="w-6 h-6 text-white"
              ></app-svg-icon>
              <span class="text-sm">Add API</span>
            </div>
          </button>
        </div>

        <div
          class="h-full list-apis flex flex-col justify-start overflow-y-auto"
        >
          @for (data of listApi(); track $index; let first = $first, last =
          $last) {
          <div
            class="p-4 flex flex-col space-y-3 hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer group"
            [ngClass]="{
              'border-b border-primary-border dark:border-dark-primary-border':
                !last,
              'rounded-b-2xl': last,
              'bg-base-400-hover dark:bg-dark-base-400-hover':
                data.id === apiSelectedId()
            }"
            (click)="selectApi(data?.id)"
          >
            <div class="space-x-3 flex items-end justify-between">
              <div
                class="flex space-x-3 items-center justify-start !text-neutral-content dark:!text-dark-neutral-content group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
              >
                <app-svg-icon
                  type="icApi"
                  class="w-5 h-5"
                  [ngClass]="{
                    '!text-primary-hover dark:!text-dark-primary-hover':
                      data.id === apiSelectedId()
                  }"
                ></app-svg-icon>
                <div
                  class="truncate max-w-[15dvw] line-clamp-1"
                  [ngClass]="{
                    '!text-base-content dark:!text-dark-base-content':
                      data.id === apiSelectedId()
                  }"
                >
                  {{ data.name }}
                </div>
              </div>
              @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
              <div class="flex space-x-3 items-center justify-end">
                <app-svg-icon
                  type="icEdit"
                  class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer hover:text-primary-hover dark:hover:text-dark-primary-hover"
                  (click)="editApi(data, $event)"
                ></app-svg-icon>
                <app-svg-icon
                  type="icTrash"
                  class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                  (click)="deleteApi(data, $event)"
                ></app-svg-icon>
              </div>
              }
            </div>
            @if (data.description) {
            <div
              class="text-sm text-neutral-content dark:text-dark-neutral-content ml-8"
            >
              {{ data.description }}
            </div>
            }
          </div>
          } @empty {
          <div class="flex items-center justify-center py-4">
            <div
              class="text-neutral-content dark:text-dark-neutral-content italic text-[15px]"
            >
              No data available
            </div>
          </div>
          }
        </div>
      </div>
    </div>
    <!--    edit api-->
    <div
      class="col-span-5 flex flex-col w-full h-full rounded-2xl border border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
    >
      <div
        class="flex flex-row overflow-x-auto items-end bg-base-100 dark:bg-dark-base-100 rounded-t-2xl pr-4"
      >
        @for (tab of listApiTab(); track $index) {
        <div class="flex items-end">
          @if (tab.id === apiSelectedId()) {
          <app-svg-icon
            type="icRightCorner"
            class="w-[11px] h-4 !text-base-200 dark:!text-dark-base-200"
          ></app-svg-icon>
          }
          <div
            class="px-4 py-3 space-x-2 flex items-center text-neutral-content dark:text-dark-neutral-content cursor-pointer"
            [ngClass]="{
              'bg-base-200 dark:bg-dark-base-200 text-base-content dark:text-dark-base-content':
                tab.id === apiSelectedId(),
              'rounded-t-2xl group': tab.id == apiSelectedId(),
              'text-neutral-content dark:text-dark-neutral-content':
                tab.id !== apiSelectedId()
            }"
            (click)="selectApi(tab.id)"
          >
            <app-svg-icon
              type="icApi"
              class="w-5 h-5 group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
            ></app-svg-icon>
            <div class="text-base font-semibold line-clamp-1">
              {{ tab.name }}
            </div>
            @if (tab.isDirty) {
            <div class="w-3 h-3 bg-error bg-dark-error rounded-full"></div>
            } @else { @if (!(listApi().length === 1 || listApiTab().length ===
            1)) {
            <app-svg-icon
              type="icClose"
              class="w-5 h-5 cursor-pointer"
              (click)="removeApiSelectedTab($index, $event)"
            ></app-svg-icon>
            } }
          </div>
          @if (tab.id === apiSelectedId()) {
          <app-svg-icon
            type="icRightCorner"
            class="w-[11px] h-4 scale-x-[-1] !text-base-200 dark:!text-dark-base-200"
          ></app-svg-icon>
          }
        </div>
        <div
          class="items-center justify-center flex h-full text-primary-border dark:text-dark-primary-border text-xl"
        >
          |
        </div>
        } @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
        <div
          class="flex h-12 w-12 items-center justify-center !text-neutral-content dark:!text-dark-neutral-content"
        >
          <app-svg-icon
            type="icPlus"
            class="w-6 h-6 cursor-pointer"
            (click)="createApi()"
          ></app-svg-icon>
        </div>
        }
      </div>

      <div class="flex-1 w-full overflow-y-auto flex flex-col p-6">
        @if (apiSelected() && listApiTab().length > 0) {
        <div class="w-full flex flex-col justify-between">
          <div
            class="flex items-center space-x-3 mb-4 justify-start"
            [formGroup]="formGroupApiDetail"
          >
            <dx-form-field
              [style.margin-bottom]="0"
              [hideRequiredMarker]="true"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <!--                <input dxInput formControlName="method" placeholder="API Method">-->
              <dx-select
                formControlName="method"
                [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
              >
                @for (method of REST_API_METHOD_LIST; track $index) {
                <dx-option [value]="method.value">
                  {{ method.label }}
                </dx-option>
                }
              </dx-select>
            </dx-form-field>
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [hideRequiredMarker]="true"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                formControlName="url"
                placeholder="Request URL"
                [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
              />
            </dx-form-field>
            <button
              dxLoadingButton="elevated"
              class="flex justify-end space-x-2"
              [disabled]="formGroupApiDetail.invalid"
              [loading]="isSendingAPI()"
              (click)="testApi()"
            >
              <div
                class="flex items-center space-x-1 ml-2 !text-primary dark:!text-dark-primary"
              >
                <app-svg-icon
                  dxPrefix
                  type="icSend"
                  class="w-6 h-6"
                ></app-svg-icon>
                <span class="text-sm">Send</span>
              </div>
            </button>
            <button
              dxLoadingButton="filled"
              [loading]="isSubmitting()"
              [disabled]="
                formGroupApiDetail.invalid ||
                studioStoreStatus() === STUDIO_STATUS.LIVE
              "
              (click)="saveApi(true)"
            >
              Save
            </button>
          </div>
        </div>
        <div
          class="flex-grow w-full flex flex-col items-stretch"
          style="height: calc(100vh - 500px)"
        >
          <as-split
            direction="vertical"
            [gutterSize]="10"
            [style.--_as-gutter-background-color]="'transparent'"
          >
            <as-split-area
              class="border-b border-primary-border dark:border-dark-primary-border split-area-content"
              size="50"
            >
              <div class="w-full h-full">
                <dx-tab-group
                  [(selectedIndex)]="tabIndex"
                  dx-stretch-tabs="false"
                  dx-align-tabs="start"
                  class="h-full tab-api-detail"
                >
                  <dx-tab label="Params">
                    <div
                      class="w-full h-full overflow-y-auto grid grid-cols-3 gap-x-6 pt-2 px-2"
                    >
                      <div
                        class="col-span-1 text-sm text-neutral-content dark:text-dark-neutral-content"
                      >
                        Key
                      </div>
                      <div
                        class="col-span-2 text-sm text-neutral-content dark:text-dark-neutral-content"
                      >
                        Value
                      </div>
                      @if (parameterData().length > 0) { @for (param of
                      parameterData(); track $index) {
                      <dx-form-field
                        class="col-span-1 mt-2"
                        [style.margin-bottom]="0"
                        [style.--dx-form-field-label-offset-y]="0"
                        [subscriptHidden]="true"
                      >
                        <input
                          dxInput
                          placeholder="Key"
                          [ngModel]="param.key"
                          (ngModelChange)="onChangeParam($event, $index, 'key')"
                          [disabled]="
                            studioStoreStatus() === STUDIO_STATUS.LIVE
                          "
                        />
                      </dx-form-field>
                      <div
                        class="col-span-2 mt-2 flex space-x-4 items-center justify-between"
                      >
                        <dx-form-field
                          class="w-full"
                          [style.margin-bottom]="0"
                          [style.--dx-form-field-label-offset-y]="0"
                          [subscriptHidden]="true"
                        >
                          <input
                            dxInput
                            placeholder="Value"
                            [ngModel]="param.value"
                            type="text"
                            (ngModelChange)="
                              onChangeParam($event, $index, 'value')
                            "
                            [disabled]="
                              studioStoreStatus() === STUDIO_STATUS.LIVE
                            "
                          />
                        </dx-form-field>
                        @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                        <app-svg-icon
                          type="icTrash"
                          class="w-6 h-6 cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
                          (click)="removeParam($index)"
                        ></app-svg-icon>
                        }
                      </div>
                      } } @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                      <button
                        id="btn-add-new-param"
                        type="button"
                        (click)="addParam()"
                        class="!text-primary dark:!text-dark-primary text-[14px] flex items-center space-x-2 mt-4"
                      >
                        <app-svg-icon
                          type="icPlus"
                          class="w-6 h-6"
                        ></app-svg-icon>
                        <div>Add params</div>
                      </button>
                      }
                    </div>
                  </dx-tab>
                  <dx-tab label="Headers">
                    <div
                      class="w-full h-full overflow-y-auto grid grid-cols-3 gap-x-6 pt-2 px-2"
                    >
                      <div
                        class="col-span-1 text-sm text-neutral-content dark:text-dark-neutral-content"
                      >
                        Key
                      </div>
                      <div
                        class="col-span-2 text-sm text-neutral-content dark:text-dark-neutral-content"
                      >
                        Value
                      </div>

                      <!--                        <div class="">-->
                      @for (header of headerData(); track $index) {
                      <dx-form-field
                        class="col-span-1 mt-2"
                        [style.margin-bottom]="0"
                        [style.--dx-form-field-label-offset-y]="0"
                        [subscriptHidden]="true"
                      >
                        <input
                          dxInput
                          placeholder="Key"
                          [(ngModel)]="header.key"
                          [ngModelOptions]="{ standalone: true }"
                          [disabled]="
                            studioStoreStatus() === STUDIO_STATUS.LIVE
                          "
                        />
                      </dx-form-field>
                      <div
                        class="col-span-2 mt-2 flex space-x-4 items-center justify-between"
                      >
                        <dx-form-field
                          class="w-full"
                          [style.margin-bottom]="0"
                          [style.--dx-form-field-label-offset-y]="0"
                          [subscriptHidden]="true"
                        >
                          <input
                            dxInput
                            placeholder="Value"
                            [(ngModel)]="header.value"
                            [ngModelOptions]="{ standalone: true }"
                            [disabled]="
                              studioStoreStatus() === STUDIO_STATUS.LIVE
                            "
                          />
                        </dx-form-field>
                        @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                        <app-svg-icon
                          type="icTrash"
                          class="w-6 h-6 cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
                          (click)="removeHeader($index)"
                        ></app-svg-icon>
                        }
                      </div>
                      } @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                      <button
                        id="btn-add-new-param"
                        type="button"
                        (click)="addHeader()"
                        class="!text-primary dark:!text-dark-primary text-[14px] flex items-center space-x-2 mt-4"
                      >
                        <app-svg-icon
                          type="icPlus"
                          class="w-6 h-6"
                        ></app-svg-icon>
                        <div>Add headers</div>
                      </button>
                      }
                    </div>
                  </dx-tab>
                  <dx-tab
                    label="Body"
                    [disabled]="
                      formGroupApiDetail.get('method')?.value !== 'POST' &&
                      formGroupApiDetail.get('method')?.value !== 'PUT'
                    "
                  >
                    <div
                      class="min-h-[14dvw] h-full bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                    >
                      <ngx-monaco-editor
                        [options]="editorOptionsBody()"
                        [ngModel]="body()"
                        (ngModelChange)="onChangeBody($event)"
                      ></ngx-monaco-editor>
                    </div>
                  </dx-tab>
                  <dx-tab label="Scripts">
                    <div
                      class="min-h-[27.5dvh] w-full flex flex-col overflow-y-auto"
                    >
                      <div class="grid flex-1 grid-cols-6 gap-3">
                        <div
                          class="col-span-1 flex flex-col h-full text-sm text-neutral-content dark:text-dark-neutral-content"
                        >
                          <div
                            class="px-4 py-2 cursor-pointer text-sm"
                            [ngClass]="{
                              'bg-base-400 dark:bg-dark-base-400 rounded-[12px]':
                                selectedTab === 'pre-request'
                            }"
                            (click)="selectTab('pre-request')"
                          >
                            Pre-request
                          </div>
                          <div
                            class="px-4 py-2 cursor-pointer text-sm"
                            [ngClass]="{
                              'bg-base-400 dark:bg-dark-base-400 rounded-[12px]':
                                selectedTab === 'post-response'
                            }"
                            (click)="selectTab('post-response')"
                          >
                            Post-response
                          </div>
                        </div>
                        <div class="col-span-5 flex flex-col">
                          <div
                            class="h-full bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                          >
                            @if (selectedTab === 'pre-request') {
                            <ngx-monaco-editor
                              [options]="editorOptionsScripts()"
                              [ngModel]="preRequest()"
                              (ngModelChange)="onChangePreRequest($event)"
                            ></ngx-monaco-editor>
                            } @else if (selectedTab === 'post-response') {
                            <ngx-monaco-editor
                              [options]="editorOptionsScripts()"
                              [ngModel]="postResponse()"
                              (ngModelChange)="onChangePostResponse($event)"
                            ></ngx-monaco-editor>
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </dx-tab>
                </dx-tab-group>
              </div>
            </as-split-area>
            <as-split-area size="50" class="split-area-content">
              <div class="h-full w-full flex flex-col items-stretch pt-2">
                <div
                  class="text-[15px] text-neutral-content dark:text-dark-neutral-content"
                >
                  Response
                </div>
                <div
                  class="min-h-[16dvw] mt-2 bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                >
                  <ngx-monaco-editor
                    [options]="editorOptionsResponse()"
                    [ngModel]="response"
                  ></ngx-monaco-editor>
                </div>
              </div>
            </as-split-area>
          </as-split>
        </div>
        } @else {
        <div
          class="w-full h-full flex items-center justify-center text-neutral-content dark:text-dark-neutral-content"
        >
          Start adding your first Api to select
        </div>
        }
      </div>
    </div>
  </div>
</div>
} @else {
<app-mobile-header [title]="title" [backFn]="backFn">
  <div mHeaderLeft class="flex items-center">
    <a
      href="https://docs.dxconnect.lifesup.ai/studio/api"
      target="_blank"
      class="flex items-center"
    >
      <app-svg-icon type="icInfo" class="w-4 h-4 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
    </a>
  </div>
  <app-flow-env-select mHeaderRight></app-flow-env-select>

</app-mobile-header>
<div
  class="w-full h-[100dvh] flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
>
  <div
    (click)="viewListApi.set(true)"
    class="px-2 py-3 flex items-center justify-between bg-base-400 dark:bg-dark-base-400 rounded-xl border border-primary-border dark:border-dark-primary-border"
  >
    <app-svg-icon
      type="icApi"
      class="w-5 h-5 !text-primary dark:!text-dark-primary"
    ></app-svg-icon>
    <div
      class="flex-1 text-center text-normal font-bold text-base-content dark:text-dark-base-content line-clamp-1"
    >
      {{ this.apiSelected()?.name || "Select an Api" }}
    </div>
    <app-svg-icon
      type="icChevronDown"
      class="w-5 h-5 !text-primary dark:!text-dark-primary cursor-pointer"
    ></app-svg-icon>
  </div>
  <div
    class="col-span-5 flex flex-col w-full h-full rounded-2xl border border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <div class="flex-1 overflow-y-auto flex flex-col p-4">
      @if (apiSelected() && listApiTab().length > 0) {
      <div class="w-full h-full relative flex flex-col">
        <div class="flex-1 w-full overflow-y-auto flex flex-col">
          @if (apiSelected() && listApiTab().length > 0) {
          <div class="w-full flex flex-col justify-between">
            <div
              class="flex flex-col items-center gap-3 mb-4 justify-start"
              [formGroup]="formGroupApiDetail"
            >
              <dx-form-field
                class="w-full"
                [style.margin-bottom]="0"
                [hideRequiredMarker]="true"
                [style.--dx-form-field-label-offset-y]="0"
                [subscriptHidden]="true"
              >
                <!--                <input dxInput formControlName="method" placeholder="API Method">-->
                <dx-select
                  formControlName="method"
                  [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
                >
                  @for (method of REST_API_METHOD_LIST; track $index) {
                  <dx-option [value]="method.value">
                    {{ method.label }}
                  </dx-option>
                  }
                </dx-select>
              </dx-form-field>
              <dx-form-field
                class="w-full"
                [style.margin-bottom]="0"
                [style.--dx-form-field-label-offset-y]="0"
                [hideRequiredMarker]="true"
                [subscriptHidden]="true"
              >
                <input
                  dxInput
                  formControlName="url"
                  placeholder="Request URL"
                  [disabled]="studioStoreStatus() === STUDIO_STATUS.LIVE"
                />
              </dx-form-field>
              <div class="flex gap-3 w-full">
                <button
                  dxLoadingButton="elevated"
                  class="flex justify-end space-x-2 w-full"
                  [disabled]="formGroupApiDetail.invalid"
                  [loading]="isSendingAPI()"
                  (click)="testApi()"
                >
                  <div class="flex items-center space-x-2 m-2">
                    <app-svg-icon
                      dxPrefix
                      type="icSend"
                      class="w-6 h-6 !text-primary dark:!text-dark-primary"
                    ></app-svg-icon>
                    <span class="text-sm text-primary dark:text-dark-primary"
                      >Send</span
                    >
                  </div>
                </button>
                <button
                  class="w-full"
                  dxLoadingButton="filled"
                  [loading]="isSubmitting()"
                  [disabled]="
                    formGroupApiDetail.invalid ||
                    studioStoreStatus() === STUDIO_STATUS.LIVE
                  "
                  (click)="saveApi(true)"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
          <div
            class="flex-grow w-full flex flex-col items-stretch"
            style="height: calc(100vh - 500px)"
          >
            <as-split
              direction="vertical"
              [gutterSize]="10"
              [style.--_as-gutter-background-color]="'transparent'"
            >
              <as-split-area
                class="border-b border-primary-border dark:border-dark-primary-border split-area-content"
                size="50"
              >
                <div class="w-full h-full">
                  <dx-tab-group
                    [(selectedIndex)]="tabIndex"
                    dx-stretch-tabs="false"
                    dx-align-tabs="start"
                    class="h-full tab-api-detail"
                  >
                    <dx-tab label="Params">
                      <div
                        class="w-full h-full overflow-y-auto flex flex-col gap-x-6 pt-2 px-2"
                      >
                        @if (parameterData().length > 0) { @for (param of
                        parameterData(); track $index; let first = $first) {
                        <div
                          class="w-full flex flex-col py-4 gap-3 border-b border-primary-border dark:border-dark-primary-border"
                          [ngClass]="{ 'pt-0': first }"
                        >
                          <div
                            class="w-full flex justify-between items-center font-semibold text-base-content dark:text-dark-base-content"
                          >
                            Param {{ $index + 1 }}
                            @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                            <app-svg-icon
                              type="icTrash"
                              class="w-6 h-6 cursor-pointer justify-end !text-neutral-content dark:!text-dark-neutral-content"
                              (click)="removeParam($index)"
                            ></app-svg-icon>
                            }
                          </div>
                          <dx-form-field
                            class="w-full"
                            [style.margin-bottom]="0"
                            [subscriptHidden]="true"
                          >
                            <dx-label>Key</dx-label>
                            <input
                              dxInput
                              placeholder="Key"
                              [ngModel]="param.key"
                              (ngModelChange)="
                                onChangeParam($event, $index, 'key')
                              "
                              [disabled]="
                                studioStoreStatus() === STUDIO_STATUS.LIVE
                              "
                            />
                          </dx-form-field>
                          <dx-form-field
                            class="w-full"
                            [style.margin-bottom]="0"
                            [subscriptHidden]="true"
                          >
                            <dx-label>Value</dx-label>
                            <input
                              dxInput
                              placeholder="Value"
                              [ngModel]="param.value"
                              type="text"
                              (ngModelChange)="
                                onChangeParam($event, $index, 'value')
                              "
                              [disabled]="
                                studioStoreStatus() === STUDIO_STATUS.LIVE
                              "
                            />
                          </dx-form-field>
                        </div>
                        } } @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                        <button
                          id="btn-add-new-param"
                          type="button"
                          (click)="addParam()"
                          class="!text-primary dark:!text-dark-primary text-[14px] flex items-center space-x-2 mt-4"
                        >
                          <app-svg-icon
                            type="icPlus"
                            class="w-6 h-6"
                          ></app-svg-icon>
                          <div>Add params</div>
                        </button>
                        }
                      </div>
                    </dx-tab>
                    <dx-tab label="Headers">
                      <div
                        class="w-full h-full overflow-y-auto flex flex-col gap-x-6 pt-2 px-2"
                      >
                        @for (header of headerData(); track $index; let first =
                        $first) {
                        <div
                          class="w-full flex flex-col py-4 gap-3 border-b border-primary-border dark:border-dark-primary-border"
                          [ngClass]="{ 'pt-0': first }"
                        >
                          <div
                            class="w-full flex justify-between items-center font-semibold text-base-content dark:text-dark-base-content"
                          >
                            Header {{ $index + 1 }}
                            @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                            <app-svg-icon
                              type="icTrash"
                              class="w-6 h-6 cursor-pointer justify-end !text-neutral-content dark:!text-dark-neutral-content"
                              (click)="removeHeader($index)"
                            ></app-svg-icon>
                            }
                          </div>
                          <dx-form-field
                            class="w-full"
                            [style.margin-bottom]="0"
                            [subscriptHidden]="true"
                          >
                            <dx-label>Key</dx-label>
                            <input
                              dxInput
                              placeholder="Key"
                              [(ngModel)]="header.key"
                              [ngModelOptions]="{ standalone: true }"
                              [disabled]="
                                studioStoreStatus() === STUDIO_STATUS.LIVE
                              "
                            />
                          </dx-form-field>
                          <dx-form-field
                            class="w-full"
                            [style.margin-bottom]="0"
                            [subscriptHidden]="true"
                          >
                            <dx-label>Value</dx-label>
                            <input
                              dxInput
                              placeholder="Value"
                              [(ngModel)]="header.value"
                              [ngModelOptions]="{ standalone: true }"
                              [disabled]="
                                studioStoreStatus() === STUDIO_STATUS.LIVE
                              "
                            />
                          </dx-form-field>
                        </div>
                        } @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
                        <button
                          id="btn-add-new-param"
                          type="button"
                          (click)="addHeader()"
                          class="!text-primary dark:!text-dark-primary text-[14px] flex items-center space-x-2 mt-4"
                        >
                          <app-svg-icon
                            type="icPlus"
                            class="w-6 h-6"
                          ></app-svg-icon>
                          <div>Add headers</div>
                        </button>
                        }
                      </div>
                    </dx-tab>
                    <dx-tab
                      label="Body"
                      [disabled]="
                        formGroupApiDetail.get('method')?.value !== 'POST' &&
                        formGroupApiDetail.get('method')?.value !== 'PUT'
                      "
                    >
                      <div
                        class="min-h-[14dvw] h-full bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                      >
                        <ngx-monaco-editor
                          [options]="editorOptionsBody()"
                          [ngModel]="body()"
                          (ngModelChange)="onChangeBody($event)"
                        ></ngx-monaco-editor>
                      </div>
                    </dx-tab>
                    <dx-tab label="Scripts">
                      <div
                        class="min-h-[27.5dvh] w-full flex flex-col overflow-y-auto"
                      >
                        <div class="flex-1 flex flex-col gap-3">
                          <div
                            class="flex h-full text-sm text-neutral-content dark:text-dark-neutral-content"
                          >
                            <div
                              class="px-4 py-2 cursor-pointer text-sm"
                              [ngClass]="{
                                'bg-base-400 dark:bg-dark-base-400 rounded-[12px]':
                                  selectedTab === 'pre-request'
                              }"
                              (click)="selectTab('pre-request')"
                            >
                              Pre-request
                            </div>
                            <div
                              class="px-4 py-2 cursor-pointer text-sm"
                              [ngClass]="{
                                'bg-base-400 dark:bg-dark-base-400 rounded-[12px]':
                                  selectedTab === 'post-response'
                              }"
                              (click)="selectTab('post-response')"
                            >
                              Post-response
                            </div>
                          </div>
                          <div class="flex flex-col">
                            <div
                              class="h-full bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                            >
                              @if (selectedTab === 'pre-request') {
                              <ngx-monaco-editor
                                [options]="editorOptionsScripts()"
                                [ngModel]="preRequest()"
                                (ngModelChange)="onChangePreRequest($event)"
                              ></ngx-monaco-editor>
                              } @else if (selectedTab === 'post-response') {
                              <ngx-monaco-editor
                                [options]="editorOptionsScripts()"
                                [ngModel]="postResponse()"
                                (ngModelChange)="onChangePostResponse($event)"
                              ></ngx-monaco-editor>
                              }
                            </div>
                          </div>
                        </div>
                      </div>
                    </dx-tab>
                  </dx-tab-group>
                </div>
              </as-split-area>
              <as-split-area size="50" class="split-area-content">
                <div class="h-full w-full flex flex-col items-stretch pt-2">
                  <div
                    class="text-[15px] text-neutral-content dark:text-dark-neutral-content"
                  >
                    Response
                  </div>
                  <div
                    class="min-h-[16dvw] mt-2 bg-white dark:bg-dark-secondary-background py-6 rounded-xl border border-primary-border dark:border-dark-primary-border"
                  >
                    <ngx-monaco-editor
                      [options]="editorOptionsResponse()"
                      [ngModel]="response"
                    ></ngx-monaco-editor>
                  </div>
                </div>
              </as-split-area>
            </as-split>
          </div>
          } @else {
          <div
            class="w-full h-full flex items-center justify-center text-neutral-content dark:text-dark-neutral-content"
          >
            Start adding your first Api to select
          </div>
          }
        </div>
      </div>
      } @else {
      <div
        class="w-full h-full flex items-center justify-center text-neutral-content dark:text-dark-neutral-content"
      >
        Start adding your first Function to select
      </div>
      }
    </div>
  </div>
</div>
<!--  edit api-->
<app-mobile-drawer
  [visible]="viewListApi()"
  [direction]="'btt'"
  [size]="'60%'"
  (visibleChange)="viewListApi.set(false)"
>
  <div class="w-full h-full flex flex-col">
    <!--  header-->
    <div
      class="w-full h-12 px-4 py-3 flex items-center justify-between bg-base-200 dark:bg-dark-base-200"
    >
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
        (click)="viewListApi.set(false)"
      ></app-svg-icon>
      <div
        class="text-[16px] font-bold text-base-content dark:text-dark-base-content"
      >
        API
      </div>
      <div class="flex items-center justify-end w-6 h-6">
        @if (studioStoreStatus() !== STUDIO_STATUS.LIVE) {
        <app-svg-icon
          type="icPlus"
          class="w-6 h-6 !text-primary"
          (click)="createApi()"
        ></app-svg-icon>
        }
      </div>
    </div>

    <div
      class="flex-1 w-full p-4 flex flex-col gap-y-3 bg-base-100 dark:bg-dark-base-100"
    >
      <div class="col-span-2 flex flex-col w-full">
        <div>
          <div
            class="flex flex-wrap 2xl:flex-nowrap gap-2 2xl:gap-0 2xl:space-x-2 items-center justify-between"
          >
            <dx-form-field
              class="w-full"
              id="search"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <app-svg-icon
                dxPrefix
                type="icSearch"
                class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
              ></app-svg-icon>
              <input
                dxInput
                [(ngModel)]="searchModel.key_word"
                (ngModelChange)="searchSubject.next($event)"
                [type]="'text'"
                placeholder="Search by Name"
              />
            </dx-form-field>
          </div>

          <div class="h-full list-functions overflow-y-auto">
            @for (data of listApi(); track $index; let first = $first, last =
            $last) {
            <div
              class="py-4 lg:p-4 space-x-3 flex items-center justify-between hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover cursor-pointer group"
              [ngClass]="{
              'border-b border-primary-border dark:border-dark-primary-border':
                !last,
              'rounded-b-2xl': last,
            }"
              (click)="selectApi(data?.id); viewListApi.set(false)"
            >
              <div
                class="flex space-x-3 items-center justify-start !text-neutral-content dark:!text-dark-neutral-content group-hover:!text-primary-hover dark:group-hover:!text-dark-primary-hover"
              >
                <app-svg-icon
                  type="icApi"
                  class="w-5 h-5"
                  [ngClass]="{
                    '!text-primary-hover dark:!text-dark-primary-hover':
                      data.id === apiSelectedId()
                  }"
                ></app-svg-icon>
                <div
                  class="truncate max-w-[65dvw] text-ellipsis line-clamp-1"
                  [ngClass]="{
                    '!text-base-content dark:!text-dark-base-content':
                      data.id === apiSelectedId()
                  }"
                >
                  {{ data.name }}
                </div>
              </div>
              @if (studioStoreStatus() === STUDIO_STATUS.DEV) {
              <div class="flex space-x-3 items-center justify-end">
                <app-svg-icon
                  type="icEdit"
                  class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer hover:text-primary-hover dark:hover:text-dark-primary-hover"
                  (click)="editApi(data, $event)"
                ></app-svg-icon>
                <app-svg-icon
                  type="icTrash"
                  class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                  (click)="deleteApi(data, $event)"
                ></app-svg-icon>
              </div>
              }
            </div>
            } @empty {
            <div class="flex items-center justify-center py-4">
              <div
                class="text-neutral-content dark:text-dark-neutral-content italic text-[15px]"
              >
                No data available
              </div>
            </div>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</app-mobile-drawer>

}
