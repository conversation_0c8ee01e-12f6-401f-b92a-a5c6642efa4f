import { NgClass } from '@angular/common';
import {
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { APP_ROUTES, STUDIO_STATUS } from '@core/constants';
import { StudioStore, UIStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxOption,
  DxPrefix,
  DxSelect,
  DxSnackBar,
  DxTab,
  DxTabGroup,
} from '@dx-ui/ui';
import { provideIcons } from '@ng-icons/core';
import {
  heroPencilSquareMini,
  heroPlusMini,
  heroTrashMini,
} from '@ng-icons/heroicons/mini';
import { heroPlusCircle } from '@ng-icons/heroicons/outline';
import {
  heroPlusSolid,
  heroXCircleSolid,
  heroXMarkSolid,
} from '@ng-icons/heroicons/solid';
import { REST_API_METHOD } from '@shared/app.constant';
import {
  ConfirmDialogComponent,
  FlowEnvSelectComponent,
  MobileDrawerComponent,
  MobileHeaderComponent,
  SvgIconComponent,
} from '@shared/components';
import {MHeaderLeftDirective, MHeaderRightDirective} from '@shared/directives';
import { IApi, IApiFilter } from '@shared/models';
import { ApiService } from '@shared/services';
import { ThemeService } from '@shared/services/theme.service';
import { CustomValidators } from '@shared/validators';
import { AddOrEditApiComponent } from '@views/studio/components/add-or-edit-api/add-or-edit-api.component';
import { SplitAreaComponent, SplitComponent } from 'angular-split';
import { MonacoEditorModule } from 'ngx-monaco-editor-v2';
import { catchError, debounceTime, of, Subject, Subscription } from 'rxjs';

@Component({
  selector: 'app-api-define',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    NgClass,
    MonacoEditorModule,
    DxButton,
    DxFormField,
    DxInput,
    DxPrefix,
    SvgIconComponent,
    SplitComponent,
    SplitAreaComponent,
    DxSelect,
    DxOption,
    DxLoadingButton,
    DxTabGroup,
    DxTab,
    DxLabel,
    FlowEnvSelectComponent,
    MHeaderRightDirective,
    MobileHeaderComponent,
    MobileDrawerComponent,
    MHeaderLeftDirective,
  ],
  templateUrl: './api-define.component.html',
  styleUrl: './api-define.component.css',
  providers: [
    provideIcons({
      heroPlusSolid,
      heroPlusMini,
      heroPlusCircle,
      heroTrashMini,
      heroPencilSquareMini,
      heroXMarkSolid,
      heroXCircleSolid,
    }),
  ],
})
export class ApiDefineComponent implements OnInit, OnDestroy {
  formGroup: FormGroup = new FormGroup({});
  viewListApi = signal<boolean>(false);
  apiSelectedId = signal<number | undefined>(undefined);
  apiSelected = signal<IApi | undefined>(undefined);
  listApi = signal<IApi[]>([]);
  listApiTab = signal<IApi[]>([]);
  api = signal<IApi | undefined>(undefined);
  isSubmitting = signal<boolean>(false);
  searchModel: IApiFilter = {
    key_word: '',
  };
  headerData = signal<{ value: unknown; key: string }[]>([]);
  parameterData = signal<{ key: string; value: unknown }[]>([]);
  fb = inject(FormBuilder);

  formGroupApiDetail: FormGroup = this.fb.group({
    id: [null],
    ai_id: [null],
    name: [
      null,
      [Validators.required, CustomValidators.noWhitespaceValidator()],
    ],
    description: [null],
    method: [null, Validators.required],
    url: [
      null,
      [
        Validators.required,
        CustomValidators.noWhitespaceValidator(),
        CustomValidators.urlValidator(),
      ],
    ],
    headers: [null],
    parameters: [null],
    body: [null],
    pre_request: [null],
    post_response: [null],
  });

  tabIndex: number = 0;
  response: string = '{}';
  body = signal('{}');
  preRequest = signal('');
  postResponse = signal('');

  isSendingAPI = signal(false);
  selectedTab: string = 'pre-request'; // Giá trị ban đầu
  studioStore = inject(StudioStore);
  themeService = inject(ThemeService);
  theme = computed(() => this.themeService.theme());
  studioStoreStatus = computed(() => this.studioStore.status());
  readonly studioStatus = computed(() => this.studioStore.status());

  readonly STUDIO_STATUS = STUDIO_STATUS;
  readonly REST_API_METHOD_LIST = [
    {
      value: REST_API_METHOD.GET,
      label: 'GET',
    },
    {
      value: REST_API_METHOD.POST,
      label: REST_API_METHOD.POST,
    },
    {
      value: REST_API_METHOD.PUT,
      label: REST_API_METHOD.PUT,
    },
    {
      value: REST_API_METHOD.DELETE,
      label: REST_API_METHOD.DELETE,
    },
  ];
  readonly listType: Object[] = [
    { label: 'string' },
    { label: 'number' },
    { label: 'array' },
  ];
  editorOptionsResponse = computed(() => ({
    theme: this.theme() === 'dark' ? 'vs-dark' : 'vs-light',
    language: 'json',
    automaticLayout: true,
    readOnly: true,
  }));
  editorOptionsBody = computed(() => ({
    theme: this.theme() === 'dark' ? 'vs-dark' : 'vs-light',
    language: 'json',
    automaticLayout: true,
    readOnly: this.studioStore.status() === STUDIO_STATUS.LIVE,
  }));
  editorOptionsScripts = computed(() => ({
    theme: this.theme() === 'dark' ? 'vs-dark' : 'vs-light',
    language: 'python',
    automaticLayout: true,
    readOnly: this.studioStore.status() === STUDIO_STATUS.LIVE,
  }));
  title: string = 'APIs';
  searchSubject = new Subject<string>();
  private subscription?: Subscription;
  private apiService: ApiService = inject(ApiService);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  uiStore = inject(UIStore);
  backFn = () => {};

  ngOnInit(): void {
    this.getListApi();
    this.route.queryParams.subscribe((params: Params) => {
      this.searchModel.key_word = params['key_word'] ?? '';
      this.doSearch();
    });
    this.searchSubject
      .pipe(debounceTime(300))
      .subscribe(() => this.updateUrlParams());

    this.subscription = this.formGroupApiDetail.valueChanges.subscribe({
      next: (formValue) => {
        // this.setDirtyState(formValue.id, true);
        const urlParams = formValue.url?.split('?') || [];
        if (urlParams && urlParams.length && urlParams.length > 1) {
          const paramsFormUrl = urlParams[1].split('&');
          if (paramsFormUrl && paramsFormUrl.length) {
            this.parameterData.set(
              paramsFormUrl.map((param: string) => ({
                key: param.split('=')[0],
                value: param.split('=')[1],
              }))
            );
          }
        } else {
          this.parameterData.set([]);
        }
      },
    });
    this.backFn = () => {
      this.router.navigate([APP_ROUTES.MENU]);
    };
  }

  ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe();
  }

  doSearch(event?: any) {
    this.getListApi();
  }

  onChangeParam(value: any, index?: number, key?: string) {
    if (value) {
      this.parameterData.update((data) =>
        data.map((item, idx) =>
          idx === index ? { ...item, [key!]: value } : item
        )
      );
      this.updateParam();
      this.setDirtyState(this.apiSelectedId()!, true);
    }
  }

  onChangeBody(value: any) {
    if (value && value !== this.body()) {
      this.body.set(value);
      this.setDirtyState(this.apiSelectedId()!, true);
    } else {
      this.clearDirtyState();
    }
  }

  onChangePreRequest(value: any) {
    if (value && value !== this.preRequest()) {
      this.preRequest.set(value);
      this.setDirtyState(this.apiSelectedId()!, true);
    } else {
      this.clearDirtyState();
    }
  }

  onChangePostResponse(value: any) {
    if (value && value !== this.postResponse()) {
      this.postResponse.set(value);
      this.setDirtyState(this.apiSelectedId()!, true);
    } else {
      this.clearDirtyState();
    }
  }

  createApi() {
    const createApiAction = () => {
      this.dialog
        .open(AddOrEditApiComponent, {
          data: {
            headerData: this.headerData(),
            // parameterData: this.parameterData,
            isCreate: true,
          },
          width: '40vw',
          minWidth: '340px',
        })
        .afterClosed()
        .subscribe((value) => {
          if (value && typeof value === 'number') {
            this.getListApi(false, value);
          }
        });
    };

    if (this.isOneApiDirty) {
      this.handleConfirmDirtyApi((value: string) => {
        if (!value) {
          this.clearDirtyState();
          createApiAction();
        }
      }, false);
      return;
    }
    createApiAction();
  }

  editApi(api: IApi, evt: any) {
    evt.stopPropagation();

    const editApiAction = () => {
      this.dialog
        .open(AddOrEditApiComponent, {
          data: {
            api: api,
            headerData: this.headerData(),
            parameterData: this.parameterData(),
            isCreate: false,
          },
          width: '40vw',
          minWidth: '340px',
        })
        .afterClosed()
        .subscribe((value) => {
          if (value && typeof value === 'number') {
            this.getListApi(true, value);
          }
        });
    };

    if (this.isOneApiDirty) {
      this.handleConfirmDirtyApi((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          editApiAction();
        }
      }, false);
      return;
    }
    editApiAction();
  }

  removeHeader(index: number) {
    this.headerData.update((data) => {
      return data.filter((item, idx) => idx !== index);
    });
  }

  addHeader() {
    this.headerData.update((data) => {
      data.push({ key: '', value: '' });
      return data;
    });
  }

  removeParam(index: number) {
    this.parameterData.update((data) => {
      return data.filter((item, idx) => idx !== index);
    });
    this.updateParam();
  }

  addParam() {
    this.parameterData.update((data) => {
      data.push({ key: '', value: '' });
      return data;
    });
  }

  saveApi(fromDetail: boolean = false) {
    let body: IApi;
    const convertedHeaders: any = {};
    const buildHeaders = () => {
      for (const item of this.headerData()) {
        convertedHeaders[item.key] = item.value;
      }
      convertedHeaders['Content-Type'] = 'application/json';
      convertedHeaders['Accept'] = 'application/json';
      return Object.keys(convertedHeaders).length
        ? JSON.stringify(convertedHeaders)
        : null;
    };

    if (fromDetail) {
      body = {
        ...this.formGroupApiDetail.value,
        name: this.formGroupApiDetail.value.name.split('API_')[1],
        headers: buildHeaders(),
        parameters:
          this.parameterData().length > 0
            ? JSON.stringify(this.parameterData())
            : null,
        body: this.body(),
        pre_request: this.preRequest(),
        post_response: this.postResponse(),
      };
    } else {
      this.headerData.set([]);
      this.parameterData.set([]);
      body = {
        ...this.formGroup.value,
        headers: buildHeaders(),
        parameters: null,
      };
    }
    this.isSubmitting.set(true);
    if (body.id) {
      this.apiService
        .updateApi(body)
        .pipe(catchError((error) => of(false)))
        .subscribe({
          next: (api) => {
            if (!api) {
              this.showSnackBar('Update failed', 'error');
              this.isSubmitting.set(false);
              return;
            }
            this.showSnackBar('Update successfully', 'success');
            this.isSubmitting.set(false);
            this.clearDirtyState();
            this.getListApi(true);
          },
        });
    } else {
      this.apiService
        .createApi(body)
        .pipe(catchError((error) => of(false)))
        .subscribe({
          next: (api) => {
            if (!api) {
              this.showSnackBar('Create failed', 'error');
              this.isSubmitting.set(false);
              return;
            }
            this.clearDirtyState();
            this.showSnackBar('Create successfully', 'success');
            this.isSubmitting.set(false);
            this.getListApi();
          },
        });
    }
  }

  deleteApi(api: IApi, evt: any) {
    evt.stopPropagation();

    const deleteApiAction = () => {
      this.dialog
        .open(ConfirmDialogComponent, {
          data: {
            title: 'Delete this api',
            content: 'Are you sure delete this api?',
            isDelete: true,
          },
          width: '300px',
        })
        .afterClosed()
        .subscribe((value) => {
          if (!!value) {
            if (api && api.id) {
              this.apiService.deleteApi(api.id).subscribe({
                next: () => {
                  this.listApiTab().splice(
                    this.listApiTab().findIndex((v) => v.id === api.id),
                    1
                  );
                  this.apiSelected.set(undefined);
                  this.showSnackBar('Delete successfully', 'success');
                  this.getListApi();
                },
                error: (err) => {
                  this.showSnackBar('Failed to delete api', 'error');
                },
              });
            }
          }
        });
    };

    if (this.isOneApiDirty) {
      this.handleConfirmDirtyApi((value: any) => {
        if (!!value) {
          this.clearDirtyState();
          deleteApiAction();
        }
      });
      return;
    }
    deleteApiAction();
  }

  selectApi(api_id?: number | null | undefined) {
    if (!api_id) return;

    if (this.apiSelectedId() === api_id) return;
    if (this.isOneApiDirty) {
      this.handleConfirmDirtyApi((value: string) => {
        if (!!value) {
          this.clearDirtyState();
          this.selectApiAction(api_id);
        }
      }, false);
      return;
    }
    this.selectApiAction(api_id);
  }

  removeApiSelectedTab(index: number, evt: any) {
    evt.stopPropagation();
    if (this.listApiTab()[index].isDirty) {
      return;
    }

    const removeApiSelectedTabAction = () => {
      this.listApiTab().splice(index, 1);
      if (this.listApiTab().length > 0) {
        this.selectApi(this.listApiTab()[this.listApiTab().length - 1].id ?? 0);
      }
    };

    if (this.isOneApiDirty) {
      this.handleConfirmDirtyApi((value: string) => {
        if (!!value) {
          this.clearDirtyState();
          removeApiSelectedTabAction();
        }
      }, false);
      return;
    }
    removeApiSelectedTabAction();
  }

  testApi() {
    this.isSendingAPI.set(true);
    let bodyReq = this.apiSelected() ?? {};
    this.apiService
      .testApi(bodyReq)
      .pipe(
        catchError((error) => {
          this.isSendingAPI.set(false);
          return of(error);
        })
      )
      .subscribe((res) => {
        this.response = JSON.stringify(res, null, 2);
        this.isSendingAPI.set(false);
      });
  }

  private getListApi(
    isEdit: boolean = false,
    api_id?: number | null | undefined
  ) {
    this.apiService.getListApi(this.searchModel).subscribe({
      next: (res) => {
        if (res && res.length !== 0) {
          res.forEach((v) => {
            v.isDirty = false;
          });
          this.listApi.set(res);
          this.listApiTab.update((list) => {
            if (isEdit) {
              const listApiNew = new Map(list.map((v) => [v.id!, v]));
              list.map((v) => ({
                ...v,
                ...listApiNew.get(v.id!),
                isDirty: false,
              }));
            }
            return list;
          });

          if (!isEdit && res[0]?.id && !api_id) {
            this.selectApi(res[0].id);
          } else {
            if (api_id) {
              this.apiSelectedId.set(api_id);
              // this.selectApi(api_id);
            }
            this.selectApiAction(this.apiSelectedId());
            this.apiSelected.set(
              this.listApi().find((v) => v.id === this.apiSelectedId())
            );
            const indexApiSelected = this.listApiTab().findIndex(
              (v) => v.id === this.apiSelected()!.id
            );
            this.listApiTab.update((list) => {
              list[indexApiSelected] = this.apiSelected()!;
              return list;
            });
          }
        } else {
          this.listApi.set([]);
        }
      },
      error: () => {
        this.listApi.set([]);
      },
    });
  }

  private get isOneApiDirty() {
    const indexDirtyTab = this.listApiTab().findIndex((v) => v.isDirty);
    const indexDirtyList = this.listApi().findIndex((v) => v.isDirty);
    return Boolean(indexDirtyTab > -1 || indexDirtyList > -1);
  }

  private handleConfirmDirtyApi(callback?: any, isDelete: boolean = true) {
    return this.dialog
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Confirm',
          content:
            'You haven’t saved your changes. Are you sure you want to continue?',
          isDelete: isDelete,
          confirmText: isDelete ? 'Delete' : 'Continue',
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => value!! && callback && callback(value));
  }

  private setDirtyState(api_id: number, state: boolean) {
    const updateIsDirty = (list: IApi[]) => {
      const api = list.find((v) => v.id === api_id);
      if (api) {
        api.isDirty = state;
      }
    };
    updateIsDirty(this.listApiTab());
    updateIsDirty(this.listApi());
  }

  private clearDirtyState() {
    const indexDirtyTab = this.listApiTab().findIndex((v) => v.isDirty);
    const indexDirtyList = this.listApi().findIndex((v) => v.isDirty);
    if (indexDirtyTab !== -1) {
      this.listApiTab()[indexDirtyTab].isDirty = false;
    }
    if (indexDirtyList !== -1) {
      // this.listApi[indexDirtyList].isDirty = false;
      this.listApi.update((list) => {
        list[indexDirtyList].isDirty = false;
        return list;
      });
    }
    if (this.apiSelected() && this.apiSelected()!.isDirty) {
      this.apiSelected.update((api) => {
        api!.isDirty = false;
        return api;
      });
    }
  }

  private selectApiAction(api_id?: number) {
    this.tabIndex = 0;

    this.apiSelectedId.set(api_id);
    const apiSelected = this.listApi().find((v) => v.id === api_id);
    if (apiSelected) {
      this.apiSelected.set(apiSelected);
    }

    const indexApiSelected = this.listApiTab().findIndex(
      (v) => v.id === this.apiSelected()?.id
    );
    if (indexApiSelected < 0 && this.apiSelected()) {
      this.listApiTab.update((lt) => {
        this.apiSelected() && lt.push(this.apiSelected()!);
        return lt;
      });
    }

    this.formGroupApiDetail.reset();
    this.formGroupApiDetail.patchValue(
      {
        id: this.apiSelected()?.id,
        name: this.apiSelected()?.name,
        description: this.apiSelected()?.description,
        url: this.apiSelected()?.url,
        method: this.apiSelected()?.method,
        headers: this.apiSelected()?.headers,
        parameters: this.apiSelected()?.parameters,
        body: this.apiSelected()?.body,
        pre_request: this.apiSelected()?.pre_request,
        post_response: this.apiSelected()?.post_response,
      },
      {
        emitEvent: false,
      }
    );

    this.parameterData.set(
      (JSON.parse(this.apiSelected()?.parameters || '[]') as {
        key: string;
        value: unknown;
      }[]) || []
    );

    const headers = JSON.parse(this.apiSelected()?.headers || '[]');
    if (headers) {
      this.headerData.set([]);
      for (let [index, key] of Object.keys(headers).entries()) {
        this.headerData.update((list) => {
          list[index] = {
            key,
            value: headers[key],
          };
          return list;
        });
      }
    } else {
      this.headerData.set([]);
    }

    this.body.set(this.apiSelected()?.body || '{}');
    this.preRequest.set(this.apiSelected()?.pre_request || '');
    this.postResponse.set(this.apiSelected()?.post_response || '');
    this.response = JSON.stringify({}, null, 2);
  }

  private updateParam() {
    const url = this.formGroupApiDetail.get('url')?.value.split('?')[0];
    const paramsConverted = [];
    for (let param of this.parameterData()) {
      paramsConverted.push(`${param.key}=${param.value}`);
    }
    this.formGroupApiDetail
      .get('url')
      ?.setValue(
        url + (paramsConverted.length ? `?${paramsConverted.join('&')}` : ''),
        { emitEvent: false }
      );
  }

  selectTab(tab: string) {
    this.selectedTab = tab;
  }

  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }

  updateUrlParams() {
    const queryParams: any = {};
    queryParams['key_word'] = this.searchModel.key_word;
    // nếu có nhiều filter:
    // if (this.searchModel.method) queryParams['method'] = this.searchModel.method;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge',
      replaceUrl: true,
    });
  }
}
