<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      LLM Settings
      <a
        href="https://docs.dxconnect.lifesup.ai/llm-settings"
        target="_blank"
        class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
      >
        Learn more
      </a>
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="closeDialog()"
      ></app-svg-icon>
    </div>
  </div>
  <div
    class="flex-1 overflow-y-auto mt-18 mb-20 px-6 pt-6 pb-[3px] flex flex-col"
  >
    <div
      class="text-lg font-bold text-base-content dark:text-dark-base-content mb-4"
    >
      RAG
    </div>
    <div class="flex flex-col w-full gap-4 mb-6">
      <div
        class="flex items-end gap-4"
        [ngClass]="{ 'flex-col': uiStore.isHandset() }"
      >
        <dx-form-field
          class="flex-1 w-full"
          [style.margin-bottom]="0"
          [subscriptHidden]="true"
        >
          <dx-label>AI Model</dx-label>
          <dx-select
            placeholder="Choose the AI model you would like to use"
            [formControl]="modelRag"
          >
            @for (item of listModelOptions(); track item.value) {
            <dx-option [value]="item.value">
              {{ item.label }}
            </dx-option>
            }
          </dx-select>
        </dx-form-field>
        <div class="flex-1 flex flex-wrap items-center space-x-3 w-full">
          <div class="flex-1 w-full flex items-center space-x-4">
            <dx-label class="flex items-center space-x-1 !mb-0"
              >Temperature</dx-label
            >
            <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
              <input dxSliderThumb #slider [formControl]="temperatureRag" />
            </dx-slider>
          </div>
          <input
            type="number"
            step="0.1"
            min="0"
            max="1"
            [formControl]="temperatureRag"
            class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          />
        </div>
      </div>
      <div
        class="flex items-end gap-4"
        [ngClass]="{ 'flex-col': uiStore.isHandset() }"
      >
        <dx-form-field
          class="flex-1 w-full"
          [style.margin-bottom]="0"
          [subscriptHidden]="true"
        >
          <dx-label>Limit (0-20)</dx-label>
          <input
            dxInput
            type="number"
            placeholder="Enter limit"
            [formControl]="limit"
          />
        </dx-form-field>
        <div class="flex-1 flex flex-wrap items-center space-x-3 w-full">
          <div class="flex-1 w-full flex items-center space-x-4">
            <dx-label class="flex items-center space-x-1 !mb-0">
              <div>Threshold</div>
              <ng-icon
                name="heroInformationCircle"
                class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
                dxTooltip="Predefined value that determines the decision boundary for classifying outcomes based on a model's output."
                dxTooltipPosition="right"
              ></ng-icon>
            </dx-label>
            <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
              <input dxSliderThumb #slider [formControl]="threshold" />
            </dx-slider>
          </div>
          <input
            type="number"
            step="0.1"
            min="0"
            max="1"
            [formControl]="threshold"
            class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          />
        </div>
      </div>
    </div>

    <hr class="text-primary-border dark:text-dark-primary-border" />

    <div
      class="text-lg font-bold text-base-content dark:text-dark-base-content mb-4 mt-6"
    >
      Check Fit
    </div>
    <div class="flex flex-col w-full gap-4 mb-6">
      <div
        class="flex items-end gap-4"
        [ngClass]="{ 'flex-col': uiStore.isHandset() }"
      >
        <dx-form-field
          class="flex-1 w-full"
          [style.margin-bottom]="0"
          [subscriptHidden]="true"
        >
          <dx-label>AI Model</dx-label>
          <dx-select
            placeholder="Choose the AI model you would like to use"
            [formControl]="modelCF"
          >
            @for (item of listModelOptions(); track item.value) {
            <dx-option [value]="item.value">
              {{ item.label }}
            </dx-option>
            }
          </dx-select>
        </dx-form-field>
        <div class="flex-1 flex flex-wrap items-center space-x-3 w-full">
          <div class="flex-1 w-full flex items-center space-x-4">
            <dx-label class="flex items-center space-x-1 !mb-0"
              >Temperature</dx-label
            >
            <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
              <input dxSliderThumb #slider [formControl]="temperatureCF" />
            </dx-slider>
          </div>
          <input
            type="number"
            step="0.1"
            min="0"
            max="1"
            [formControl]="temperatureCF"
            class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          />
        </div>
      </div>
    </div>

    <hr class="text-primary-border dark:text-dark-primary-border" />

    <div
      class="text-lg font-bold text-base-content dark:text-dark-base-content mb-4 mt-6"
    >
      Detect Intent
    </div>
    <div class="flex flex-col w-full gap-4 mb-6">
      <div
        class="flex items-end gap-4"
        [ngClass]="{ 'flex-col': uiStore.isHandset() }"
      >
        <dx-form-field
          class="flex-1 w-full"
          [style.margin-bottom]="0"
          [subscriptHidden]="true"
        >
          <dx-label>AI Model</dx-label>
          <dx-select
            placeholder="Choose the AI model you would like to use"
            [formControl]="modelDI"
          >
            @for (item of listModelOptions(); track item.value) {
            <dx-option [value]="item.value">
              {{ item.label }}
            </dx-option>
            }
          </dx-select>
        </dx-form-field>
        <div class="flex-1 flex flex-wrap items-center space-x-3 w-full">
          <div class="flex-1 w-full flex items-center space-x-4">
            <dx-label class="flex items-center space-x-1 !mb-0"
              >Temperature</dx-label
            >
            <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
              <input dxSliderThumb #slider [formControl]="temperatureDI" />
            </dx-slider>
          </div>
          <input
            type="number"
            step="0.1"
            min="0"
            max="1"
            [formControl]="temperatureDI"
            class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          />
        </div>
      </div>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="closeDialog()">Close</button>
    <!--<button
      dxLoadingButton="filled"
      [loading]="isSaving()"
      (click)="saveSettings()"
    >
      Save
    </button>-->
  </div>
</div>
