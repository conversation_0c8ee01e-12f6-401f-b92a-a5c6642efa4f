<div
  class="settings-tab-content h-[calc(100dvh-162px)] lg:h-[calc(100dvh-180px)] w-full p-6 flex flex-col space-y-6"
>
  <div class="flex items-center space-x-2">
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      AI settings
      <a
        href="https://docs.dxconnect.lifesup.ai/ai-settings"
        target="_blank"
        class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
      >
        Learn more
      </a>
    </div>
  </div>
  <hr class="text-primary-border dark:text-dark-primary-border" />
  <div class="flex flex-col lg:w-3/4">
    <dx-form-field>
      <dx-label>AI's name</dx-label>
      <input
        dxInput
        placeholder="Enter your AI's name (e.g., DxConnect)"
        [formControl]="name"
      />
    </dx-form-field>
    <dx-form-field>
      <dx-label>AI's business</dx-label>
      <input
        dxInput
        placeholder="What industry is this AI used in?"
        [formControl]="business"
      />
    </dx-form-field>
    <dx-form-field>
      <dx-label>AI's description</dx-label>
      <textarea
        dxInput
        id="settings-ai-description"
        rows="5"
        placeholder="Briefly describe what this AI does (e.g., 24/7 customer support assistant)"
        [formControl]="description"
        (valueChange)="updateTextAreaHeight('settings-ai-description')"
      ></textarea>
    </dx-form-field>
    <dx-form-field>
      <dx-label class="flex items-center space-x-1">
        <div>Reply length</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="Control the average length of your AI Assistant's responses during conversation."
          dxTooltipPosition="right"
        ></ng-icon>
      </dx-label>
      <dx-select
        placeholder="Choose how long you want the AI's responses to be"
        [formControl]="replyLength"
      >
        @for (item of listReplyLength; track item.code) {
        <dx-option [value]="item.code">
          {{ item.name }}
        </dx-option>
        }
      </dx-select>
    </dx-form-field>
    <div class="flex flex-col space-y-6">
      <dx-slide-toggle [formControl]="askForEmailEnabled">
        <div class="flex items-center space-x-1">
          <div>Ask for email</div>
          <ng-icon
            name="heroInformationCircle"
            class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
            dxTooltip="AI Assistant will ask customers for their contact."
            dxTooltipPosition="right"
          ></ng-icon>
        </div>
      </dx-slide-toggle>
      <dx-slide-toggle [formControl]="usingEmoji">
        <div class="flex items-center space-x-1">
          <div>Using emoji</div>
          <ng-icon
            name="heroInformationCircle"
            class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
            dxTooltip="Allow AI Assistant to use emoji in answering."
            dxTooltipPosition="right"
          ></ng-icon>
        </div>
      </dx-slide-toggle>
      <div
        class="w-full sm:w-3/4 flex flex-wrap md:flex-nowrap items-center space-x-3"
      >
        <div class="flex-1 w-full flex items-center space-x-4">
          <dx-label class="flex items-center space-x-1 !mb-0">
            <div>Threshold</div>
            <ng-icon
              name="heroInformationCircle"
              class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
              dxTooltip="Predefined value that determines the decision boundary for classifying outcomes based on a model's output."
              dxTooltipPosition="right"
            ></ng-icon>
          </dx-label>
          <dx-slider class="flex-1" [max]="1" [min]="0" [step]="0.1">
            <input dxSliderThumb #slider [formControl]="threshold" />
          </dx-slider>
        </div>
        <input
          type="number"
          class="h-8 w-18 pl-3 py-1 text-center text-base-content dark:text-dark-base-content rounded-lg bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border"
          [formControl]="threshold"
        />
      </div>
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex flex-col lg:w-3/4">
    <dx-form-field>
      <dx-label>Bot style</dx-label>
      <textarea
        dxInput
        id="settings-bot-style"
        placeholder="E.g., Friendly and casual tone, like a Gen Z assistant"
        [formControl]="bot_style"
        (valueChange)="updateTextAreaHeight('settings-bot-style')"
      ></textarea>
    </dx-form-field>
    <dx-form-field>
      <dx-label>Custom prompt</dx-label>
      <textarea
        dxInput
        id="settings-custom-prompt"
        rows="5"
        placeholder="E.g., Always reply in short bullet points with helpful links"
        [formControl]="customPrompt"
        (valueChange)="updateTextAreaHeight('settings-custom-prompt')"
      ></textarea>
    </dx-form-field>
    <dx-slide-toggle [formControl]="show_source">
      <div class="flex items-center space-x-1">
        <div>Show source</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="Allow AI Assistant to show source in answering."
          dxTooltipPosition="right"
        ></ng-icon>
      </div>
    </dx-slide-toggle>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="w-full flex items-center justify-between">
    <div
      class="text-lg font-bold text-base-content dark:text-dark-base-content"
    >
      Speech Role
    </div>
    <button dxButton (click)="addSpeechRole()">Add role</button>
  </div>
  <div
    class="p-4 rounded-xl border border-primary-border dark:border-dark-primary-border flex flex-col items-center"
  >
    <div class="w-full overflow-x-auto">
      <div class="min-w-140 flex flex-col items-stretch gap-4 mb-4">
        <div class="grid grid-cols-9 gap-4">
          <div class="col-span-2 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Language</span
            >
          </div>
          <div class="col-span-2 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Male</span
            >
          </div>
          <div class="col-span-2 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Female</span
            >
          </div>
          <div class="col-span-2 flex items-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Term</span
            >
          </div>
          <div class="col-span-1 flex items-center justify-center">
            <span
              class="text-sm font-semibold text-base-content dark:text-dark-base-content"
              >Action</span
            >
          </div>
        </div>

        @for (role of speechRoles.value; track $index) {
        <div class="grid grid-cols-9 gap-4">
          <div class="col-span-2 flex items-center">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <dx-select
                [ngModel]="role.language"
                (ngModelChange)="changeSpeechRole($event, $index, 'language')"
                placeholder="Language"
              >
                @for (lang of languageOptions; track $index) {
                <dx-option [value]="lang.value"> {{ lang.label }}</dx-option>
                }
              </dx-select>
            </dx-form-field>
          </div>
          <div class="col-span-2 flex items-center">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [ngModel]="role.male"
                (ngModelChange)="changeSpeechRole($event, $index, 'male')"
                placeholder="Male"
              />
            </dx-form-field>
          </div>
          <div class="col-span-2 flex items-center">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [ngModel]="role.female"
                (ngModelChange)="changeSpeechRole($event, $index, 'female')"
                placeholder="Female"
              />
            </dx-form-field>
          </div>
          <div class="col-span-2 flex items-center">
            <dx-form-field
              class="w-full"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <input
                dxInput
                [ngModel]="role.term"
                (ngModelChange)="changeSpeechRole($event, $index, 'term')"
                placeholder="Term"
              />
            </dx-form-field>
          </div>
          <div class="col-span-1 flex items-center justify-center">
            <app-svg-icon
              type="icTrash"
              (click)="deleteSpeechRole($index)"
              class="w-6 h-6 !text-error dark:!text-dark-error"
            ></app-svg-icon>
          </div>
        </div>
        }
      </div>
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex items-center mb-4">
    <div class="text-lg font-bold text-error dark:text-dark-error">
      Delete AI Assistant
    </div>
  </div>
  <div class="flex flex-col lg:w-3/4">
    <button
      dxButton="elevated"
      class="w-max !border-0 !outline-2 !outline-offset-3 !outline-double !outline-error dark:!outline-dark-error !text-error dark:!text-dark-error !font-bold"
      (click)="showDialogConfirmDeleteAi()"
    >
      Delete this AI Assistant
    </button>
  </div>
</div>
