<div
  class="settings-tab-content h-[calc(100dvh-162px)] lg:h-[calc(100dvh-180px)] w-full p-6 flex flex-col space-y-6"
>
  <div class="flex items-center space-x-2">
    <dx-slide-toggle [formControl]="enabled"></dx-slide-toggle>
    <div
      class="text-xl font-bold text-base-content dark:text-dark-base-content"
    >
      Human handoff
      <a
        href="https://docs.dxconnect.lifesup.ai/Settings/Human%20Handoff"
        target="_blank"
        class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
      >
        Learn more
      </a>
    </div>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex flex-col lg:w-3/4">
    <dx-slide-toggle [formControl]="workingHoursEnabled">
      <div class="flex items-center space-x-1">
        <div>Human Agents’s availability</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="Human Handoff shouldn't occur when there are no Human Agents on the other side. Please specify your agents' working hours and what information should be provided to users who cannot be handed off to a Human Agent outside of working hours."
          dxTooltipPosition="right"
        ></ng-icon>
      </div>
    </dx-slide-toggle>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex flex-col lg:w-3/4">
    <div class="w-full flex flex-wrap items-center space-x-4">
      <dx-form-field class="flex-1">
        <dx-label>Working hours</dx-label>
        <input
          dxInput
          type="time"
          placeholder="Select your daily start working hours"
          class="!h-5"
          [formControl]="workingHoursFrom"
        />
        <app-svg-icon
          dxSuffix
          type="icClock"
          class="w-5 h-5 mr-3 !text-neutral-content dark:!text-dark-neutral-content"
        ></app-svg-icon>
      </dx-form-field>
      <dx-form-field class="flex-1">
        <dx-label class="!text-transparent">Working hours</dx-label>
        <input
          dxInput
          type="time"
          placeholder="Select your daily end working hours"
          class="!h-5"
          [formControl]="workingHoursTo"
        />
        <app-svg-icon
          dxSuffix
          type="icClock"
          class="w-5 h-5 mr-3 !text-neutral-content dark:!text-dark-neutral-content"
        ></app-svg-icon>
      </dx-form-field>
    </div>
    <dx-form-field>
      <dx-label>Timezone</dx-label>
      <dx-select
        placeholder="Choose the timezone that applies to your system"
        [formControl]="workingTimezone"
      >
        @for (item of listTimezone; track item.value) {
        <dx-option [value]="item.value">
          {{ item.label }}
        </dx-option>
        }
      </dx-select>
    </dx-form-field>

    <div class="flex flex-col space-y-3 mb-[20.4px]">
      <dx-label>Working days</dx-label>
      <div class="flex flex-wrap gap-4">
        <div
          class="w-15 h-12 rounded-xl cursor-pointer border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 flex items-center justify-center text-base-content dark:text-dark-base-content text-[16px]"
          [ngClass]="{ '!bg-primary': workingDays.value?.includes('monday') }"
          (click)="selectWorkingDays('monday')"
        >
          Mon
        </div>
        <div
          class="w-15 h-12 rounded-xl cursor-pointer border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 flex items-center justify-center text-base-content dark:text-dark-base-content text-[16px]"
          [ngClass]="{ '!bg-primary': workingDays.value?.includes('tuesday') }"
          (click)="selectWorkingDays('tuesday')"
        >
          Tue
        </div>
        <div
          class="w-15 h-12 rounded-xl cursor-pointer border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 flex items-center justify-center text-base-content dark:text-dark-base-content text-[16px]"
          [ngClass]="{
            '!bg-primary': workingDays.value?.includes('wednesday')
          }"
          (click)="selectWorkingDays('wednesday')"
        >
          Wed
        </div>
        <div
          class="w-15 h-12 rounded-xl cursor-pointer border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 flex items-center justify-center text-base-content dark:text-dark-base-content text-[16px]"
          [ngClass]="{
            '!bg-primary': workingDays.value?.includes('thursday')
          }"
          (click)="selectWorkingDays('thursday')"
        >
          Thu
        </div>
        <div
          class="w-15 h-12 rounded-xl cursor-pointer border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 flex items-center justify-center text-base-content dark:text-dark-base-content text-[16px]"
          [ngClass]="{
            '!bg-primary': workingDays.value?.includes('friday')
          }"
          (click)="selectWorkingDays('friday')"
        >
          Fri
        </div>
        <div
          class="w-15 h-12 rounded-xl cursor-pointer border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 flex items-center justify-center text-base-content dark:text-dark-base-content text-[16px]"
          [ngClass]="{
            '!bg-primary': workingDays.value?.includes('saturday')
          }"
          (click)="selectWorkingDays('saturday')"
        >
          Sat
        </div>
        <div
          class="w-15 h-12 rounded-xl cursor-pointer border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 flex items-center justify-center text-base-content dark:text-dark-base-content text-[16px]"
          [ngClass]="{
            '!bg-primary': workingDays.value?.includes('sunday')
          }"
          (click)="selectWorkingDays('sunday')"
        >
          Sun
        </div>
      </div>
    </div>

    <dx-form-field>
      <dx-label>Message outside working hours</dx-label>
      <textarea
        dxInput
        placeholder="Enter the message that will be shown when users contact you outside of working hours"
        [formControl]="outsideWorkingHours"
      ></textarea>
    </dx-form-field>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div
    class="text-lg font-bold text-base-content dark:text-dark-base-content mb-4"
  >
    AI escalation
  </div>
  <div class="flex flex-col lg:w-3/4">
    <dx-form-field>
      <dx-label class="flex items-center space-x-1">
        <div>Question</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="Is there a specific way you would like the user to be asked if they would like to be transferred to a Human Agent?"
          dxTooltipPosition="right"
        ></ng-icon>
      </dx-label>
      <textarea
        dxInput
        placeholder="Enter the question to ask when offering to escalate to a Human Assistant"
        [formControl]="question"
      ></textarea>
    </dx-form-field>
    <dx-form-field>
      <dx-label class="flex items-center space-x-1">
        <div>Confirmation</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="Provide your preferred wording for the message confirming that Human Handoff is about to happen."
          dxTooltipPosition="right"
        ></ng-icon>
      </dx-label>
      <textarea
        dxInput
        placeholder="Enter the confirmation message shown when transferring to a Human Assistant"
        [formControl]="confirmation"
      ></textarea>
    </dx-form-field>
    <dx-form-field>
      <dx-label class="flex items-center space-x-1">
        <div>Max unrelated questions before automatic handoff</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="The number of consecutive unrelated questions that a customer can ask in a conversation. After that, AI Assistant will automatically handoff the conversation to a live agent."
          dxTooltipPosition="right"
        ></ng-icon>
      </dx-label>
      <input
        dxInput
        type="number"
        placeholder="Select the maximum number of unrelated questions allowed before auto-escalation"
        [formControl]="numSentencesBotAnswerNotTopic"
      />
    </dx-form-field>
    <dx-slide-toggle [formControl]="summaryEnabled">
      <div class="flex items-center space-x-1">
        <div>AI summary will be provided on Human Handoff</div>
        <ng-icon
          name="heroInformationCircle"
          class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
          dxTooltip="Each conversation which ended in Human Handoff will be annotated with a short note summarising to the Human Agent what happened in the conversation so far. That will give them all the right context and help them jump right in."
          dxTooltipPosition="right"
        ></ng-icon>
      </div>
    </dx-slide-toggle>
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex items-center mb-4">
    <dx-slide-toggle [formControl]="emailNotificationEnabled"></dx-slide-toggle>
    <div
      class="text-lg font-bold text-base-content dark:text-dark-base-content ml-3 mr-2"
    >
      Email notifications
    </div>
    <ng-icon
      name="heroInformationCircle"
      class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
      dxTooltip="Where should we send notifications about new conversations that need to be handled by a Human Agent?"
      dxTooltipPosition="right"
    ></ng-icon>
  </div>
  <div class="flex flex-col lg:w-3/4">
    <dx-form-field [subscriptHidden]="true">
      <app-svg-icon
        dxPrefix
        type="icLinkPrefix"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        placeholder="Paste url here"
        [formControl]="emailNotificationsInput"
        (keyup.enter)="addEmail()"
      />
    </dx-form-field>
    @if (emailNotifications.value && emailNotifications.value.length > 0) {
    <div class="flex flex-wrap gap-2">
      @for (email of emailNotifications.value; track email; let index = $index)
      {
      <div
        class="flex items-center space-x-2 px-4 py-1 bg-base-100 dark:bg-dark-base-100 rounded-[12px]"
      >
        <div class="text-sm text-base-content dark:text-dark-base-content">
          {{ email }}
        </div>
        <ng-icon
          name="heroXCircleSolid"
          class="!text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="removeEmail(index)"
        ></ng-icon>
      </div>
      }
    </div>
    }
  </div>

  <hr class="text-primary-border dark:text-dark-primary-border" />

  <div class="flex items-center mb-4">
    <div
      class="text-lg font-bold text-base-content dark:text-dark-base-content mr-2"
    >
      Human-Only mode
    </div>
    <ng-icon
      name="heroInformationCircle"
      class="cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
      dxTooltip="Switch to speaker-only mode."
      dxTooltipPosition="right"
    ></ng-icon>
  </div>
  <div class="flex flex-col lg:w-3/4">
    <dx-slide-toggle [formControl]="onlyHuman">
      Switch to speaker-only mode.
    </dx-slide-toggle>
  </div>
</div>
