@if (!uiStore.isHandset()) {
  <div class="flex flex-col justify-between">
    <h1 class="text-[28px] font-bold text-base-content dark:text-dark-base-content" [bdcWalkTriggerFor]="integration_guide_1">Integrations
      <a
        href="https://docs.dxconnect.lifesup.ai/category/integrations"
        target="_blank"
        class="text-[15px] font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
      >
        Learn more
      </a>
    </h1>
    <p class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content">
      As soon as your AI Assistant is ready, anyone can start talking to your AI
      Assistant using your public link or web widget.
    </p>

    <div class="mt-6 w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" [bdcWalkTriggerFor]="integration_guide_2">
      @for (integration of listIntegrations(); track $index) {
        <div class="col-span-1 w-full">
          <dx-card
            appearance="outlined"
            class="card-integration"
            [ngClass]="{
        'cursor-pointer': !integration.isComingSoon
      }"

          >
            <dx-card-header>
              <div class="w-full flex items-start justify-between">
                <img
                  [ngSrc]="integration.icon!"
                  alt="Icon description"
                  width="56"
                  height="56"
                  class="w-14 h-14 object-cover"
                  (click)="$event.stopPropagation()"
                />
                @if (!integration.isComingSoon) {
                  <dx-slide-toggle
                    [checked]="!!integration.isEnabled"
                    (checkedChange)="enableIntegration($event,integration)"
                  ></dx-slide-toggle>
                } @else {
                  <div
                    class="px-2 py-1 text-[13px] text-primary-content bg-neutral-content rounded-full"
                  >
                    Coming Soon
                  </div>
                }
              </div>
            </dx-card-header>
            <dx-card-content  (click)="showDiaLogEdit(integration)">
              <h1
                class="text-xl font-bold text-base-content dark:text-dark-base-content"
              >
                {{ integration.title }}
              </h1>
              <div
                class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content"
              >
                {{ integration.subTitle }}
              </div>
            </dx-card-content>
          </dx-card>
        </div>
      }
    </div>
  </div>
} @else {
  <app-mobile-header [title]="title" [backFn]="backFn">
    <div mHeaderLeft class="flex items-center">
      <a
        href="https://docs.dxconnect.lifesup.ai/category/integrations"
        target="_blank"
        class="flex items-center"
      >
        <app-svg-icon type="icInfo" class="w-4 h-4 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
      </a>
    </div>
  </app-mobile-header>
  <div class="w-full h-full flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden" [bdcWalkTriggerFor]="integration_guide_1">
    <p class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content">
      As soon as your AI Assistant is ready, anyone can start talking to your AI
      Assistant using your public link or web widget.
    </p>
    <div class="mt-6 w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" [bdcWalkTriggerFor]="integration_guide_2">
      @for (integration of listIntegrations(); track $index) {
        <div class="col-span-1 w-full">
          <dx-card
            appearance="outlined"
            class="card-integration"
            [ngClass]="{
        'cursor-pointer': !integration.isComingSoon
      }"
          >
            <dx-card-header>
              <div class="w-full flex items-start justify-between">
                <img
                  [ngSrc]="integration.icon!"
                  alt="Icon description"
                  width="56"
                  height="56"
                  class="w-14 h-14 object-cover"
                  (click)="$event.stopPropagation()"
                />
                @if (!integration.isComingSoon) {
                  <dx-slide-toggle
                    [checked]="!!integration.isEnabled"
                    (checkedChange)="enableIntegration($event,integration)"
                  ></dx-slide-toggle>
                } @else {
                  <div
                    class="px-2 py-1 text-[13px] text-primary-content bg-neutral-content rounded-full"
                  >
                    Coming Soon
                  </div>
                }
              </div>
            </dx-card-header>
            <dx-card-content  (click)="showDiaLogEdit(integration)">
              <h1
                class="text-xl font-bold text-base-content dark:text-dark-base-content"
              >
                {{ integration.title }}
              </h1>
              <div
                class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content"
              >
                {{ integration.subTitle }}
              </div>
            </dx-card-content>
          </dx-card>
        </div>
      }
    </div>
  </div>
}


<bdc-walk-popup #integration_guide_1
                name="integration_guide_1"
                header="Integrations"
                xPosition="after"
                yPosition="above"
                sideNoteText="1/2"
                [showButton]="true"
                buttonText="Next"
                [mustCompleted]="{nav_guided_2: true}"
                [onButtonCompleteTask]="{integration_guide_1: true}">
  Integrations are the tools you can connect to your AI Assistant.
</bdc-walk-popup>


<bdc-walk-popup #integration_guide_2
                name="integration_guide_2"
                header="Integration"
                xPosition="after"
                yPosition="below"
                [horizontal]="true"
                sideNoteText="2/2"
                [showButton]="true"
                buttonText="End"
                [mustCompleted]="{integration_guide_1: true}"
                [onButtonCompleteTask]="{integration_guide_2: true}">
  Click on an integration you want to setting it.
  Switch on or off to enable or disable the integration.
</bdc-walk-popup>
