// @ts-nocheck
import { ToolFlowDev } from '@flow-editor/model/bo';
import { axiosClient } from './axios-client';

export const toolFlowDevApi = {
  getToolFlowDev(id: number): Promise<ToolFlowDev> {
    const url = `/v2/tool/${id}`;
    return axiosClient.get(url);
  },

  saveFlowDataDev(body: ToolFlowDev): Promise<any> {
    const url = `/v2/tool/save`;
    return axiosClient.post(url, body);
  },
};
