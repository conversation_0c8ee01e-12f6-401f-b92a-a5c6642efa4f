// @ts-nocheck
import { environment } from '@env/environment';
import axios, { AxiosResponse } from 'axios';

export const axiosClient = axios.create({
  baseURL: environment.FLOW_URL,
  headers: {
    'Content-Type': 'application/json; charset=utf-8',
    accept: 'application/json',
  },
});

axiosClient.interceptors.request.use(
  function (config) {
    const token =
      localStorage.getItem('accessToken') ||
      sessionStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

axiosClient.interceptors.response.use(
  function (response: AxiosResponse) {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    return response.data;
  },
  function (error) {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error
    return Promise.reject(error);
  }
);
