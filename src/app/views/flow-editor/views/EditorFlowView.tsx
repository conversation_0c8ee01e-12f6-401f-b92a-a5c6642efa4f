// @ts-nocheck
import { LoadingOutlined } from "@ant-design/icons"
import { environment } from "@env/environment"
import {
  apiApi,
  eventApi,
  functionApi,
  toolFlowApi,
  toolFlowDevApi
} from "@flow-editor/api"
import FlowCanvas from "@flow-editor/components/canvas/FlowCanvas"
import CollapseButton from "@flow-editor/components/flow/CollapseButton"
import MenuPrompts from "@flow-editor/components/flow/MenuPrompts"
import StyledFlow from "@flow-editor/components/flow/StyledFlow"
import { StyledButtonModal } from "@flow-editor/components/styled"
import { STUDIO_STATUS } from "@flow-editor/constant"
import {
  Api,
  ApiState,
  BuildFlowState,
  Event,
  EventState,
  Function,
  FunctionState,
  LayoutState,
  StudioState,
  ToolFlow,
  ToolFlowDev
} from "@flow-editor/model"
import {
  useApiState,
  useBuildFlowState,
  useEventState,
  useFunctionState,
  useLayoutState,
  useStudioState
} from "@flow-editor/store"
import { LocalStorageKey } from "@flow-editor/utils/flow"
import { RiArrowLeftSLine, RiArrowRightSFill } from "@remixicon/react"
import { DEFAULT_WHITELIST } from "@shared/app.constant"
import { ConfigProvider, Layout, message, Modal, Spin, Switch, Typography } from "antd"
import * as React from "react"
import { useCallback, useEffect, useRef, useState } from "react"
import { io, Socket } from "socket.io-client"

const {Header, Sider} = Layout

const EditorFlowView = ({flowId}) => {
  /**
   * State
   */
  const [
    listNodeCollapsed,
    setListNodeCollapsed
  ] = useState<boolean>(true)
  const [
    previewOpened,
    setPreviewOpened
  ] = useState<boolean>(false)
  const [
    modalConfirmOpened,
    setModalConfirmOpened
  ] = useState<boolean>(false)
  const [
    confirmOk,
    setConfirmOk
  ] = useState<boolean>(false)
  const [
    confirmCancel,
    setConfirmCancel
  ] = useState<boolean>(false)
  const [
    isStatusInitialized,
    setIsStatusInitialized
  ] = useState<boolean>(false)
  const [
    conversationId,
    setConversationId
  ] = useState<string>(null)
  const [
    debugMode,
    setDebugMode
  ] = useState<boolean>(false)
  const [
    debugPanelActive,
    setDebugPanelActive
  ] = useState<boolean>(false)

  /**
   * Store
   */
  const {
    isDirty,
    flow,
    setFlow
  } = useBuildFlowState<BuildFlowState>((state: BuildFlowState) => state)
  const {
    status,
    setStudioStatus
  } = useStudioState<StudioState>(state => state)
  const {
    isLoading,
    theme,
    setLoading,
    setTheme
  } = useLayoutState<LayoutState>(state => state)
  const {setApis} = useApiState<ApiState>((state) => state)
  const {setFunctions} = useFunctionState<FunctionState>((state) => state)
  const {setEvents} = useEventState<EventState>((state) => state)

  /**
   * Ref
   */
  const flowCanvasRef = useRef<{ triggerSave: () => Promise<void> }>();
  const socketRef = useRef<Socket | null>(null)
  const intervalRetry = useRef(null)

  /**
   * Hook
   */
  const [messageApi, contextHolder] = message.useMessage()

  /**
   * Lifecycle
   */
  useEffect(() => {
    const handleMessage = (event) => {
      if (event && event.data && event.data.type === "studio_status") {
        setStudioStatus(event.data.data.status)
        setIsStatusInitialized(true)
      }
      if (event && event.data && event.data.type === "conversation_id") {
        setConversationId(event.data.data)
      }
      if (event && event.data && event.data.type === "theme") {
        setTheme(event.data.data.theme)
      }
      if (event && event.data && event.data.type === "debug_panel_closed") {
        setDebugPanelActive(false)
      }
    }

    window.addEventListener("message", handleMessage)

    return () => {
      window.removeEventListener("message", handleMessage)
    }
  }, [])

  useEffect(() => {
    if (status !== STUDIO_STATUS.DEV && debugMode) {
      setDebugMode(false)
      postMessage({type: "debug_mode", data: false})
    }
  }, [status, debugMode])

  useEffect(() => {
    if (previewOpened && debugMode) {
      postMessage({
        type: "debug_ready",
        data: {
          debug: true,
          flow_id: flowId,
          preview_opened: previewOpened,
          studio_status: status
        }
      })
    }
  }, [debugMode, previewOpened, flowId, status])

  useEffect(() => {
    if (!isStatusInitialized) return
    if (status) {
      const fetchFlowDev = async () => {
        setLoading(true)
        const res: ToolFlowDev = await toolFlowDevApi.getToolFlowDev(Number(flowId))
        if (res) {
          setFlow(res)
          localStorage.setItem(LocalStorageKey.flowDevData(res.id), res.flow_data)
        }
        setLoading(false)
      }
      const fetchFlowLive = async () => {
        setLoading(true)
        const res: ToolFlow = await toolFlowApi.getToolFlow(Number(flowId))
        if (res) {
          setFlow(res)
          localStorage.setItem(LocalStorageKey.flowLiveData(res.id), res.flow_data)
        }
        setLoading(false)
      }

      const fetchApi = async () => {
        const res: Api[] = await apiApi.getListApi()
        setApis(res)
      }

      const fetchFunction = async () => {
        const res: Function[] = await functionApi.getListFunction()
        setFunctions(res)
      }

      const fetchEvent = async () => {
        const res: Event[] = await eventApi.getEvent()
        setEvents(res)
      }

      if (status === STUDIO_STATUS.DEV) {
        fetchFlowDev().catch((error) => {
          void messageApi.error(error.message || error.detail || JSON.stringify(error))
        })
      }

      if (status === STUDIO_STATUS.LIVE) {
        fetchFlowLive().catch((error) => {
          void messageApi.error(error.message || error.detail || JSON.stringify(error))
        })

        setStudioStatus(STUDIO_STATUS.LIVE)
      }

      fetchApi().catch((error) => {
        void messageApi.error(error.message || error.detail || JSON.stringify(error))
      })

      fetchFunction().catch((error) => {
        void messageApi.error(error.message || error.detail || JSON.stringify(error))
      })

      fetchEvent().catch((error) => {
        void messageApi.error(error.message || error.detail || JSON.stringify(error))
      })
    }
  }, [status, isStatusInitialized])

  useEffect(() => {
    if (flow && conversationId) {
      const options = {
        query: {
          ai_id: flow.ai_id,
          domain: DEFAULT_WHITELIST
        }
      }

      const retryConnect = () => {
        intervalRetry.current = setInterval(() => {
          socketRef.current.connect()
        }, 1000)
      }

      socketRef.current = io(environment.SOCKET_MESSAGE_ENDPOINT, options)

      socketRef.current.on('connect', () => {
        if (intervalRetry.current) clearInterval(intervalRetry.current)
        socketRef.current.emit('join_room_conversation', {
          conversationId
        })
      })

      socketRef.current.on('connect_error', () => {
        if (intervalRetry.current) clearInterval(intervalRetry.current)
        retryConnect()
      })

      return () => {
        if (socketRef.current) {
          socketRef.current.close();
        }
        if (intervalRetry.current) clearInterval(intervalRetry.current);
      }
    }
  }, [flow, conversationId])

  const handlePublishFlow = useCallback(() => {
    postMessage({type: "publish_flow", data: flow})
  }, [flow])

  const handleRevertFlow = useCallback(() => {
    postMessage({type: "revert_flow", data: flow})
  }, [flow])

  const togglePreview = useCallback(() => {
    const newPreviewState = !previewOpened
    postMessage({type: "toggle_preview", data: newPreviewState})
    setPreviewOpened(newPreviewState)

    if (!newPreviewState) {
      setDebugMode(false)
      postMessage({type: "debug_mode", data: false})

      // Also close debug panel when preview is closed
      setDebugPanelActive(false)
      window.postMessage({type: "toggle_debug_panel", data: false}, '*')
      if (window.parent !== window) {
        window.parent.postMessage({type: "toggle_debug_panel", data: false}, '*')
      }
    }
  }, [flowId, previewOpened])

  const toggleDebugMode = useCallback((checked: boolean) => {
    if (status !== STUDIO_STATUS.DEV) {
      return
    }

    setDebugMode(checked)

    postMessage({
      type: "debug_mode",
      data: checked
    })

    postMessage({
      type: "debug_context",
      data: {
        debug: checked,
        studio_status: status,
        flow_id: flowId,
        preview_opened: previewOpened,
        timestamp: new Date().toISOString()
      }
    })

  }, [status, flowId, previewOpened])

  const toggleDebugPanel = useCallback(() => {
    const newDebugPanelState = !debugPanelActive
    setDebugPanelActive(newDebugPanelState)

    // Try both window and parent window to ensure message delivery
    window.postMessage({
      type: "toggle_debug_panel",
      data: newDebugPanelState
    }, '*')

    if (window.parent !== window) {
      window.parent.postMessage({
        type: "toggle_debug_panel",
        data: newDebugPanelState
      }, '*')
    }
  }, [debugPanelActive])

  const handleOpenModalConfirm = useCallback(() => {
    setModalConfirmOpened(true)
  }, [])

  const handleSave = async () => {
    if (flowCanvasRef.current) {
      await flowCanvasRef.current.triggerSave();
    }
  }

  const handleOk = () => {
    setConfirmOk(true)
    setModalConfirmOpened(false)
  }

  const handleCancel = () => {
    setConfirmCancel(true)
    setModalConfirmOpened(false)
  }

  const handleBackToAgentTool = () => {
    postMessage({
      type: "back_to_agent_tool",
      data: true
    })
  }

  return (
    <>
      {contextHolder}
      <ConfigProvider
        theme={{
          token: {
            fontFamily: "Plus Jakarta Sans"
          },
          components: {
            Layout: {
              headerBg: "transparent",
              bodyBg: "transparent",
              siderBg: "transparent",
              triggerBg: "#7241ff",
            },
            Segmented: {
              itemColor: "#6F767E",
              itemHoverColor: "#ffffff",
              itemSelectedBg: "#272B30",
              itemSelectedColor: "#ffffff",
              trackBg: "#1A1D1F",
              borderRadius: 12,
              borderRadiusXS: 12,
              borderRadiusSM: 12,
              borderRadiusLG: 12,
              borderRadiusOuter: 12
            },
            Modal: {
              contentBg: theme === 'light' ? '#EBEBEF' : '#1E232E',
              headerBg: theme === 'light' ? '#EBEBEF' : '#1E232E',
              footerBg: theme === 'light' ? '#EBEBEF' : '#1E232E',
              borderRadius: 12,
              borderRadiusXS: 12,
              borderRadiusSM: 12,
              borderRadiusLG: 12,
              borderRadiusOuter: 12,
              colorText: 'white'
            },
            Select: {
              activeBorderColor: theme === 'light' ? '#D2D6DD' : '#292E39',
              activeOutlineColor: theme === 'light' ? '#d2ceee' : '#343942',
              borderRadius: 12
            }
          },
        }}
      >
        <Layout>
          <Header className="!p-0">
            <div className="h-full flex items-center justify-between">
              <div className="flex items-center justify-start space-x-3">
                <RiArrowLeftSLine onClick={handleBackToAgentTool} className="!w-8 !h-8 rounded-full cursor-pointer text-neutral-content dark:text-neutral-content hover:bg-base-400 dark:hover:bg-dark-base-400"/>
                {flow && (
                  <div className="mb-1.5 text-2xl font-semibold text-base-content dark:text-dark-base-content">
                    <span>[ </span>{flow.name}<span> ]</span>
                  </div>
                )}
              </div>
              <div className="flex items-center justify-end space-x-3">
                {previewOpened ? (
                  <div className="flex items-center justify-end space-x-3">
                     {status === STUDIO_STATUS.DEV && (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-neutral-content dark:text-dark-neutral-content">Debug</span>
                        <Switch style={{backgroundColor: debugMode ? '#9DC390' : '#6F767E', color: 'black'}} onChange={toggleDebugMode}/>
                      </div>
                     )}
                    <div className="h-full flex items-center">
                      <button
                        type="button"
                        className={`rounded-[12px] h-10 px-4 border flex items-center justify-center button-disabled cursor-pointer ${
                          debugPanelActive
                            ? 'bg-primary text-primary-content border-primary'
                            : 'bg-base-100 dark:bg-dark-base-100 border-neutral-content dark:border-dark-neutral-content text-neutral-content dark:text-dark-neutral-content'
                        }`}
                        onClick={toggleDebugPanel}
                      >
                        <span>Debug Panel</span>
                      </button>
                    </div>
                    <div className="h-full flex items-center">
                      <button
                        type="button"
                        className="rounded-[12px] h-10 px-4 bg-base-100 dark:bg-dark-base-100 border border-neutral-content dark:border-dark-neutral-content text-neutral-content dark:text-dark-neutral-content flex items-center justify-center button-disabled cursor-pointer"
                        onClick={togglePreview}
                      >
                        <span>Hide preview</span>
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="h-full flex items-center">
                    <button
                      type="button"
                      className="rounded-[12px] h-10 px-4 bg-base-100 dark:bg-dark-base-100 border border-info text-info flex items-center justify-center button-disabled cursor-pointer"
                      onClick={togglePreview}
                    >
                      <span>Preview</span>
                    </button>
                  </div>
                )}
                {status && status === STUDIO_STATUS.LIVE && (
                  <div className="h-full flex items-center">
                    <button
                      type="button"
                      onClick={handleRevertFlow}
                      className="rounded-[12px] h-10 flex items-center justify-center text-white button-disabled cursor-pointer"
                    >
                      <span>Revert changes</span>
                    </button>
                  </div>
                )}
                {status && status === STUDIO_STATUS.DEV && (
                  <>
                    {/* <div className="h-full flex items-center">
                        <button type="button"
                                style={{height: 48, width: 128}}
                                onClick={handleGetOldDataFlow}
                                className="bg-light-black rounded-lg flex items-center justify-center text-white button-disabled">
                          <span>Undo data</span>
                        </button>
                      </div> */}
                    {/* <div className="h-full flex items-center">
                      <button
                        type="button"
                        onClick={handlePublishFlow}
                        className="rounded-[12px] h-10 px-4 bg-success text-success-content flex items-center justify-center button-disabled cursor-pointer"
                      >
                        <span>Publish changes</span>
                      </button>
                    </div> */}
                  </>
                )}
                <div className="h-full flex items-center">
                  <button
                    type="button"
                    className="rounded-[12px] h-10 px-6 bg-primary text-primary-content relative flex items-center justify-center button-disabled cursor-pointer"
                    onClick={() => handleSave()}
                  >
                    <span>Save</span>
                    {isDirty !== 0 && <div className="absolute right-3 text-red-600">*</div>}
                  </button>
                </div>
              </div>
            </div>
          </Header>
          <Layout className="!bg-base-100 dark:!bg-dark-base-100">
            <Sider
              trigger={null}
              collapsible
              collapsed={listNodeCollapsed}
              onCollapse={(value) => setListNodeCollapsed(value)}
              collapsedWidth={0}
              width={256}
            >
              <MenuPrompts/>
              <CollapseButton
                collapsed={listNodeCollapsed}
                onClick={() => setListNodeCollapsed(!listNodeCollapsed)}
              >
                <RiArrowRightSFill/>
              </CollapseButton>
            </Sider>
            <Layout>
              <Spin indicator={<LoadingOutlined spin/>} size="large" spinning={isLoading}>
                <StyledFlow>
                  <FlowCanvas
                    ref={flowCanvasRef}
                    flowId={flowId}
                    listNodeCollapsed={listNodeCollapsed}
                    socket={socketRef.current}
                    conversationId={conversationId}
                    messageApi={messageApi}
                    onOpenModalConfirm={handleOpenModalConfirm}
                  />
                </StyledFlow>
              </Spin>
            </Layout>
          </Layout>
        </Layout>

        <Modal
          title={'Restore Previous Save'}
          centered
          maskClosable={false}
          closable={false}
          open={modalConfirmOpened}
          onOk={handleOk}
          onCancel={handleCancel}
          footer={null}
          width={'18vw'}
          styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        >
          <div className="flex flex-col space-y-3 mt-3">
            <div className="flex flex-col justify-center space-y-2">
              <Typography.Text className="text-white w-full text-left">Do you want to restore the previous incomplete
                save?</Typography.Text>
              <Typography.Text className="text-[#fa6505] w-full text-left">All current changes will be
                overwritten.</Typography.Text>
            </div>

            <div className="w-full flex justify-end items-center space-x-4">
              <StyledButtonModal type="button" key="cancel" $bgColor={theme === "dark" ? "white" : "#ececec"} $color={"black"}
                                 onClick={() => setModalConfirmOpened(false)}>
                Cancel
              </StyledButtonModal>
              <StyledButtonModal
                type="button"
                key="ok"
                $bgColor={"#fa6505"}
                $color={"white"}
                disabled={status && status === STUDIO_STATUS.LIVE}
              >
                OK
              </StyledButtonModal>
            </div>
          </div>
        </Modal>
      </ConfigProvider>
    </>
  )
}

export default EditorFlowView
