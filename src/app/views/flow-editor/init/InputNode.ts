// @ts-nocheck
import { NodeFlow } from '@flow-editor/model';

export const inputNode: NodeFlow = {
  id: 'input_0',
  type: '_input',
  position: { x: 50, y: 250 },
  name: 'Input',
  description: 'Start the flow by receiving an input.',
  icon: 'RiLoginBoxFill',
  usage_type: 'DEFAULT',
  status: 'SUPPORTING',
  data: {
    node_type: 'input',
    node_color: '#f3b90c',
    label: 'Input',
    name: 'input',
    description: 'Send a message to others with this node.',
    status: 'SUPPORTING',
    icon: 'RiLoginBoxFill',
    parentId: null,
    extent: null,
    draggable: true,
    output_key: '',
  },
  style: {},
};
