import { Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UIStore } from '@core/stores';
import { DxCard, DxCardContent } from '@dx-ui/ui';
import { SettingsService } from '../../shared/services/settings.service';

@Component({
  selector: 'app-invite-join',
  templateUrl: './invite-join.component.html',
  styleUrls: ['./invite-join.component.scss'],
  imports: [DxCard, DxCardContent],
})
export class InviteJoinComponent implements OnInit {
  invitedSuccess = signal<boolean | null>(null);
  countdown = signal<number>(5);

  uiStore = inject(UIStore);
  private settingsService = inject(SettingsService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  ngOnInit(): void {
    const inviteToken = this.route.snapshot.paramMap.get('invite_token');

    if (inviteToken) {
      this.settingsService.activeInvite(inviteToken).subscribe({
        next: (response) => {
          this.invitedSuccess.set(true);
          this.startCountdown();
        },
        error: (error) => {
          this.invitedSuccess.set(false);
        },
      });
    }
  }

  startCountdown() {
    const interval = setInterval(() => {
      this.countdown.set(this.countdown() - 1);
      if (this.countdown() === 0) {
        clearInterval(interval);
        this.router.navigate(['/auth/sign-in']);
      }
    }, 1000);
  }
}
