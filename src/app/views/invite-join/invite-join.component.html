<div class="h-screen max-w-screen flex justify-center items-center">
  @if (invitedSuccess()) {
  <dx-card appearance="outlined" class="card-dashboard">
    <dx-card-content>
      <div class="flex flex-col justify-center items-center">
        @if(uiStore.theme() === 'light') {
        <img
          src="assets/img/invite-light.png"
          class="w-20 h-20 object-fit mb-1"
        />
        } @else {
        <img
          src="assets/img/invite-dark.png"
          class="w-20 h-20 object-fit mb-1"
        />
        }
        <h1 class="text-2xl font-semibold text-primary mb-8">
          Join AI Successfully
        </h1>
        <div
          class="text-[14px] text-neutral-content dark:text-dark-neutral-content"
        >
          You will be redirected to the login page in
          {{ countdown() }} seconds.
        </div>
        <div class="flex items-center space-x-1">
          <div
            class="text-[14px] text-neutral-content dark:text-dark-neutral-content"
          >
            If you are not redirected, please click
          </div>
          <a
            routerLink="/auth/sign-in"
            class="text-[14px] text-primary cursor-pointer hover:underline"
            >here</a
          >
          <div
            class="text-[14px] text-neutral-content dark:text-dark-neutral-content"
          >
            to log in
          </div>
        </div>
      </div>
    </dx-card-content>
  </dx-card>
  } @else {
  <dx-card appearance="outlined" class="card-dashboard">
    <dx-card-content>
      <div class="flex flex-col justify-center items-center">
        @if(uiStore.theme() === 'light') {
        <img
          src="assets/img/invite-light.png"
          class="w-20 h-20 object-fit mb-1"
        />
        } @else {
        <img
          src="assets/img/invite-dark.png"
          class="w-20 h-20 object-fit mb-1"
        />
        }
        <h1 class="text-2xl font-semibold text-error mb-8">Join AI Failed</h1>
        <div
          class="text-[14px] text-neutral-content dark:text-dark-neutral-content mb-1"
        >
          There was a problem when you joined AI.
        </div>
        <div class="flex items-center space-x-1 mb-6">
          <div
            class="text-[14px] text-neutral-content dark:text-dark-neutral-content"
          >
            Please try contacting us directly at
          </div>
          <a
            href="mailto:contact&#64;dxgpt.ai"
            class="text-[14px] text-primary cursor-pointer hover:underline"
            >contact&#64;dxgpt.ai</a
          >
          <div
            class="text-[14px] text-neutral-content dark:text-dark-neutral-content"
          >
            for assistance.
          </div>
        </div>
        <div class="flex items-center space-x-1">
          <div
            class="text-[14px] text-neutral-content dark:text-dark-neutral-content"
          >
            Click
          </div>
          <a
            routerLink="/auth/sign-in"
            class="text-[14px] text-primary cursor-pointer hover:underline"
            >here</a
          >
          <div
            class="text-[14px] text-neutral-content dark:text-dark-neutral-content"
          >
            to log in
          </div>
        </div>
      </div>
    </dx-card-content>
  </dx-card>
  }
</div>
