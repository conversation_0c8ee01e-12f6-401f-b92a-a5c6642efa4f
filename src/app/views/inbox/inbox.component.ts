import { CommonModule, DatePipe } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  computed,
  effect,
  inject,
  OnDestroy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { SocketStore, UIStore } from '@core/stores';
import { IConversation, IMessage } from '@shared/models';
import { filter, map, Subscription, switchMap } from 'rxjs';

import { OverlayModule } from '@angular/cdk/overlay';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MAT_DATE_FORMATS, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { TYPE_INTEGRATION } from '@shared/app.constant';
import {
  ExportConfigType,
  IExportMessageBody,
} from '@shared/models/export.model';

import {
  DxButton,
  DxCard,
  DxCardContent,
  DxCardHeader,
  DxDialog,
  DxFormField,
  DxInput,
  DxLabel,
  DxOption,
  DxPrefix,
  DxSelect,
  DxSnackBar, DxTooltip,
} from '@dx-ui/ui';
import { heroPencilSquareMini } from '@ng-icons/heroicons/mini';
import {
  heroArrowLeftOnRectangle,
  heroArrowRightOnRectangle,
  heroCheckCircle,
  heroChevronLeft,
  heroInformationCircle,
  heroMagnifyingGlass,
  heroXCircle,
} from '@ng-icons/heroicons/outline';
import {
  MobileDrawerComponent,
  MobileHeaderComponent,
  SvgIconComponent,
} from '@shared/components';
import {ClickOutsideDirective, MHeaderLeftDirective} from '@shared/directives';
import {
  ExportConfigService,
  MessagesService,
  TagService,
} from '@shared/services';
import { ChatUtils } from '@shared/utils';
import moment from 'moment';
import { InfiniteScrollDirective } from 'ngx-infinite-scroll';
import { ChatContentComponent } from './components/chat-content/chat-content.component';
import { ConversationListComponent } from './components/conversation-list/conversation-list.component';
import { FAQDialogComponent } from './components/faq-dialog/faq-dialog.component';
import { TagDialogComponent } from './components/tag-dialog/tag-dialog.component';
import {BdcWalkPopupComponent, BdcWalkTriggerDirective} from 'bdc-walkthrough';

// Custom date formats to match the application's format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'DD/MM/YYYY',
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-inbox',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    NgIconsModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatFormFieldModule,
    MatInputModule,
    SvgIconComponent,
    ClickOutsideDirective,
    DxCard,
    DxCardContent,
    DxCardHeader,
    DxInput,
    DxFormField,
    DxPrefix,
    DxSelect,
    DxOption,
    DxLabel,
    DxButton,
    MatCheckboxModule,
    OverlayModule,
    ConversationListComponent,
    ChatContentComponent,
    DxButton,
    MobileHeaderComponent,
    MobileDrawerComponent,
    DxTooltip,
    MHeaderLeftDirective,
    BdcWalkPopupComponent,
    BdcWalkTriggerDirective
  ],
  templateUrl: './inbox.component.html',
  styleUrl: './inbox.component.css',
  providers: [
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS },
    provideIcons({
      heroInformationCircle,
      heroArrowLeftOnRectangle,
      heroArrowRightOnRectangle,
      heroPencilSquareMini,
      heroCheckCircle,
      heroMagnifyingGlass,
      heroXCircle,
      heroChevronLeft
    }),
  ],
})
export class InboxComponent implements OnInit, OnDestroy {
  @ViewChild(InfiniteScrollDirective)
  infiniteScrollDirective!: InfiniteScrollDirective;
  @ViewChild(ConversationListComponent)
  conversationListComponent!: ConversationListComponent;
  // Convert all state properties to signals
  isChatListActive = signal(true);
  chatMessages = signal<IMessage[]>([]);
  conversations = signal<IConversation[]>([]);
  selectedConversationIdx = signal(-1);
  selectedConversationId = signal('');
  isTyping = signal(false);
  message = signal('');
  currentConversationPage = signal(1);
  pageSize = signal(25);
  isLoadingConversation = signal(true);
  isLoadingContentConversation = signal(false);
  showMessageInbox = signal(false);
  isChatWithAI = signal(false);
  isSendingMessage = signal(false);
  nameConversationNew = signal('');
  openExportOptions = signal(false);
  totalConversations = signal(0);
  hasMoreData = signal(true);
  viewMDetailConv = signal(false);
  isProcessingAction = signal(false);

  // Filter signals
  viewFilter = signal<boolean>(false);
  filterName = signal('');
  filterKeyWord = signal('');
  filterStatus = signal('');
  filterTags = signal<any[]>([]);

  // Date signals
  timeFrom = signal(moment().subtract(12, 'month').toDate());
  timeTo = signal(moment().toDate());
  timeNow = signal(moment().toDate());

  // Tag signals
  listTagForConversation = signal<any[]>([]);
  listTagForConversationFiltered = signal<any[]>([]);
  listTagSelectedInConversation = signal<any[]>([]);
  listTagForConversationAvailable = signal<any[]>([]);

  // Conversation status signals
  conversationStatusInfo = signal<{
    action: 'takeOver' | 'resume';
    isChatWithAI: boolean;
  } | null>(null);

  // Computed signals
  selectedConversation = computed(() =>
    this.conversations().find((c) => c.id === this.selectedConversationId())
  );

  hasConversations = computed(() => this.conversations().length > 0);

  canLoadMore = computed(
    () => this.hasMoreData() && !this.isLoadingConversation()
  );

  filter = computed(() => ({
    name: this.filterName(),
    key_word: this.filterKeyWord(),
    status: this.filterStatus(),
    tags: this.filterTags(),
    from_date_update: moment(this.timeFrom()).format('YYYY-MM-DD'),
    to_date_update: moment(this.timeTo()).format('YYYY-MM-DD'),
  }));

  searchContent = computed(() => this.filterKeyWord());
  searchName = computed(() => this.filterName());

  // Constants
  TYPE_INTEGRATION = TYPE_INTEGRATION;
  author: any = {
    type: 'human_operator',
  };

  listStatus: any[] = [
    {
      label: 'All',
      code: '',
    },
    {
      label: 'Open',
      code: 'OPEN',
    },
    {
      label: 'Need reply',
      code: 'REPLY',
    },
  ];

  // Forms
  faqForm!: FormGroup;
  selectedMessageForFAQ!: IMessage;
  getNewMessageSubscription!: Subscription;
  // tagKeyword = '';
  listTag: number[] = [];
  closeFn = () => this.viewFilter.set(false);
  private hasCheckedConversationId = false;
  private isCheckingConversation: any;
  private searchTimeout: any;
  private socketStore = inject(SocketStore);
  private subscription: Subscription = new Subscription();

  uiStore = inject(UIStore);
  private chatService = inject(MessagesService);
  private tagService = inject(TagService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private exportConfigService = inject(ExportConfigService);
  private cdr = inject(ChangeDetectorRef);
  private fb = inject(FormBuilder);
  private dialog = inject(DxDialog);
  private snackbar = inject(DxSnackBar);

  constructor() {
    this.initForms();
    this.initializeSocketConnection();
    this.setupEffects();
  }

  private setupEffects(): void {
    // Effect for tag changes subscription
    effect(() => {
      const tagSubscription = this.tagService.tagAssignmentChanged$.subscribe(
        (event) => {
          if (event.conversationId === this.selectedConversationId()) {
            // Update tags cho conversation hiện tại
            this.loadConversationTags(event.conversationId);
          }

          // Update tags trong conversation list
          const conversations = this.conversations();
          const conversationIndex = conversations.findIndex(
            (c) => c.id === event.conversationId
          );
          if (conversationIndex !== -1) {
            const updatedConversations = [...conversations];
            if (event.action === 'assign') {
              // Map tag IDs to tag objects
              const assignedTags = this.listTagForConversation().filter(
                (tag: any) => event.tagIds.includes(tag.id)
              );
              updatedConversations[conversationIndex].matchedTags =
                assignedTags;
            } else if (event.action === 'unassign') {
              // Remove unassigned tags
              updatedConversations[conversationIndex].matchedTags =
                updatedConversations[conversationIndex].matchedTags?.filter(
                  (tag: any) => !event.tagIds.includes(tag.id)
                ) || [];
            }
            this.conversations.set(updatedConversations);
          }
        }
      );

      this.subscription.add(tagSubscription);
    });

    // Effect for selected conversation changes
    effect(() => {
      const conversationId = this.selectedConversationId();
      if (conversationId && this.hasConversations()) {
        const selectedIdx = this.conversations().findIndex(
          (c) => c.id === conversationId
        );
        this.selectedConversationIdx.set(selectedIdx);

        // Check and update isChatWithAI status
        const selectedConversation = this.selectedConversation();
        if (selectedConversation) {
          this.isChatWithAI.set(
            Boolean(
              selectedConversation.assigned &&
                selectedConversation.assigned !== 'Unassigned'
            )
          );
        } else {
          this.isChatWithAI.set(false);
        }
      }
    });
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.getListTag();
      this.checkConversationId();
      this.initializeFilter(params);
      this.doSearch();
      this.subscribeToTagChanges();
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    if (this.getNewMessageSubscription) {
      this.getNewMessageSubscription.unsubscribe();
    }
  }

  // Subscribe to tag assignment changes
  private subscribeToTagChanges(): void {
    const tagSubscription = this.tagService.tagAssignmentChanged$.subscribe(
      (event) => {
        if (event.conversationId === this.selectedConversationId()) {
          // Update tags cho conversation hiện tại
          this.loadConversationTags(event.conversationId);
        }

        // Update tags trong conversation list
        const conversations = this.conversations();
        const conversationIndex = conversations.findIndex(
          (c) => c.id === event.conversationId
        );
        if (conversationIndex !== -1) {
          const updatedConversations = [...conversations];
          if (event.action === 'assign') {
            // Map tag IDs to tag objects
            const assignedTags = this.listTagForConversation().filter(
              (tag: any) => event.tagIds.includes(tag.id)
            );
            updatedConversations[conversationIndex].matchedTags = assignedTags;
          } else if (event.action === 'unassign') {
            // Remove unassigned tags
            updatedConversations[conversationIndex].matchedTags =
              updatedConversations[conversationIndex].matchedTags?.filter(
                (tag: any) => !event.tagIds.includes(tag.id)
              ) || [];
          }
          this.conversations.set(updatedConversations);
        }

        // Trigger change detection để update UI
        this.cdr.detectChanges();
      }
    );

    // Add subscription to subscription list để cleanup khi component destroy
    this.subscription.add(tagSubscription);
  }

  // Initialization methods
  private initForms(): void {
    this.faqForm = this.fb.group({
      question: ['', Validators.required],
      answer: ['', Validators.required],
    });
  }

  private initializeSocketConnection(): void {
    // Socket connection logic
  }

  private initializeFilter(params?: any): void {
    this.filterName.set(params?.name || '');
    this.filterKeyWord.set(params?.keyword || '');
    this.filterStatus.set(params?.status || '');
    this.filterTags.set(params?.tags ? params.tags.split(',') : []);

    // Xử lý timeFrom và timeTo
    if (params?.from) {
      this.timeFrom.set(moment(params.from, 'YYYY-MM-DD').toDate());
    } else {
      this.timeFrom.set(moment().subtract(12, 'month').toDate());
    }
    if (params?.to) {
      this.timeTo.set(moment(params.to, 'YYYY-MM-DD').toDate());
    } else {
      this.timeTo.set(moment().toDate());
    }
  }

  // Event handlers from child components
  onConversationSelected(conversationId: string): void {
    this.selectedConversationId.set(conversationId);
    this.selectedConversationIdx.set(
      this.conversations().findIndex((c) => c.id === conversationId)
    );

    // Check and update isChatWithAI status based on conversation's assigned field
    const selectedConversation = this.conversations().at(
      this.selectedConversationIdx()
    );
    if (selectedConversation) {
      this.isChatWithAI.set(
        Boolean(
          selectedConversation.assigned &&
            selectedConversation.assigned !== 'Unassigned'
        )
      );
    } else {
      // Default to false if conversation not found
      this.isChatWithAI.set(false);
    }

    this.showMessageInbox.set(true);
    this.loadMessages(conversationId);
    this.loadConversationTags(conversationId);
  }

  mConversationSelectedViewed(val: boolean) {
    this.viewMDetailConv.set(val);
  }

  onConversationScrolled(): void {
    this.onScroll();
  }

  getListTag() {
    const paginator = {
      page: 1,
      page_size: 99999,
    };
    const body = {
      application: 'CONVERSATION',
    };
    this.tagService.getListTag(paginator, body).subscribe((res) => {
      console.log(res)
      this.listTagForConversation.set(res.items);
      this.listTagForConversationFiltered.set(res.items);
      this.doSearch();
    });

    const bodyForFilter = {
      application: 'CONVERSATION',
      is_active: true,
    };
    this.tagService.getListTag(paginator, bodyForFilter).subscribe((res) => {
      this.listTagForConversationAvailable.set(res.items);
    });
  }

  doSearch() {
    if (this.infiniteScrollDirective) {
      // Reset the infinite scroll by destroying and recreating it
      // We can't directly access private properties, so we'll use a different approach
      const element = document.querySelector('.list-conversation');
      if (element) {
        // Trigger a scroll event to reset the infinite scroll
        element.scrollTop = 0;
      }
    }
    this.currentConversationPage.set(1);
    this.hasMoreData.set(true); // Reset pagination state
    // Update the Moment objects from the Date objects in the UI
    this.timeFrom.set(moment(this.timeFrom()).toDate());
    this.timeTo.set(moment(this.timeTo()).toDate());

    this.filterName.set(this.filter().name);
    this.filterKeyWord.set(this.filter().key_word);
    this.filterStatus.set(this.filter().status);
    this.filterTags.set(this.filter().tags);
    const params = {
      page: this.currentConversationPage(),
      page_size: this.pageSize(),
    };

    this.updateAllFilterParams();
    this.getListConversations(this.filter(), params);
  }

  getListConversations(body: any, params: any, callback?: any): void {
    this.chatService.getListConversations(body, params).subscribe({
      next: (res) => {
        this.totalConversations.set(res.total || 0);

        if (res.items && res.items.length > 0) {
          this.conversations.set(
            res.items.map((item: any, index: number) => ({
              ...item,
              name:
                item.name ??
                `Conversation #${
                  res.total -
                  this.pageSize() * (this.currentConversationPage() - 1) -
                  index
                }`,
              isEditName: false,
              matchedTags: this.mapConversationTags(
                item.tags,
                this.listTagForConversation()
              ),
            }))
          );
          // console.log(this.conversations())
          // Check if there are more items to load
          this.hasMoreData.set(
            this.conversations().length < this.totalConversations()
          );
        } else {
          this.conversations.set([]);
          this.hasMoreData.set(false);
        }
        if (!this.hasCheckedConversationId) {
          this.checkConversationId();
        }
        this.cdr.detectChanges();
        this.isLoadingConversation.set(false);
      },
      error: (err) => {
        this.isLoadingConversation.set(false);
        this.hasMoreData.set(false);
      },
    });
  }

  onSelectConversation(conversationId: string): void {
    this.selectedConversationId.set(conversationId);

    // Check and update isChatWithAI status based on conversation's assigned field
    const selectedConversation = this.conversations().find(
      (c) => c.id === conversationId
    );
    if (selectedConversation) {
      this.isChatWithAI.set(
        Boolean(
          selectedConversation.assigned &&
            selectedConversation.assigned !== 'Unassigned'
        )
      );
    } else {
      // Default to false if conversation not found
      this.isChatWithAI.set(false);
    }

    this.showMessageInbox.set(true);
    this.loadMessages(conversationId);
  }

  onMessageChange(message: string): void {
    this.message.set(message);
  }

  onAddTag(): void {
    this.openTagDialog();
  }

  onOpenSaveFAQDialog(message: IMessage): void {
    this.openSaveFAQDialog(message);
  }

  onExportUsingS3(): void {
    this.exportUsingS3();
  }

  onConversationsUpdated(conversations: IConversation[]): void {
    this.conversations.set(conversations);
    this.cdr.detectChanges();
  }

  onMessagesUpdated(messages: IMessage[]): void {
    this.chatMessages.set(messages);
    this.cdr.detectChanges();
  }

  onSendingStatusChange(status: boolean): void {
    this.isSendingMessage.set(status);
    this.cdr.detectChanges();
  }

  onReloadChatMessages(): void {
    if (this.selectedConversationId()) {
      this.loadMessages(this.selectedConversationId());
    }
  }

  onTakeOverConversation(): void {
    if (this.isProcessingAction()) return;
    this.isProcessingAction.set(true);

    const params = {
      conversation_id: this.selectedConversationId(),
    };

    this.chatService.takeOverConversation(params).subscribe({
      next: (res: any) => {
        this.snackbar.open('Success take over the conversation', '', {
          duration: 2000,
          horizontalPosition: 'right',
          verticalPosition: 'top',
          panelClass: 'dx-snack-bar-success',
        });

        const updatedConversations = this.conversations().map((c) =>
          c.id === this.selectedConversationId()
            ? { ...c, ...res, isEditName: false }
            : { ...c, isEditName: false }
        );
        this.conversations.set(updatedConversations);

        const isChatWithAI = Boolean(
          res.assigned && res.assigned !== 'Unassigned'
        );
        this.isChatWithAI.set(isChatWithAI);

        this.conversationStatusInfo.set({
          action: 'takeOver',
          isChatWithAI: isChatWithAI,
        });

        this.loadMessages(this.selectedConversationId());

        this.isProcessingAction.set(false);
      },
      error: (err: any) => {
        this.snackbar.open('Failed to take over conversation', '', {
          duration: 2000,
          horizontalPosition: 'right',
          verticalPosition: 'top',
          panelClass: 'dx-snack-bar-error',
        });
        this.isProcessingAction.set(false);
      },
    });
  }

  onResumeConversation(): void {
    if (this.isProcessingAction()) return;
    this.isProcessingAction.set(true);

    const params = {
      conversation_id: this.selectedConversationId(),
    };

    this.chatService.returnToAIConversation(params).subscribe({
      next: (res: any) => {
        this.snackbar.open('Success return to AI the conversation', '', {
          duration: 2000,
          horizontalPosition: 'right',
          verticalPosition: 'top',
          panelClass: 'dx-snack-bar-success',
        });

        const updatedConversations = this.conversations().map((c) =>
          c.id === this.selectedConversationId()
            ? { ...c, ...res, isEditName: false }
            : { ...c, isEditName: false }
        );
        this.conversations.set(updatedConversations);

        const isChatWithAI = Boolean(
          res.assigned && res.assigned !== 'Unassigned'
        );

        this.isChatWithAI.set(isChatWithAI);

        this.conversationStatusInfo.set({
          action: 'resume',
          isChatWithAI: isChatWithAI,
        });

        this.loadMessages(this.selectedConversationId());

        this.isProcessingAction.set(false);
      },
      error: (err: any) => {
        this.snackbar.open('Failed to resume conversation', '', {
          duration: 2000,
          horizontalPosition: 'right',
          verticalPosition: 'top',
          panelClass: 'dx-snack-bar-error',
        });
        this.isProcessingAction.set(false);
      },
    });
  }

  onConversationStatusChanged(event: {
    conversationId: string;
    action: 'takeOver' | 'resume';
    isChatWithAI: boolean;
  }): void {
    // Update isChatWithAI status
    this.isChatWithAI.set(event.isChatWithAI);

    // Update conversation status info for ChatContentComponent
    this.conversationStatusInfo.set({
      action: event.action,
      isChatWithAI: event.isChatWithAI,
    });

    // If this is the currently selected conversation, reload messages to reflect the new status
    if (event.conversationId === this.selectedConversationId()) {
      this.loadMessages(event.conversationId);
    }

    // Reset conversation status info after a delay to avoid repeated notifications
    setTimeout(() => {
      this.conversationStatusInfo.set(null);
    }, 1000);

    this.cdr.detectChanges();
  }

  onCloseDialog(): void {
    // Close any open dialog
  }

  onDeleteConversation(): void {
    // Handle conversation deletion
    console.log('Delete conversation');
  }

  // Các method còn thiếu từ component gốc
  toggleChatList(): void {
    this.isChatListActive.set(!this.isChatListActive());
  }

  onNameSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.doSearch();
    }, 500);
  }

  onContentSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.doSearch();
    }, 500);
  }

  onDateFromMatChange(event: any): void {
    this.timeFrom.set(event.value);
    this.doSearch();
  }

  onDateToMatChange(event: any): void {
    this.timeTo.set(event.value);
    this.doSearch();
  }

  dateClass = (date: Date): string => {
    const today = new Date();
    const selectedDate = new Date(date);

    // Reset time parts for comparison
    today.setHours(0, 0, 0, 0);
    selectedDate.setHours(0, 0, 0, 0);

    if (selectedDate > today) {
      return 'future-date';
    }
    return '';
  };

  exportUsingS3(): void {
    this.exportConfigService
      .getExportConfig()
      .pipe(
        filter((res) => !!res && res.length > 0),
        map(
          (res) =>
            res.filter(
              (config) => config && config.type_config === ExportConfigType.S3
            )[0]
        ),
        switchMap((config) => {
          const body: IExportMessageBody = {
            id: config.id as number,
            from_date: moment(this.timeFrom()).format('YYYY-MM-DD'),
            to_date: moment(this.timeTo()).format('YYYY-MM-DD'),
          };
          return this.exportConfigService.exportMessage(body);
        })
      )
      .subscribe({
        next: () => {
          this.snackbar.open('Exported', '', {
            duration: 2000,
            horizontalPosition: 'right',
            verticalPosition: 'top',
            panelClass: 'dx-snack-bar-success',
          });
          this.openExportOptions.set(false);
        },
        error: (error) => {
          this.snackbar.open(error.error.detail, '', {
            duration: 2000,
            horizontalPosition: 'right',
            verticalPosition: 'top',
            panelClass: 'dx-snack-bar-error',
          });
          this.openExportOptions.set(false);
        },
      });
  }

  // Load messages
  private loadMessages(conversation_id: string) {
    this.isLoadingContentConversation.set(true);
    this.showMessageInbox.set(true);
    this.chatMessages.set([]);

    this.chatService.getMessageByConversationId(conversation_id).subscribe({
      next: (res) => {
        this.chatMessages.set(
          res.map((item: any) => {
            const author = item.author ? JSON.parse(item.author) : null;
            const typeMessage = ChatUtils.classifyMessage(item.content);
            const showSource = ChatUtils.isShowSource(item.content);

            return {
              ...item,
              summary: author
                ? ChatUtils.extractUrls(author?.summary ?? '')
                : null,
              role: author ? author?.type : null,
              user: author ? author?.user : null,
              typeMessage: typeMessage,
              showSource: showSource,
              content: ChatUtils.extractUrls(
                ChatUtils.convertMarkdownToImgHTML(item.content.trim()),
                author ? author?.type : null
              ),
              haveMessage: Boolean(
                (
                  (
                    ChatUtils.extractUrls(
                      item.content,
                      author ? author?.type : null
                    ) || ''
                  ).match(/<img\s+[^>]*src="([^"]*)"[^>]*>/g) || []
                ).length
              ),
              listImages:
                typeMessage == 'grid' || typeMessage == 'gallery'
                  ? ChatUtils.extractImages(item.content)
                  : [],
              listTextSources: showSource
                ? ChatUtils.extractSources(item.content)
                : [],
            };
          })
        );

        this.isLoadingContentConversation.set(false);
        this.cdr.detectChanges();
      },
      error: (err) => {
        this.isLoadingContentConversation.set(false);
        this.snackbar.open('Failed to load conversation messages', '', {
          duration: 2000,
          horizontalPosition: 'right',
          verticalPosition: 'top',
          panelClass: 'dx-snack-bar-error',
        });
      },
    });
  }

  private loadConversationTags(conversationId: string): void {
    // For now, we'll use empty tags or existing conversation tags
    this.listTagSelectedInConversation.set(
      this.conversations().find((c) => c.id === conversationId)?.matchedTags ||
        []
    );
  }

  // Check conversation ID from route
  private checkConversationId(): void {
    this.route.queryParams.subscribe((params) => {
      if (params['conversation_id'] && this.conversations().length > 0) {
        const conversationId = params['conversation_id'];
        const conversation = this.conversations().find(
          (c) => c.id === conversationId
        );
        if (conversation) {
          this.onConversationSelected(conversationId);
        }
        this.hasCheckedConversationId = true;
      }
    });
  }

  // Scroll handling
  onScroll(conversationId?: string): void {
    if (this.isLoadingConversation() || !this.hasMoreData()) return;

    // Reset isEditName for all conversations
    this.conversations().forEach((conversation) => {
      conversation.isEditName = false;
    });

    this.currentConversationPage.set(this.currentConversationPage() + 1);
    this.isLoadingConversation.set(true);

    const body = {
      ...this.filter(),
    };
    const params = {
      page: this.currentConversationPage(),
      page_size: this.pageSize(),
    };

    this.chatService.getListConversations(body, params).subscribe({
      next: (res) => {
        this.totalConversations.set(res.total || 0);

        if (res.items && res.items.length > 0) {
          const newConversations = res.items.map(
            (item: any, index: number) => ({
              ...item,
              name:
                item.name ??
                `Conversation #${
                  res.total -
                  this.pageSize() * (this.currentConversationPage() - 1) -
                  index
                }`,
              isEditName: false,
              matchedTags: this.mapConversationTags(
                item.tags,
                this.listTagForConversation()
              ),
            })
          );
          this.conversations.set([
            ...this.conversations(),
            ...newConversations,
          ]);

          // Check if there are more items to load
          this.hasMoreData.set(
            this.conversations().length < this.totalConversations()
          );

          this.cdr.detectChanges();

          if (conversationId) {
            const indexConversation = this.conversations().findIndex(
              (conversation) => conversation.id === conversationId
            );
            if (indexConversation !== -1) {
              this.hasCheckedConversationId = true;
              this.onConversationSelected(conversationId);
              if (this.conversationListComponent) {
                this.conversationListComponent.scrollToConversation(
                  conversationId
                );
              }
            } else if (this.hasMoreData()) {
              this.onScroll(conversationId);
            }
          }
        } else {
          this.hasMoreData.set(false);
        }
        this.isLoadingConversation.set(false);
      },
      error: (err) => {
        this.isLoadingConversation.set(false);
        this.hasMoreData.set(false);
      },
    });
  }

  openTagDialog(): void {
    const tagAddSelected = this.listTagSelectedInConversation().map(
      (tag: any) => tag.id
    );

    this.dialog
      .open(TagDialogComponent, {
        width: '600px',
        data: {
          conversationId: this.selectedConversationId(),
          listTagForConversationAvailable:
            this.listTagForConversationAvailable(),
          tagAddSelected: tagAddSelected,
        },
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe((result: any) => {
        if (result && result.conversationId === this.selectedConversationId()) {
          this.listTagSelectedInConversation.set(result.updatedTags);
          const conversations = this.conversations();
          const conversationIndex = conversations.findIndex(
            (c) => c.id === result.conversationId
          );
          if (conversationIndex !== -1) {
            const updatedConversations = [...conversations];
            updatedConversations[conversationIndex].matchedTags =
              result.updatedTags;
            this.conversations.set(updatedConversations);
          }
          this.cdr.detectChanges();
        }
      });
  }

  openSaveFAQDialog(message: IMessage): void {
    this.selectedMessageForFAQ = message;

    this.dialog.open(FAQDialogComponent, {
      width: '800px',
      data: {
        selectedMessage: message,
        chatMessages: this.chatMessages(),
      },
      minWidth: '340px',
    });
  }

  private mapConversationTags(
    conversationTags: string[] | undefined,
    listConversationTag: any[]
  ): any[] {
    if (!conversationTags || !listConversationTag) return [];

    return listConversationTag.filter((tag) =>
      conversationTags.includes(tag.id)
    );
  }

  trackByFn(index: number, row: any): number {
    return row.id;
  }
  searchTags(keyword: string) {
    this.listTagForConversationFiltered.update((lstTag) =>
      keyword && keyword !== ''
        ? lstTag.filter((tag: any) =>
            tag.name.toLowerCase().includes(keyword.toLowerCase())
          )
        : this.listTagForConversation()
    );
  }

  markAllTags() {
    this.filterTags.set(
      this.listTagForConversationFiltered().length === 0
        ? []
        : this.listTagForConversationFiltered().map((tag: any) => tag.id)
    );
  }

  selectTags(value: number | number[]) {
    if (value && !Array.isArray(value) && value > 0) {
      this.filterTags.set([value]);
    } else if (Array.isArray(value) && value.length > 0) {
      this.filterTags.set(value);
    } else {
      this.filterTags.set([]);
    }
    this.doSearch();
  }

  updateAllFilterParams() {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        from: moment(this.timeFrom()).format('YYYY-MM-DD'),
        to: moment(this.timeTo()).format('YYYY-MM-DD'),
        status: this.filterStatus(),
        keyword: this.filterKeyWord(),
        name: this.filterName(),
        tags: this.filterTags().join(','), // nếu là mảng
        // ... các filter khác nếu có
      },
      queryParamsHandling: 'merge',
    });
  }
}
