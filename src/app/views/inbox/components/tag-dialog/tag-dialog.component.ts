import { CommonModule } from '@angular/common';
import { Component, OnInit, computed, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxLoadingButton,
  DxSnackBar,
} from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';
import { TagService } from '@shared/services';

@Component({
  selector: 'app-tag-dialog',
  templateUrl: './tag-dialog.component.html',
  styleUrls: ['./tag-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCheckboxModule,
    SvgIconComponent,
    DxButton,
    DxLoadingButton,
  ],
  host: {
    class: 'h-full',
  },
})
export class TagDialogComponent implements OnInit {
  // Internal state signals
  conversationId = signal<string>('');
  selectedTags = signal<string[]>([]);
  isLoadingSave = signal(false);

  // Computed signals
  availableTags = computed(
    () => this.data?.listTagForConversationAvailable || []
  );

  hasSelectedTags = computed(() => this.selectedTags().length > 0);

  selectedTagsCount = computed(() => this.selectedTags().length);

  availableTagsCount = computed(() => this.availableTags().length);

  hasChanges = computed(() => {
    const current = [...this.selectedTags()].sort();
    const initial = [...(this.data?.tagAddSelected || [])].sort();
    return JSON.stringify(current) !== JSON.stringify(initial);
  });

  canSave = computed(() => this.hasChanges() && !this.isLoadingSave());

  dialogRef = inject(DxDialogRef<TagDialogComponent>);
  data: any = inject(DIALOG_DATA);
  private tagService = inject(TagService);
  private snackbar = inject(DxSnackBar);

  ngOnInit(): void {
    if (this.data) {
      this.conversationId.set(this.data.conversationId);
      this.selectedTags.set([...(this.data.tagAddSelected || [])]);
    }
  }

  // Handle tag selection locally with signals
  onTagSelectionChange(event: any, tagId: string): void {
    const currentTags = this.selectedTags();

    if (event.checked) {
      if (!currentTags.includes(tagId)) {
        this.selectedTags.set([...currentTags, tagId]);
      }
    } else {
      this.selectedTags.set(currentTags.filter((id) => id !== tagId));
    }
  }

  // Check if tag is selected
  isTagSelected(tagId: string): boolean {
    return this.selectedTags().includes(tagId);
  }

  // Handle save operation with signals
  onSaveAddTag(): void {
    if (!this.canSave()) return;

    this.isLoadingSave.set(true);

    const body = {
      conversation_id: this.conversationId(),
      tag_ids: this.selectedTags(),
    };

    this.tagService.assignTag(body).subscribe({
      next: (res) => {
        this.snackbar.open(res.detail, '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });

        const updatedTags = this.availableTags().filter((tag: any) =>
          this.selectedTags().includes(tag.id)
        );

        this.isLoadingSave.set(false);
        this.dialogRef?.close({
          conversationId: this.conversationId(),
          updatedTags: updatedTags,
        });
      },
      error: (err) => {
        this.snackbar.open('Failed to save tags', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.isLoadingSave.set(false);
      },
    });
  }

  onResetSelections(): void {
    this.selectedTags.set([...(this.data?.tagAddSelected || [])]);
  }

  onSelectAll(): void {
    const allTagIds = this.availableTags().map((tag: any) => tag.id);
    this.selectedTags.set([...allTagIds]);
  }

  // Clear all selections
  onClearAll(): void {
    this.selectedTags.set([]);
  }

  onCloseDialog(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  getColor(config: string): string {
    try {
      const parsedConfig = JSON.parse(config);
      return parsedConfig.color || '#000000';
    } catch (error) {
      return config ? `#${config}` : '#000000';
    }
  }

  getTextColor(bgColor: string): string {
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }
}
