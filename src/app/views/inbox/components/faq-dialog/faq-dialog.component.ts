import { CommonModule } from '@angular/common';
import { Component, Inject, inject, signal, computed, input, output, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BaseComponent, LoadingButtonComponent } from '@shared/components';
import { IMessage } from '@shared/models';
import { FaqService } from '@shared/services';

@Component({
  selector: 'app-faq-dialog',
  templateUrl: './faq-dialog.component.html',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    LoadingButtonComponent,
  ],
})
export class FAQDialogComponent extends BaseComponent implements OnInit {
  // Input signals - not needed since we use MAT_DIALOG_DATA

  // Output signals (replaced @Output)
  faqSaved = output<{ question: string, answer: string }>();

  // Internal state signals
  faqForm = signal<FormGroup | null>(null);
  isLoadingSave = signal(false);
  questionText = signal('');
  answerText = signal('');

  // Computed signals
  hasSelectedMessage = computed(() => !!this.data?.selectedMessage);
  
  canSave = computed(() => {
    const question = this.questionText().trim();
    const answer = this.answerText().trim();
    return question.length > 0 && answer.length > 0 && !this.isLoadingSave();
  });

  formIsValid = computed(() => {
    const form = this.faqForm();
    return form ? form.valid : false;
  });

  hasFormChanges = computed(() => {
    const question = this.questionText().trim();
    const answer = this.answerText().trim();
    return question.length > 0 || answer.length > 0;
  });

  private faqService = inject(FaqService);
  public override fb = inject(FormBuilder);

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: {
      selectedMessage: IMessage;
      chatMessages?: IMessage[];
    },
    private dialogRef?: MatDialogRef<FAQDialogComponent>
  ) {
    super();
  }
    
  ngOnInit(): void {
      this.initForm();
  }

  private initForm(): void {
    const message = this.data?.selectedMessage;
    const initialAnswer = message?.content || '';
    
    const form = this.fb.group({
      question: ['', Validators.required],
      answer: [initialAnswer, Validators.required],
    });

    this.faqForm.set(form);
    this.answerText.set(initialAnswer);

    // Auto-populate question from previous user message
    const messages = this.data?.chatMessages || [];
    if (messages.length > 0 && message) {
      const previousUserMessage = this.findNearestUserObjectBefore(message, messages);
      if (previousUserMessage) {
        const questionContent = this.removeTagsHTML(previousUserMessage.content);
        form.patchValue({ question: questionContent });
        this.questionText.set(questionContent);
      }
    }

    // Subscribe to form changes
    form.get('question')?.valueChanges.subscribe(value => {
      this.questionText.set(value || '');
    });

    form.get('answer')?.valueChanges.subscribe(value => {
      this.answerText.set(value || '');
    });
  }

  // Handle form input changes
  onQuestionChange(value: string): void {
    this.questionText.set(value);
    const form = this.faqForm();
    if (form) {
      form.get('question')?.setValue(value);
    }
  }

  onAnswerChange(value: string): void {
    this.answerText.set(value);
    const form = this.faqForm();
    if (form) {
      form.get('answer')?.setValue(value);
    }
  }

  // Reset form
  onResetForm(): void {
    const message = this.data?.selectedMessage;
    const initialAnswer = message?.content || '';
    
    this.questionText.set('');
    this.answerText.set(initialAnswer);
    
    const form = this.faqForm();
    if (form) {
      form.reset({
        question: '',
        answer: initialAnswer
      });
    }
  }

  // Handle save FAQ with signals
  onSaveFAQ(): void {
    if (!this.canSave()) {
      this.showSnackBar('Please fill all required fields', 'error');
      return;
    }

    this.isLoadingSave.set(true);

    const faqData = {
      question: this.questionText().trim(),
      answer: this.removeTagsHTML(this.answerText().trim())
    };

    // Simulate API call - can be replaced with actual service call
    setTimeout(() => {
      this.showSnackBar('FAQ saved successfully', 'success');
      
      // Emit the saved FAQ data
      this.faqSaved.emit(faqData);
      
      this.isLoadingSave.set(false);
      this.onCloseDialog();
    }, 500);

    // Real API call would be:
    // this.faqService.createFAQ(faqData).subscribe({
    //   next: (response) => {
    //     this.showSnackBar('FAQ saved successfully', 'success');
    //     this.faqSaved.emit(faqData);
    //     this.isLoadingSave.set(false);
    //     this.onCloseDialog();
    //   },
    //   error: (error) => {
    //     this.showSnackBar('Failed to save FAQ', 'error');
    //     this.isLoadingSave.set(false);
    //   }
    // });
  }

  onCloseDialog(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  removeTagsHTML(input: string): string {
    return input.replace(/<[^>]*>/g, '');
  }

  findNearestUserObjectBefore(currentObject: IMessage, chatMessages: IMessage[]): any {
    const currentIndex = chatMessages.indexOf(currentObject);
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (chatMessages[i].role === 'user') {
        return chatMessages[i];
      }
    }
    return null;
  }
} 