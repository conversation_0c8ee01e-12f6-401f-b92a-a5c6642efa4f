import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { OverlayModule } from '@angular/cdk/overlay';
import { ClickOutsideDirective } from '@shared/directives';

@Component({
  selector: 'app-export-dialog',
  templateUrl: './export-dialog.component.html',
  standalone: true,
  imports: [
    CommonModule,
    OverlayModule,
    ClickOutsideDirective,
  ],
})
export class ExportDialogComponent {
  @Input() openExportOptions: boolean = false;

  @Output() openExportOptionsChange = new EventEmitter<boolean>();
  @Output() exportUsingS3 = new EventEmitter<void>();

  onOpenExportOptionsChange(value: boolean): void {
    this.openExportOptionsChange.emit(value);
  }

  onExportUsingS3(): void {
    this.exportUsingS3.emit();
  }

  onClickOutside(): void {
    this.openExportOptionsChange.emit(false);
  }
} 