<div class="bg-light-background dark:bg-dark-background h-screen max-w-screen flex justify-center items-center">
  @if (activationSuccess !== null) {
  <div class="p-12 bg-light-background dark:bg-dark-background box-shadow-gradient flex flex-col justify-center items-center text-light-text dark:text-dark-text">
    <h1 class="text-2xl mb-6">{{ activationSuccess ? 'Account Activated' : 'Account Activation Failed'}}</h1>
    @if (activationSuccess) {
      <div class="flex flex-col items-center">
        <p>You will be redirected to the login page in {{ countdown }} seconds.</p>
      </div>
      <br/>
      <p>If you are not redirected, please click</p>
      <a class="text-light-primary" routerLink="/auth/sign-in">here</a>
      <p>to log in.</p>
    } @else {
      <div class="flex flex-col items-center">
        <p>There was a problem activating your account. Please try contacting us directly at</p>
        <p><a href="mailto:<EMAIL>" class="text-light-primary">{{mail}}</a></p>
        <p>for assistance.</p>
      </div>
    }
  </div>
  }
</div>
