import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { APP_PATH, AUTH_PATH } from '@core/constants';
import { AuthService } from '@core/services';

@Component({
  selector: 'app-active-account',
  imports: [RouterLink],
  templateUrl: './active-account.component.html',
  styleUrl: './active-account.component.css',
})
export class ActiveAccountComponent implements OnInit {
  private authService = inject(AuthService);
  private activatedRoute = inject(ActivatedRoute);
  private router = inject(Router);
  token: string = '';
  countdown: number = 3;
  activationSuccess: boolean | null = null;
  protected readonly mail: string = '<EMAIL>';

  ngOnInit(): void {
    this.token = this.activatedRoute.snapshot.paramMap.get('token') || '';
    if (this.token) {
      this.authService.activateAccount(this.token).subscribe({
        next: () => {
          this.activationSuccess = true;
          this.startCountdown();
        },
        error: (error) => {
          this.activationSuccess = false;
        },
      });
    }
  }
  startCountdown() {
    const interval = setInterval(() => {
      this.countdown -= 1;
      if (this.countdown === 0) {
        clearInterval(interval);
        const url = `${APP_PATH.AUTH}/${AUTH_PATH.LOGIN}`;
        this.router.navigate([url]);
      }
    }, 1000);
  }
}
