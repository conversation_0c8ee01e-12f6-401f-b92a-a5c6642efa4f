<div class="flex flex-col">
  <div
    class="w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div class="text-2xl text-base-content dark:text-dark-base-content font-bold">
      Api key
      <a
        [href]="data.llmName === 'Open AI' ? 'https://docs.dxconnect.lifesup.ai/apis/openai'
              : data.llmName === 'Gemini' ? 'https://docs.dxconnect.lifesup.ai/apis/gemini' : 'https://docs.dxconnect.lifesup.ai/category/api-keys'"
        target="_blank"
        class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
      >
        Learn more
      </a>
    </div>
    <app-svg-icon
      type="icClose"
      class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer justify-end"
      (click)="dialogRef.close()"
    ></app-svg-icon>
  </div>

  <div class="px-6 py-5 max-h-[60vh] overflow-auto">
    <dx-form-field [subscriptHidden]="true" class="w-full" id="apiKey">
      <dx-label class="text-sm">Api Key:</dx-label>
      <input
        dx-input
        [(ngModel)]="data.apiKey"
        (ngModelChange)="data.apiKey = $event.trim()"
        [type]="'text'"
        placeholder="Api key"
      />
    </dx-form-field>
  </div>

  <div
    class="flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
    <button
      dxLoadingButton
      [loading]="isLoading()"
      [spinColor]="'white'"
      (click)="saveNewLLM(data)"
    >
      Save
    </button>
  </div>
</div>
