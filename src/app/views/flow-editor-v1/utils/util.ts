import { NodeFlowData } from '@flow-editor-v1/model';
import { ReactFlowInstance } from 'reactflow';

export const hexToRgb = (hex: string) => {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
};

export const prepareFlowInstanceData = (
  flowInstance: ReactFlowInstance<NodeFlowData, any>
) => {
  return {
    nodes: [...flowInstance.getNodes()].map((node) => ({
      ...node,
      selected: false,
      data: { ...node.data, selected: false },
      style: { ...node.style, opacity: 1 },
    })),
    edges: [...flowInstance.getEdges()].map((edge) => {
      if (edge.data?.isGoToBlock) {
        return {
          ...edge,
          animated: false,
          style: {
            ...edge.style,
            opacity: 0,
          },
        };
      } else {
        return {
          ...edge,
          animated: false,
          style: {
            ...edge.style,
            stroke: 'white',
            strokeWidth: 1,
            strokeOpacity: 1,
            opacity: 1,
          },
        };
      }
    }),
  };
};

export const getCurlyBracesContent = (str: string) => {
  let result = '';
  let openBraces = 0;
  let i = 0;

  for (; i < str.length; i++) {
    if (str[i] === '{') {
      openBraces++;
      result += str[i];
    } else if (str[i] === '}') {
      if (openBraces > 0) {
        openBraces--;
        result += str[i];
        if (openBraces === 0) {
          i++;
          break;
        }
      }
    } else {
      result += str[i];
    }
  }

  result += str.slice(i);

  return result;
};

export const extractVariableNames = (text: string): string[] => {
  if (text)
    return (
      text
        .match(/{{{(.*?)}}}/g)
        ?.map((match) => match.slice(3, -3).split('.')[0]) || []
    );
  return [];
};
