/**
 * This file serves as a bridge between Angular's ThemeService and React components
 * It exposes the ThemeService to the window object so React components can access it
 */

// This function should be called from Angular to expose the ThemeService to React
export function exposeThemeServiceToReact(themeService: any) {
  if (typeof window !== 'undefined') {
    window.themeService = {
      getCurrentThemeValue: () => themeService.getCurrentThemeValue(),
      getTheme: () => ({
        subscribe: (callback: (theme: 'light' | 'dark') => void) => {
          const subscription = themeService.getTheme().subscribe(callback);
          return {
            unsubscribe: () => subscription.unsubscribe(),
          };
        },
      }),
    };
  }
}
