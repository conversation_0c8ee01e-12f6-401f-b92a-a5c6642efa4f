// @ts-nocheck
import { GetNodeFlow, NodeFlowData } from '@flow-editor-v1/model';
import { Connection, ReactFlowInstance } from 'reactflow';

export const getUniqueNodeId = (
  nodeData: NodeFlowData,
  nodes: GetNodeFlow[],
  flowId: number
): string => {
  let maxSameNodes = 0;
  for (let i = 0; i < nodes.length; i += 1) {
    const node = nodes[i];
    if (node.data.name === nodeData.name) {
      const nodeIdCount = Number(node.data.id.split('_')[1].split('|')[0]);
      if (maxSameNodes <= nodeIdCount) maxSameNodes = nodeIdCount;
    }
  }

  return `${nodeData.name}_${maxSameNodes + 1}|${flowId}`;
};

export const initializeDefaultNodeData = (nodeParams: Array<any>) => {
  const initialValues = {};

  for (let i = 0; i < nodeParams.length; i += 1) {
    const input = nodeParams[i];
    initialValues[input.name] = input.default || '';
  }

  return initialValues;
};

export const isValidConnection = (
  connection: Connection,
  flowInstance: ReactFlowInstance<NodeFlowData, any>
) => {
  return true;
};

export const translateCanvasCoordinates = (
  x: number,
  y: number,
  offsetX: number = 0,
  offsetY: number = 0,
  scale: number = 1
) => {
  const translatedX = (x - offsetX) * scale;
  const translatedY = (y - offsetY) * scale;

  return { x: translatedX, y: translatedY };
};

export class LocalStorageKey {
  static flowDevData(id: number) {
    if (id == null || isNaN(id)) {
      throw new Error('Invalid ID provided. ID must be a valid number.');
    }
    return `flowDevData:${id}`;
  }

  static flowLiveData(id: number) {
    if (id == null || isNaN(id)) {
      throw new Error('Invalid ID provided. ID must be a valid number.');
    }
    return `flowLiveData:${id}`;
  }

  static flowDevDataUpdatedAt(id: number) {
    if (id == null || isNaN(id)) {
      throw new Error('Invalid ID provided. ID must be a valid number.');
    }
    return `flowDevDataCreatedAt:${id}`;
  }

  static nodePositionPasted(id: string) {
    if (id == null) {
      throw new Error('Invalid ID provided. ID must be a valid string.');
    }
    return `nodePositionPasted:${id}`;
  }

  static variableLocation() {
    return `variableLocation`;
  }

  static mousePositionFlow(id: number) {
    if (id == null || isNaN(id)) {
      throw new Error('Invalid ID provided. ID must be a valid number.');
    }
    return `mousePositionFlow:${id}`;
  }
}
