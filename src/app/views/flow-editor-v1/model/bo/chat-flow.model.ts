export interface IChatFlowBase {
  id?: number;
  category_id: number;
  intent_id?: number;
  event_id?: string;
  trigger_type?: string;
  ai_id: string;
  name: string;
  description?: string;
  flow_data?: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

export interface IChatFlow extends IChatFlowBase {}

export type ChatFlow = IChatFlow

export interface IChatFlowFilter {
  key_word?: string;
  trigger_type: string;
}

export type ChatFlowFilter = IChatFlowFilter
