export interface IVariableDefDevBase {
  id?: number;
  ai_id: string;
  flow_dev_id?: number;
  var_name: string;
  type: string;
  init_value?: string;
}

export interface IVariableDefDev extends IVariableDefDevBase {}

export type VariableDefDev = IVariableDefDev;

export interface IVariableDefDevFilter {
  key_word: string;
  flow_dev_id?: number;
}

export type VariableDefDevFilter = IVariableDefDevFilter;
