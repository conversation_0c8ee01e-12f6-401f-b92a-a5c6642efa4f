import {VARIABLE_POSITION_TYPE} from "../../constant/variable";

export interface IVariableLocatedBase {
  position_type: VARIABLE_POSITION;
  flow_dev_id?: number;
  node_id?: string;
  api_id?: number;
  function_id?: number;
  var_def_dev_id?: number;
  var_def_dev_name?: string;
}

export interface IVariableLocated extends IVariableLocatedBase {
}

export type VariableLocated = IVariableLocated;

export type VARIABLE_POSITION = VARIABLE_POSITION_TYPE.FLOW | VARIABLE_POSITION_TYPE.API | VARIABLE_POSITION_TYPE.FUNCTION

export interface IVariableLocatedFilter {
  ai_id?: string;
}

export type VariableLocatedFilter = IVariableLocatedFilter
