export interface IChatFlowDevBase {
  id?: number;
  flow_id?: number;
  category_id: number;
  intent_dev_id?: number;
  event_id?: string;
  trigger_type?: string;
  ai_id: string;
  name: string;
  description?: string;
  flow_data?: string;
  created_at?: string;
  updated_at?: string;
}

export interface IChatFlowDev extends IChatFlowDevBase {}

export type ChatFlowDev = IChatFlowDev

export interface IChatFlowDevFilter {
  key_word?: string;
  trigger_type: string;
}

export type ChatFlowDevFilter = IChatFlowDevFilter
