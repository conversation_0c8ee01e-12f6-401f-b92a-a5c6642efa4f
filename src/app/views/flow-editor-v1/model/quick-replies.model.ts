import {INodeFlowData} from "./node.model";

export interface IQuickRepliesAction {
  type: string;
  label: string;
  bgColor: string;
  textColor: string;
  value: string;
  link?: string;
  text_aliases?: string[];
  action_id: string;
}

export interface IQuickRepliesNodeData extends INodeFlowData {
  messages: Array<{
    type: string;
    value: string;
    language: string;
  }>;
  actions: Array<IQuickRepliesAction>;
  var_store_id?: number;
}

export type QuickRepliesNodeData = IQuickRepliesNodeData;
