import { Node } from 'reactflow';

export type StartFlowNodeType = 'start';
export type ToolNodeType = 'tool';
export type ToolV2NodeType = 'toolV2';
export type ApiNodeType = 'api';
export type EventApiNodeType = 'eapi';
export type IfElseNodeType = 'ifElse';
export type TextNodeType = 'text';
export type QuestionNodeType = 'question';
export type SwitchFlowNodeType = 'switchFlow';
export type FunctionNodeType = 'function';
export type VariableNodeType = 'variable';
export type CarouselNodeType = 'carousel';
export type EventTriggerNodeType = 'eventTrigger';
export type EQuickRepliesNodeType = 'eQuickReplies';
export type SendMessageNodeType = 'sendMessage';
export type EventConditionNodeType = 'eIfElse';
export type BasicLLMChain = 'basicLLMChain';
export type LLMMessage = 'LLMMessage';
export type QuickRepliesNodeType = 'quickReplies';
export type GenericMessage = 'genericMessage';

export type NodeFlowType = StartFlowNodeType
  | ToolNodeType
  | ToolV2NodeType
  | ApiNodeType
  | EventApiNodeType
  | IfElseNodeType
  | TextNodeType
  | QuestionNodeType
  | SwitchFlowNodeType
  | FunctionNodeType
  | VariableNodeType
  | CarouselNodeType
  | EventTriggerNodeType
  | EQuickRepliesNodeType
  | SendMessageNodeType
  | EventConditionNodeType
  | BasicLLMChain
  | LLMMessage
  | QuickRepliesNodeType
  | GenericMessage

export interface INodeFlowData {
  id?: string;
  node_type: string;
  node_color?: string;
  label: string;
  name: string;
  description: string;
  type: string;
  icon: string;
  selected?: boolean;
  debug?: boolean;
  parentId?: string;
  extent?: string;
  draggable?: boolean;
  goToBlockSource?: Array<{ sourceHandle: string, blockName: string, blockId: string }>;
  oldNodeId?: string;
}

export type NodeFlowData = any

export type NodeFlow = Node<NodeFlowData, NodeFlowType>
export type GetNodeFlow = Node<NodeFlowData>
