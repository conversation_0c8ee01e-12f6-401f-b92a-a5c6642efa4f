import { STUDIO_STATUS_TYPE } from '@flow-editor-v1/constant';
import { ReactFlowInstance } from 'reactflow';
import {
  Api,
  ChatFlow,
  ChatFlowDev,
  Event,
  Function,
  VariableDef,
  VariableDefDev,
} from './bo';
import {
  VARIABLE_POSITION,
  VariableLocated,
} from './bo/variable-located.model';
import { NodeFlowData } from './node.model';

export interface IBuildFlowState {
  flow: ChatFlow | ChatFlowDev;
  isDirty: number;
  dialogShowing: boolean;
  triggerSave: number;
  setFlow: (flow: ChatFlow | ChatFlowDev) => void;
  setDirty: () => void;
  setNotDirty: () => void;
  setShowingDialog: () => void;
  setDialogNotShowing: () => void;
  setTriggerSave: () => void;
}

export type BuildFlowState = IBuildFlowState;

export interface IFlowInstanceState {
  flowInstance: ReactFlowInstance<NodeFlowData, any>;
  setFlowInstance: (instance: ReactFlowInstance<NodeFlowData, any>) => void;
}

export type FlowInstanceState = IFlowInstanceState;

export interface IFlowInstanceContext {
  duplicateNode: (nodeId: string) => void;
  deleteNode: (nodeId: string) => void;
  deleteNodes: (nodeIds: string[]) => void;
  deleteEdge: (edgeId: string) => void;
  convertNode: (nodeId: string) => void;
  unSelectNode: (nodeId: string) => void;
  resetStyle: () => void;
}

export type FlowInstanceContext = IFlowInstanceContext;

export interface IVariableDefState {
  variables: VariableDef[];
  variablesDev: VariableDefDev[];
  variableSelected: VariableDef | VariableDefDev;
  setVariableDef: (variables: VariableDef[]) => void;
  setVariableDefDev: (variablesDev: VariableDefDev[]) => void;
  setVariableSelected: (variableSelected: VariableDef | VariableDefDev) => void;
}

export type VariableDefState = IVariableDefState;

export interface IApiState {
  apis: Api[];
  setApis: (apis: Api[]) => void;
}

export type ApiState = IApiState;

export interface IEventState {
  events: Event[];
  setEvents: (events: Event[]) => void;
}

export type EventState = IEventState;

export interface IFunctionState {
  functions: Function[];
  setFunctions: (functions: Function[]) => void;
}

export type FunctionState = IFunctionState;

export interface IStudioState {
  status: STUDIO_STATUS_TYPE;
  setStudioStatus: (status: STUDIO_STATUS_TYPE) => void;
}

export type StudioState = IStudioState;

export interface IMenuState {
  menu: any;
  setMenu: (menu: any) => void;
}

export type MenuState = IMenuState;

export interface ILayoutState {
  isLoading: boolean;
  theme: string;
  setLoading: (val: boolean) => void;
  setTheme: (val: string) => void;
}

export type LayoutState = ILayoutState;

export interface IFlowDebugState {
  debug: any;
  setDebug: (val: boolean) => void;
}

export type FlowDebugState = IFlowDebugState;

export interface IVariableLocatedState {
  variableLocations: Array<VariableLocated>;
  setInitVariableLocations: (locations: Array<VariableLocated>) => void;
  setVariableLocations: (
    locations: Array<VariableLocated>,
    type: VARIABLE_POSITION
  ) => void;
  setVariableLocation: (location: VariableLocated) => void;
}

export type VariableLocatedState = IVariableLocatedState;

export interface IMousePositionFlow {
  x: number;
  y: number;
}

export interface IMousePositionFlowState {
  mousePosition: IMousePositionFlow | null;
  setMousePosition: (val: IMousePositionFlow | null) => void;
}

export type MousePositionFlowState = IMousePositionFlowState;

export interface INodeHeaderState {
  isEditing: string | null;
  setIsEditing: (val: string | null) => void;
}

export type NodeHeaderState = INodeHeaderState;
