import { INodeFlowData } from './node.model';

export interface IEQuickRepliesAction {
  type: string;
  text: {
    type: string;
    text: string;
  };
  style: 'default' | 'primary' | 'danger';
  value: string;
  action_id: string;
}

export interface IEQuickRepliesNodeData extends INodeFlowData {
  send_to: string[];
  title: {
    type: string;
    value: string;
    language: string;
  };
  messages: Array<{
    type: string;
    value: string;
    language: string;
  }>;
  comment_labels: Array<{
    type: string;
    value: string;
    language: string;
  }>;
  actions: Array<IEQuickRepliesAction>;
  comments?: boolean;
  var_store_id?: number;
}

export type EQuickRepliesNodeData = IEQuickRepliesNodeData;
