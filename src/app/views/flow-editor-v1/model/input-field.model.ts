import { InputHTMLAttributes, ReactElement } from 'react';
import { Control, UseFormSetValue } from 'react-hook-form';

export interface IInputFieldProps
  extends InputHTMLAttributes<HTMLInputElement> {
  type: 'input' | 'textarea';
  rows?: number;
  name: string;
  control: Control<any>;
  setValue: UseFormSetValue<Record<string, any>>;
  validate?: (value: string) => string | null;
  onClickInput?: (e: any) => void;
  onChangeInput?: (e: any, name: string) => void;
  onFocusInput?: (e: any, name: string) => void;
  onBlurInput?: (e: any, name: string) => void;
  suffix?: ReactElement;
  onMouseDownSuffix?: (e: any) => void;
  onClickSuffix?: (e: any) => void;
}
