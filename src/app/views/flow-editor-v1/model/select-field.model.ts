import type { Variant } from 'antd/es/config-provider';
import { InputHTMLAttributes } from 'react';
import { Control } from 'react-hook-form';

interface SelectOption {
  key: any;
  value: any;
  label: string;
}

interface Options extends SelectOption {
  [key: string]: any;
}

export interface ISelectFieldProps
  extends InputHTMLAttributes<HTMLInputElement> {
  options: Array<Options>;
  name: string;
  control: Control<any>;
  variant?: Variant;
  mode?: 'default' | 'multiple' | 'tags';
  onChangeSelect?: (value: any, option: any, name: string) => void;
  onClearSelect?: (name: string) => void;
}
