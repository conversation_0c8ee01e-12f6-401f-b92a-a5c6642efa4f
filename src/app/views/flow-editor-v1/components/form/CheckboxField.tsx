import { Checkbox } from "antd";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import React, { memo } from "react";
import { Control, useController } from "react-hook-form";

interface CheckboxFieldProps {
  name: string;
  control: Control<any>;
  disabled: boolean;
  onChange?: (e: CheckboxChangeEvent) => void; // Thay đổi kiểu của onChange
}

const CheckboxField: React.FC<CheckboxFieldProps> = ({ name, control, disabled, onChange: externalOnChange }) => {
  const {
    field: { value, onChange },
    fieldState: { error }
  } = useController({
    name,
    control
  });

  const handleChange = (e: CheckboxChangeEvent) => {
    onChange(e.target.checked); // React Hook Form xử lý
    if (externalOnChange) {
      externalOnChange(e); // Nếu onChange từ prop được truyền, g<PERSON><PERSON> n<PERSON>
    }
  };

  return (
    <div className="flex flex-col space-y-2">
      <Checkbox
        onChange={handleChange} // Sử dụng handleChange đã thay đổi kiểu
        checked={value}
        disabled={disabled}
      />
      {error && <div style={{ color: "red", fontSize: 12 }}>{error.message}</div>}
    </div>
  );
};

export default memo(CheckboxField);
