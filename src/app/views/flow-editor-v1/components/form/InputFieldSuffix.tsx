// @ts-nocheck
import { IInputFieldProps, VariableDefState } from "@flow-editor-v1/model";
import { useVariableDefState } from "@flow-editor-v1/store";
import { AutoComplete, Input } from "antd";
import TextArea from "antd/es/input/TextArea";
import React, { forwardRef, useEffect, useRef, useState } from "react";
import { useController } from "react-hook-form";
import styled from "styled-components";

const StyledSuffix = styled.div`
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  padding: 12px 12px 6px 6px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
`

const InputFieldSuffix = forwardRef<HTMLDivElement, IInputFieldProps>((props: IInputFieldProps, ref) => {
  const {
    type,
    name,
    control,
    setValue,
    rows,
    disabled,
    onClickInput,
    onChangeInput,
    onFocusInput,
    onBlurInput,
    validate,
    suffix,
    onMouseDownSuffix,
    onClickSuffix,
    ...inputProps
  } = props;

  const {
    field: {value, onChange},
    fieldState: {error},
  } = useController({
    name,
    control
  });

  const {variablesDev} = useVariableDefState<VariableDefState>(state => state);
  const [options, setOptions] = useState<{ value: any }[]>([]);
  const [customError, setCustomError] = useState<string | null>(null);
  const [text, setText] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  const inputRef = useRef(null);
  const textareaRef = useRef(null);

  useEffect(() => {
    if (type === 'input') {
      const input = inputRef.current.input;
      if (input && isSearching) {
        const {selectionStart} = input
        const textBeforeCursor = value.slice(0, selectionStart).slice(-1);
        if (textBeforeCursor !== "{") {
          setText((prevText) => prevText + textBeforeCursor);
        }
      }
    }

    if (type === 'textarea') {
      const textarea = textareaRef.current.resizableTextArea.textArea;
      if (textarea && isSearching) {
        const {selectionStart} = textarea
        const textBeforeCursor = value.slice(0, selectionStart).slice(-1);
        if (textBeforeCursor !== "{") {
          setText((prevText) => prevText + textBeforeCursor);
        }
      }
    }
  }, [isSearching, value]);

  useEffect(() => {
    if (isSearching) {
      const variableNameSearch = text || ""
      setOptions(
        !value ? [] : variablesDev.filter(v1 => v1.var_name.includes(variableNameSearch)).map(v2 => ({value: v2.var_name})),
      );
    }
  }, [isSearching, text]);

  const handleSearch = (value: string) => {
    if (type === 'input') {
      const input = inputRef.current.input;
      if (input) {
        const {selectionStart} = input;
        const textBeforeCursor = value.slice(0, selectionStart);
        const lastThreeChars = textBeforeCursor.slice(-3);
        const lastTextBeforeCursor = value.slice(0, selectionStart).slice(-1);
        if (lastThreeChars === "{{{") {
          setIsSearching(true)
        } else if ((lastThreeChars.includes("{{") || lastThreeChars.includes("{")) && lastTextBeforeCursor === "{") {
          setIsSearching(false)
          setOptions([]);
        }
      }
    }

    if (type === 'textarea') {
      const textarea = textareaRef.current.resizableTextArea.textArea;
      if (textarea) {
        const {selectionStart} = textarea
        const textBeforeCursor = value.slice(0, selectionStart);
        const lastThreeChars = textBeforeCursor.slice(-3);
        const lastTextBeforeCursor = value.slice(0, selectionStart).slice(-1);
        if (lastThreeChars === "{{{") {
          setIsSearching(true)
        } else if ((lastThreeChars.includes("{{") || lastThreeChars.includes("{")) && lastTextBeforeCursor === "{") {
          setIsSearching(false)
          setOptions([]);
        }
      }
    }
  };

  const handleSelect = (valSelected) => {
    if (type === 'input') {
      const input = inputRef.current.input;
      if (input) {
        const {selectionStart} = input
        const valueSplit = value.split("");
        const valSelectedSplit = `${valSelected}}}}`.split("");
        valueSplit.splice(selectionStart - text.length, text.length, ...valSelectedSplit);
        const newValue = [...valueSplit].join('')
        setValue(name, newValue)
        handleChange(newValue)
        setTimeout(() => {
          inputRef.current.setSelectionRange(selectionStart + valSelectedSplit.length, selectionStart + valSelectedSplit.length)
        }, 0)
      }
    }

    if (type === 'textarea') {
      const textarea = textareaRef.current.resizableTextArea.textArea;
      if (textarea) {
        const {selectionStart} = textarea
        const valueSplit = value.split("");
        const valSelectedSplit = `${valSelected}}}}`.split("");
        valueSplit.splice(selectionStart - text.length, text.length, ...valSelectedSplit);
        const newValue = [...valueSplit].join('')
        setValue(name, newValue)
        handleChange(newValue)
        setTimeout(() => {
          textarea.setSelectionRange(selectionStart + valSelectedSplit.length, selectionStart + valSelectedSplit.length)
        }, 0)
      }
    }

    setOptions([]);
    setText("");
  }

  const handleChange = (e) => {
    const inputValue = e;
    onChange(inputValue);
    if (onChangeInput) onChangeInput(inputValue, name)
    if (validate) {
      const validationError = validate(inputValue);
      setCustomError(validationError);
    }
  };

  const handleKeyUp = (e) => {
    if (e && e.key === "Backspace") {
      setText("")
    }

    if (e && e.key === "Delete") {
      setText("")
    }
  };

  const handleOnClickInput = (e) => {
    if (onClickInput) onClickInput(e)
  }

  const handleFocus = (e) => {
    e.stopPropagation();
    if (onFocusInput) onFocusInput(e, name)
  }

  const handleBlur = (e) => {
    e.stopPropagation()
    if (onBlurInput) onBlurInput(e, name)
  }

  const handleMouseDownSuffix = (e) => {
    if (onMouseDownSuffix) onMouseDownSuffix(e)
  }

  const handleClickSuffix = (e) => {
    e.stopPropagation()
    onClickSuffix(e)
  }

  return (
    <>
      {type === 'input' && suffix && (
        <div id={name} className="w-full flex-grow flex flex-col space-y-2 px-1">
          <AutoComplete
            options={options}
            onSelect={handleSelect}
            onSearch={handleSearch}
            onChange={handleChange}
            value={value}
            disabled={disabled}
            onClick={handleOnClickInput}
            onFocus={handleFocus}
            onBlur={handleBlur}
          >
            <Input suffix={suffix}
                   ref={inputRef}
                   onKeyUp={handleKeyUp}
                   className="w-full h-10 !rounded-[12px] !bg-base-400 dark:!bg-dark-base-400 !border-primary-border dark:!border-dark-primary-border focus:!border-primary dark:focus:!border-dark-primary focus:!outline-4 focus:!outline-[#d2ceee] dark:focus:!outline-[#343942] placeholder-neutral-content dark:!placeholder-dark-neutral-content !text-base-content dark:!text-dark-base-content"/>
          </AutoComplete>
          {(error || customError) && (
            <div style={{color: 'red', fontSize: 12}}>{error?.message || customError}</div>
          )}
        </div>
      )}

      {type === 'textarea' && suffix && (
        <div id={name} className="w-full flex-grow flex flex-col relative px-1">
          <AutoComplete
            options={options}
            onSelect={handleSelect}
            onSearch={handleSearch}
            onChange={handleChange}
            value={value}
            disabled={disabled}
            onClick={handleOnClickInput}
            onFocus={handleFocus}
            onBlur={handleBlur}
          >
            <TextArea rows={rows || 5}
                      ref={textareaRef}
                      onKeyUp={handleKeyUp}
                      className="w-full h-10 !rounded-[12px] !bg-base-400 dark:!bg-dark-base-400 !border-primary-border dark:!border-dark-primary-border focus:!border-primary dark:focus:!border-dark-primary focus:!outline-4 focus:!outline-[#d2ceee] dark:focus:!outline-[#343942] placeholder-neutral-content dark:!placeholder-dark-neutral-content !text-base-content dark:!text-dark-base-content"/>
          </AutoComplete>
          <StyledSuffix ref={ref} onClick={handleClickSuffix} onMouseDown={handleMouseDownSuffix}>{suffix}</StyledSuffix>
          {(error || customError) && (
            <div style={{color: 'red', fontSize: 12, marginTop: 8}}>{error?.message || customError}</div>
          )}
        </div>
      )}
    </>
  );
});

export default InputFieldSuffix
