// @ts-nocheck
import { Segmented } from "antd";
import React, { memo } from "react";
import { use<PERSON><PERSON>roll<PERSON> } from "react-hook-form";

const SegmentedField = ({name, control, disabled, options}) => {
  const {
    field: {value, onChange},
    fieldState: {error}
  } = useController({
    name,
    control
  });

  return (
    <div className="flex flex-col space-y-2">
      <Segmented
        value={value || options[0]?.value} // Fallback to the default if value is undefined
        disabled={disabled}
        onChange={onChange}
        options={options}
      />
      {error && <div style={{color: 'red', fontSize: 12}}>{error?.message}</div>}
    </div>
  )
}

export default memo(SegmentedField)
