// @ts-nocheck
import { <PERSON><PERSON><PERSON> } from "antd";
import React, { memo } from "react";
import { use<PERSON><PERSON>roller } from "react-hook-form";

const CheckboxField = ({ name, control, disabled, min, max, step, defaultValue }) => {
  const {
    field: { value, onChange },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <div className="flex flex-col space-y-2">
      <Slider
        onChange={onChange}
        disabled={disabled}
        defaultValue={defaultValue}
        min={min}
        max={max}
        step={step}
        value={value}
      />
      {error && (
        <div style={{ color: "red", fontSize: 12 }}>{error?.message}</div>
      )}
    </div>
  );
};

export default memo(CheckboxField);
