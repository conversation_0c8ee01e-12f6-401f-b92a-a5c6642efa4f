// @ts-nocheck
import { variableDefDevApi } from "@flow-editor-v1/api";
import GoToBlockHandle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import InputField from "@flow-editor-v1/components/form/InputField";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import Icon from "@flow-editor-v1/components/icon/icon";
import NodeHeader from "@flow-editor-v1/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { STUDIO_STATUS } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { FlowDebugState, VariableDefDev, VariableDefState, VariableNodeData } from "@flow-editor-v1/model";
import {
    useBuildFlowState,
    useFlowDebugState,
    useFlowInstanceState,
    useLayoutState,
    useStudioState,
    useVariableDefState,
    useVariableLocatedState
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { extractVariableNames, hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from '@hookform/resolvers/yup';
import { Modal } from "antd";
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from 'yup';
import { VARIABLE_POSITION_TYPE } from "../../../constant/variable";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$data ? 'white' : `rgba(255, 255, 255, 0.5)`};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const variableFormControlSchema = yup.object().shape({
  var_id: yup.number().required("Variable required"),
  value: yup.mixed()
})

const variableNodeDataFormSchema = yup.object().shape({
  variables: yup.array().of(variableFormControlSchema),
});

const initFormValue = {
  variables: [{var_id: 0, value: ''}],
}

const VariableNode = ({data}: { data: VariableNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {variablesDev, setVariableDefDev} = useVariableDefState<VariableDefState>(state => state);
  const {flow, setDirty} = useBuildFlowState(state => state);
  const {setVariableLocations} = useVariableLocatedState(state => state)
  const {status} = useStudioState(state => state);
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState(state => state);

  const {
    control,
    setValue,
    handleSubmit,
    reset
  } = useForm({
    resolver: yupResolver(variableNodeDataFormSchema),
    defaultValues: variableNodeDataFormSchema.cast(initFormValue)
  })

  const {
    fields: varFields,
    append: varAppend,
    remove: varRemove,
    replace: varReplace
  } = useFieldArray({
    control,
    name: 'variables'
  });

  const handleAddVar = () => {
    varAppend({var_id: 0, value: ''})
  }

  const handleDeleteVar = (index: number) => {
    varRemove(index)
  }

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    data.variables = _.cloneDeep(formValue?.variables);

    const variableLocationsDefine = data.variables?.map(variable => ({
      position_type: VARIABLE_POSITION_TYPE.FLOW,
      flow_dev_id: flow.id,
      node_id: data.id,
      var_def_dev_id: variable.var_id,
    }))
    const variableLocationsValue = data.variables?.flatMap(variable => {
      const variableNames = extractVariableNames(variable.value);
      return variableNames?.length
        ? variableNames.map(name => ({
          position_type: VARIABLE_POSITION_TYPE.FLOW,
          flow_dev_id: flow.id,
          node_id: data.id,
          var_def_dev_name: name,
        }))
        : [];
    });
    const variableLocations = [...variableLocationsDefine, ...variableLocationsValue]
    setVariableLocations(variableLocations, VARIABLE_POSITION_TYPE.FLOW)

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  useEffect(() => {
    const fetchVariableDefDev = async () => {
      const res: VariableDefDev[] = await variableDefDevApi.getVariableDev({
        key_word: "",
      });
      setVariableDefDev(res);
    };

    void fetchVariableDefDev()
  }, [status])

  useEffect(() => {
    resetForm();
    data.variables && data.variables.length ? varReplace(data.variables) : varReplace({var_id: 0, value: ''})
  }, [JSON.stringify(data), JSON.stringify(flow)]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        <div className="absolute left-0 text-[10px] text-neutral-content dark:!text-dark-neutral-content" style={{top: -20}}>{data.id}</div>
      }
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        <StyledHandleSourceAnchor $bgColor={data.node_color} $data={data.variables && data.variables.length}
                                  onDoubleClick={handleOpenModal}>
          {
            (data.variables && data.variables.length && data.variables[0].var_id) ? variablesDev.find(v => v.id === data.variables[0].var_id)?.var_name ? variablesDev.find(v => v.id === data.variables[0].var_id).var_name : 'Configure' : 'Configure'
          }
        </StyledHandleSourceAnchor>
        {
          !data.goToBlockSource ? (
            <Handle
              type="source"
              position={Position.Right}
              id={`${data.id}_source#1`}
              style={{
                height: 10,
                width: 10,
                backgroundColor: theme === 'dark' ? 'white' : 'black',
              }}
            />
          ) : (() => {
              const key = `${data.id}_source#1`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: theme === 'dark' ? 'black' : 'white',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              }

              return <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}_source#1`}
                style={{
                  height: 10,
                  width: 10,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            }
          )()
        }
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col">
            {
              varFields.map((field, index) => (
                <div key={field.id} className="flex items-center justify-between gap-4 mt-3">
                  <div className="flex-grow grid grid-cols-3 gap-4">
                    <div className="col-span-1">
                      <span className="block mb-1 !text-neutral-content dark:!text-dark-neural-content">
                        Variable <span style={{color: 'red'}}>*</span>
                      </span>
                      <SelectField name={`variables[${index}].var_id`} control={control}
                                   disabled={status && status === STUDIO_STATUS.LIVE} options={variablesDev.map(v => ({
                        ...v,
                        key: v.id,
                        value: v.id,
                        label: v.var_name
                      }))}/>
                    </div>
                    <div className="col-span-2">
                      <span className="block mb-1 !text-neutral-content dark:!text-dark-neural-content">
                        Value
                      </span>
                      <InputField type={'textarea'} rows={2} name={`variables[${index}].value`} control={control}
                                  disabled={status && status === STUDIO_STATUS.LIVE}
                                  setValue={setValue}/>
                    </div>
                  </div>
                  {
                    status && status === STUDIO_STATUS.DEV && (
                      <div className="hover:cursor-pointer" onClick={() => handleDeleteVar(index)}>
                        <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                      </div>
                    )
                  }
                </div>
              ))
            }
          </div>

          {
            status && status === STUDIO_STATUS.DEV && (
              <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddVar}>
                <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                <div style={{color: data.node_color}}>Add another variable</div>
              </div>
            )
          }

          <div className="w-full flex justify-end items-center space-x-4" style={{marginTop: 24}}>
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default VariableNode;
