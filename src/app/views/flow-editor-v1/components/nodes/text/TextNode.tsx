// @ts-nocheck
import GoTo<PERSON><PERSON>Handle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import CheckboxField from "@flow-editor-v1/components/form/CheckboxField";
import InputFieldSuffix from "@flow-editor-v1/components/form/InputFieldSuffix";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import Icon from "@flow-editor-v1/components/icon/icon";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { STUDIO_STATUS } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { lang } from "@flow-editor-v1/init";
import { FlowDebugState, LayoutState, TextNodeData } from "@flow-editor-v1/model";
import {
    useBuildFlowState,
    useFlowDebugState,
    useFlowInstanceState,
    useLayoutState,
    useStudioState,
    useVariableLocatedState
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { extractVariableNames, hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Modal, Popover, Spin, Typography } from "antd";
import _, { debounce } from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { translationApi } from "../../../api/translationApi";
import { VARIABLE_POSITION_TYPE } from "../../../constant/variable";
import NodeHeader from "../NodeHeader";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$data ? 'white' : `rgba(255, 255, 255, 0.5)`};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const textMessageFormSchema = yup.object().shape({
  type: yup.string().required(),
  value: yup.string().required("Bot say is required"),
  language: yup.string().required()
});

const textNodeDataFormSchema = yup.object().shape({
  messages: yup.array().of(textMessageFormSchema).default([{type: 'text', value: '', language: ''}]),
  streaming: yup.boolean().default(true)
});

const TextNode = ({data}: { data: TextNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalLanguageOpen, setModalLanguageOpen] = useState<string | null>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {flow, setDirty} = useBuildFlowState(state => state);
  const {status} = useStudioState(state => state);
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const {setVariableLocations} = useVariableLocatedState(state => state)
  const [disableSaveBtn, setDisableSaveBtn] = useState<boolean>(true);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState<LayoutState>(state => state);

  const inputWithPopoverRef = useRef({});

  const {
    control,
    setValue,
    handleSubmit,
    reset,
    watch,
    getValues
  } = useForm({
    resolver: yupResolver(textNodeDataFormSchema)
  })

  const {
    fields: messageFields,
    append: messageAppend,
    remove: messageRemove,
    replace: messageReplace,
    update: messageUpdate
  } = useFieldArray({
    control,
    name: 'messages'
  });

  const handleAddMessage = () => {
    messageAppend({
      type: 'text',
      value: '',
      language: ''
    })
  }

  const handleDeleteMessage = (index: number, fieldId: string) => {
    messageRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    if (confirmLoading) return
    data.messages = _.cloneDeep(formValue?.messages);
    data.streaming = formValue?.streaming ?? true;

    const variableLocations = data.messages?.flatMap(message => {
      const variableNames = extractVariableNames(message.value);
      return variableNames?.length
        ? variableNames.map(name => ({
          position_type: VARIABLE_POSITION_TYPE.FLOW,
          flow_dev_id: flow.id,
          node_id: data.id,
          var_def_dev_name: name,
        }))
        : [];
    });
    setVariableLocations(variableLocations, VARIABLE_POSITION_TYPE.FLOW);

    resetForm();
    setModalOpen(false);
    setModalLanguageOpen(null);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  const onSubmitLanguage = (e) => {
    e.preventDefault()
    data.messages = _.cloneDeep(getValues().messages) as any;
    setModalLanguageOpen(null);
  }

  useEffect(() => {
    resetForm();
    messageReplace(_.cloneDeep(data.messages && data.messages.length ? data.messages : [{
      type: 'text',
      value: '',
      language: ''
    }]));
    if (data.streaming !== undefined && data.streaming !== null) {
      setValue('streaming', data.streaming);
    } else {
      setValue('streaming', true);
    }
  }, [JSON.stringify(data), modalOpen]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  const handleClickInput = (e) => {
    setModalLanguageOpen(null)
  }

  const handleOnFocusInput = (e, name, index) => {
    setModalLanguageOpen(null)
  }

  const handleDetectLanguage = async (sentence, index) => {
    if (sentence && index > -1) {
      setConfirmLoading(true);
      const {language} = await translationApi.detectLanguage({
        sentence,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        const currentMessage = _.cloneDeep(getValues().messages[index]);
        setValue(`messages.${index}`, { ...currentMessage, language });
        setConfirmLoading(false);
        setDisableSaveBtn(false);
        data.messages = _.cloneDeep(getValues().messages) as any;
      }
    }
  }

  const debounceOnChangeInput = useCallback(
    debounce(handleDetectLanguage, 2000),
    []
  );

  const handleChangeInput = () => {
    if (data.messages) {
      setDisableSaveBtn(true);
    }
  };

  const handleOnOpenModalLanguageChange = () => {
    setModalLanguageOpen(null)
  }

  const handleClickSuffix = async (e, fieldId, index) => {
    e.preventDefault();
    setModalLanguageOpen(fieldId)
    if (!getValues().messages[index].language) {
      const {language} = await translationApi.detectLanguage({
        sentence: watch().messages[index].value ?? '',
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        messageUpdate(index, {...messageFields[index], language});
        setConfirmLoading(false);
        data.messages = _.cloneDeep(getValues().messages) as any;
      }
    }
  }

  const handleMouseDownSuffix = (e) => {
    e.preventDefault();
  };

  const handleClosePopover = (e) => {
    e.stopPropagation()
    setModalLanguageOpen(null)
  }

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        <div className="absolute left-0 text-[10px] text-neutral-content dark:!text-dark-neutral-content" style={{top: -20}}>{data.id}</div>
      }
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        <StyledHandleSourceAnchor $bgColor={data.node_color}
                                  $data={data.messages && data.messages.length && data.messages[0].value}
                                  onDoubleClick={handleOpenModal}>
          {
            (data.messages && data.messages.length && data.messages[0].value) ? data.messages[0].value : 'Configure'
          }
        </StyledHandleSourceAnchor>
        {
          !data.goToBlockSource ? (
            <Handle
              type="source"
              position={Position.Right}
              id={`${data.id}_source#1`}
              style={{
                height: 10,
                width: 10,
                backgroundColor: theme === 'dark' ? 'white' : 'black',
              }}
            />
          ) : (() => {
              const key = `${data.id}_source#1`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: theme === 'dark' ? 'black' : 'white',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              }

              return <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}_source#1`}
                style={{
                  height: 10,
                  width: 10,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            }
          )()
        }
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => {
          if (!confirmLoading) setModalOpen(false)
        }}
        onCancel={() => {
          if (!confirmLoading) {
            setModalOpen(false)
            setModalLanguageOpen(null)
          }
        }}
        footer={null}
        closable={false}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <Spin spinning={confirmLoading}>
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Bot says <span style={{color: 'red'}}>*</span>
              </Typography.Text>
              {
                messageFields.map((field, index) => (
                  <div key={field.id} className="flex items-center justify-between gap-4 mt-2">
                    <Popover
                      color={theme === 'light' ? '#EBEBEF' : '#1E232E'}
                      content={
                        <div className="flex flex-col space-y-4 mt-6">
                          <SelectField
                            name={`messages[${index}].language`}
                            control={control}
                            options={lang}
                          />

                          <div
                            className="w-full flex justify-end items-center space-x-4"
                            style={{marginTop: 24}}
                          >
                            <StyledButtonModal
                              type="button"
                              key="cancel"
                              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                              onClick={handleClosePopover}
                            >
                              Cancel
                            </StyledButtonModal>
                            <StyledButtonModal
                              $bgColor={"#7F75CF"}
                              $color={"white"}
                              type={undefined}
                              onClick={onSubmitLanguage}
                            >
                              Save
                            </StyledButtonModal>
                          </div>
                        </div>
                      }
                      title="Localization"
                      placement="left"
                      trigger="click"
                      getPopupContainer={() => inputWithPopoverRef.current[field.id]}
                      open={modalLanguageOpen === field.id}
                      onOpenChange={handleOnOpenModalLanguageChange}>
                      <InputFieldSuffix ref={(el) => inputWithPopoverRef.current[field.id] = el} style={{flexGrow: 1}}
                                        type={'textarea'}
                                        name={`messages.${index}.value`}
                                        suffix={<Icon iconName={'RiTranslate2'} size={16} style={{color: '#6F767E'}}/>}
                                        onMouseDownSuffix={(e) => handleMouseDownSuffix(e)}
                                        onClickSuffix={(e) => handleClickSuffix(e, field.id, index)}
                                        onChangeInput={(e, name) => {
                                          debounceOnChangeInput(e, index);
                                          handleChangeInput()
                                        }}
                                        onClickInput={(e) => handleClickInput(e)}
                                        onFocusInput={(e, name) => handleOnFocusInput(e, name, index)}
                                        disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                                        setValue={setValue}/>
                    </Popover>

                    {
                      status && status === STUDIO_STATUS.DEV && (
                        <div className="hover:cursor-pointer" onClick={(e) => handleDeleteMessage(index, field.id)}>
                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                        </div>
                      )
                    }
                  </div>
                ))
              }
              <div className="col-span-1 w-full flex space-x-2 mt-4">
                <CheckboxField
                  name={`streaming`}
                  control={control}
                  disabled={status && status === STUDIO_STATUS.LIVE}
                  onChange={() => setDisableSaveBtn(false)}
                />
                <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                  Streaming
                </Typography.Text>
              </div>
            </div>

            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddMessage}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add multiple message for randomizing</div>
                </div>
              )
            }

            <div className="w-full flex justify-end items-center space-x-4" style={{marginTop: 24}}>
              <StyledButtonModal
                type="button"
                key="cancel"
                $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                onClick={() => {
                  if (!confirmLoading) {
                    setModalOpen(false)
                    setModalLanguageOpen(null)
                  }
                }}
              >
                Cancel
              </StyledButtonModal>
              <StyledButtonModal
                type="submit"
                key="save"
                $bgColor={"#7F75CF"}
                $color={"white"}
                disabled={disableSaveBtn || (status && status === STUDIO_STATUS.LIVE)}
              >
                Save
              </StyledButtonModal>
            </div>
          </form>
        </Spin>
      </Modal>
    </StyledNodeFlow>
  );
};

export default TextNode;
