// @ts-nocheck
import GoTo<PERSON>lockHandle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import CheckboxField from "@flow-editor-v1/components/form/CheckboxField";
import InputField from "@flow-editor-v1/components/form/InputField";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import Slider<PERSON>ield from "@flow-editor-v1/components/form/SliderField";
import NodeHeader from "@flow-editor-v1/components/nodes/NodeHeader";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { STUDIO_STATUS, TOOL_MODEL_LIST } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { BasicLLMChainNodeData, FlowDebugState, VariableDefState } from "@flow-editor-v1/model";
import {
    useBuildFlowState,
    useFlowDebugState,
    useFlowInstanceState,
    useLayoutState,
    useStudioState,
    useVariableDefState,
    useVariableLocatedState,
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { extractVariableNames, hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { Modal, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { VARIABLE_POSITION_TYPE } from "../../../constant/variable";

const StyledHandleSourceAnchor = styled.div<{ $bgColor; $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => (props.$data ? "white" : `rgba(255, 255, 255, 0.5)`)};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => (props.$data ? "normal" : "italic")};
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const basicLLMChainNodeDataFormSchema = yup.object().shape({
  system_prompt: yup.string().required("System prompt is required"),
  model: yup.string().required("Model is required").default('gpt-3.5-turbo-0125'),
  temperature: yup.number().required("Temperature is required").default(0.1),
  use_history: yup.boolean().default(false),
  is_stream: yup.boolean().default(true)
});

const BasicLLMChainNode = ({data}: { data: BasicLLMChainNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [isHoverGoToBlock, setIsHoverGoToBlock] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState((state) => state);
  const {variablesDev} = useVariableDefState<VariableDefState>((state) => state);
  const {flow, setDirty} = useBuildFlowState((state) => state);
  const {setVariableLocations} = useVariableLocatedState(state => state);
  const {status} = useStudioState((state) => state);
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState(state => state);

  const {control, setValue, handleSubmit, reset} = useForm({
    resolver: yupResolver(basicLLMChainNodeDataFormSchema),
  });


  const resetForm = () => {
    reset();
  };

  const onSubmit = (formValue) => {
    data.system_prompt = formValue?.system_prompt;
    data.model = formValue?.model;
    data.temperature = formValue?.temperature;
    data.use_history = Boolean(formValue?.use_history);
    data.is_stream = Boolean(formValue?.is_stream)

    const variableLocationsSystemPrompt = extractVariableNames(data.system_prompt)?.map(name => ({
      position_type: VARIABLE_POSITION_TYPE.FLOW,
      flow_dev_id: flow.id,
      node_id: data.id,
      var_def_dev_name: name,
    }));
    const variableLocations = [...variableLocationsSystemPrompt]
    setVariableLocations(variableLocations, VARIABLE_POSITION_TYPE.FLOW);

    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  };

  useEffect(() => {
    resetForm();
    data.system_prompt && setValue("system_prompt", data.system_prompt);
    data.is_stream && setValue("is_stream", Boolean(data.is_stream));
    data.model && setValue("model", data.model);
    data.temperature && setValue("temperature", Number(data.temperature));
    data.use_history && setValue("use_history", Boolean(data.use_history));
  }, [JSON.stringify(data)]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        <div className="absolute left-0 text-[10px] text-neutral-content dark:!text-dark-neutral-content" style={{top: -20}}>{data.id}</div>
      }
      {status && status === STUDIO_STATUS.DEV && !isHoverGoToBlock && (
        <NodeTooltip data={data} selected={data.selected}/>
      )}
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        <StyledHandleSourceAnchor
          $bgColor={data.node_color}
          $data={data.system_prompt}
          onDoubleClick={handleOpenModal}
        >
          {data.system_prompt ? data.system_prompt : "Configure"}
        </StyledHandleSourceAnchor>
        {
          !data.goToBlockSource ? (
            <Handle
              type="source"
              position={Position.Right}
              id={`${data.id}_source#1`}
              style={{
                height: 10,
                width: 10,
                backgroundColor: theme === 'dark' ? 'white' : 'black',
              }}
            />
          ) : (() => {
              const key = `${data.id}_source#1`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: theme === 'dark' ? 'black' : 'white',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onMouseEnter={() => setIsHoverGoToBlock(true)}
                  onMouseLeave={() => setIsHoverGoToBlock(false)}
                />
              }

              return <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}_source#1`}
                style={{
                  height: 10,
                  width: 10,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            }
          )()
        }
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col space-y-4 mt-6"
        >
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              System Prompt <span style={{color: 'red'}}>*</span>
            </Typography.Text>
            <InputField
              type={"textarea"}
              name={`system_prompt`}
              control={control}
              setValue={setValue}
            />
          </div>

          <div className="w-full grid grid-cols-2 items-center gap-5">
            <div className="col-span-1 w-full flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Model</Typography.Text>
              <SelectField
                name={"model"}
                disabled={status && status === STUDIO_STATUS.LIVE}
                control={control}
                options={TOOL_MODEL_LIST}
              />
            </div>

            <div className="col-span-1 w-full flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Temperature
              </Typography.Text>
              <SliderField
                name={"temperature"}
                disabled={status && status === STUDIO_STATUS.LIVE}
                control={control}
                min={0}
                max={2}
                step={0.1}
                defaultValue={0}
              />
            </div>

            <div className="col-span-1 w-full flex space-x-2">
              <CheckboxField
                name={`use_history`}
                control={control}
                disabled={status && status === STUDIO_STATUS.LIVE}
              />
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Use history
              </Typography.Text>
            </div>

            <div className="col-span-1 w-full flex space-x-2">
              <CheckboxField
                name={`is_stream`}
                control={control}
                disabled={status && status === STUDIO_STATUS.LIVE}
              />
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Use stream
              </Typography.Text>
            </div>
          </div>

          <div
            className="w-full flex justify-end items-center space-x-4"
            style={{marginTop: 24}}
          >
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={status && status === STUDIO_STATUS.LIVE}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default BasicLLMChainNode;
