// @ts-nocheck
import GoTo<PERSON><PERSON>Handle from "@flow-editor-v1/components/flow/GoToBlockHandle";
import CheckboxField from "@flow-editor-v1/components/form/CheckboxField";
import InputField from "@flow-editor-v1/components/form/InputField";
import InputFieldSuffix from "@flow-editor-v1/components/form/InputFieldSuffix";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import Icon from "@flow-editor-v1/components/icon/icon";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import NodeTooltip from "@flow-editor-v1/components/tooltip/NodeTooltip";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { lang } from "@flow-editor-v1/init";
import { EQuickRepliesNodeData, FlowDebugState, IEQuickRepliesAction, LayoutState, VariableDefState } from "@flow-editor-v1/model";
import {
    useB<PERSON><PERSON>lowState,
    useFlowDebugState,
    useFlowInstanceState,
    useLayoutState,
    useStudioState,
    useVariableDefState
} from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import { yupResolver } from "@hookform/resolvers/yup";
import { STUDIO_STATUS } from "@shared/app.constant";
import { Divider, Modal, Popover, Spin, Typography } from "antd";
import _, { debounce } from "lodash";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Handle, Position, useUpdateNodeInternals } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { translationApi } from "../../../api/translationApi";
import NodeHeader from "../NodeHeader";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $color, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => props.$color};
  font-size: 10px;
  border-radius: 8px;
  font-style: ${(props) => props.$data ? 'normal' : 'italic'};
  background: ${(props) =>
    props.$bgColor ? props.$bgColor : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: ${(props) => props.$data ? 'center' : 'space-between'};
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const messageFormSchema = yup.object().shape({
  type: yup.string().required(),
  value: yup.string().required("Message is required"),
  language: yup.string().required()
});

const commentLabelFormSchema = yup.object().shape({
  type: yup.string().required(),
  value: yup.string().required("Comment label is required"),
  language: yup.string().required()
});

const actionFormSchema = yup.object().shape({
  type: yup.string().required("Type is required"),
  text: yup.object().shape({
    type: yup.string().required(),
    text: yup.string().required("Label is required")
  }).required(),
  style: yup.string().oneOf(["default", "primary", "danger"], "Invalid type").required("Style is required"),
  value: yup.string().required("Value is required"),
  action_id: yup.string()
});

const eQuickRepliesNodeDataFormSchema = yup.object().shape({
  send_to: yup.array().of(yup.string().required()).required("Send to is required"),
  title: yup.object().shape({
    type: yup.string().required(),
    value: yup.string().required("Title is required"),
    language: yup.string().required()
  }).required(),
  messages: yup.array().of(messageFormSchema).required(),
  actions: yup.array().of(actionFormSchema).required(),
  comments: yup.boolean(),
  comment_labels: yup.array().of(commentLabelFormSchema),
  output_key: yup.string().nullable()
});

const EQuickRepliesNode = ({data}: { data: EQuickRepliesNodeData }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalLanguageOpen, setModalLanguageOpen] = useState<string | null>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [reversed, setReverseHandle] = useState<boolean>(false);
  const {flowInstance} = useFlowInstanceState(state => state);
  const {variablesDev} = useVariableDefState<VariableDefState>(state => state);
  const {status} = useStudioState(state => state)
  const {debug} = useFlowDebugState<FlowDebugState>(state => state);
  const inputWithPopoverRef = useRef({});
  const {setDirty} = useBuildFlowState((state) => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const updateNodeInternal = useUpdateNodeInternals();
  const {theme} = useLayoutState<LayoutState>(state => state);

  const {
    control,
    setValue,
    handleSubmit,
    reset,
    getValues,
    watch
  } = useForm({
    resolver: yupResolver(eQuickRepliesNodeDataFormSchema)
  })

  const {
    fields: messageFields,
    append: messageAppend,
    remove: messageRemove,
    replace: messageReplace,
    update: messageUpdate
  } = useFieldArray({
    control,
    name: 'messages'
  });

  const {
    fields: commentLabelFields,
    append: commentLabelAppend,
    remove: commentLabelRemove,
    replace: commentLabelReplace,
    update: commentLabelUpdate
  } = useFieldArray({
    control,
    name: 'comment_labels'
  });

  const handleAddCommentLabel = () => {
    commentLabelAppend({
      type: 'text',
      value: '',
      language: ''
    })
  }

  const handleDeleteCommentLabel = (index: number, fieldId: string) => {
    commentLabelRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }


  const {
    fields: actionFields,
    append: actionAppend,
    remove: actionRemove,
    replace: actionReplace,
    update: actionUpdate
  } = useFieldArray({
    control,
    name: 'actions'
  });

  const handleAddMessage = () => {
    messageAppend({
      type: 'text',
      value: '',
      language: ''
    })
  }

  const handleDeleteMessage = (index: number, fieldId: string) => {
    messageRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }

  const handleAddAction = () => {
    actionAppend({
      type: "button",
      text: {
        type: "plain_text",
        text: ""
      },
      style: "default",
      value: ""
    } as IEQuickRepliesAction)
  }

  const handleDeleteAction = (index: number, fieldId: string) => {
    actionRemove(index)
    if (inputWithPopoverRef.current) {
      delete inputWithPopoverRef.current[fieldId]
    }
  }

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    console.log(formValue)
    data.send_to = _.cloneDeep(formValue?.send_to);
    data.title = _.cloneDeep(formValue?.title);
    data.messages = _.cloneDeep(formValue?.messages);
    data.comment_labels = _.cloneDeep(formValue?.comment_labels);
    formValue?.actions.forEach((action, index) => {
      action.action_id = `${data.id}_source#${index + 1}`
    })
    data.actions = _.cloneDeep(formValue?.actions);
    data.comments = formValue?.comments;
    data.output_key = formValue?.output_key;
    resetForm();
    setModalOpen(false);
    setNodeSelected(false);
    resetStyle();
    setDirty();
  }

  const onSubmitLanguage = (e) => {
    e.preventDefault()
    data.messages = _.cloneDeep(getValues().messages) as any;
    data.title = _.cloneDeep(getValues().title) as any;
    data.comment_labels = _.cloneDeep(getValues().comment_labels) as any;
    setModalLanguageOpen(null);
  }

  useEffect(() => {
    resetForm();
    data.send_to && setValue('send_to', data.send_to)
    data.title ? setValue("title", _.cloneDeep(data.title)) : setValue("title", {
      type: 'text',
      value: '',
      language: ''
    });
    messageReplace(_.cloneDeep(data.messages && data.messages.length ? data.messages : [{
      type: 'text',
      value: '',
      language: ''
    }]));
    commentLabelReplace(_.cloneDeep(data.comment_labels && data.comment_labels.length ? data.comment_labels : [{
      type: 'text',
      value: '',
      language: ''
    }]));
    actionReplace(_.cloneDeep(data.actions && data.actions.length ? data.actions : [{
      type: "button",
      text: {
        type: "plain_text",
        text: ""
      },
      style: "default",
      value: ""
    }]));
    data.comments && setValue('comments', data.comments)
    data.output_key && setValue('output_key', data.output_key)
    updateNodeInternal(data.id);
  }, [JSON.stringify(data), modalOpen]);

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true)
  }, [data.debug]);

  const handleClickInput = () => {
    setModalLanguageOpen(null)
  }

  const handleOnFocusInput = () => {
    setModalLanguageOpen(null)
  }

  const handleOnChangeInput = async (sentence, index) => {
    if (sentence && index > -1) {
      setConfirmLoading(true);
      const {language} = await translationApi.detectLanguage({
        sentence,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        setValue(`messages.${index}.language`, language)
        setConfirmLoading(false);
        data.messages = _.cloneDeep(watch().messages) as any;
      }
    }
  }

  const handleOnChangeInputTitle = async (sentence) => {
    if (sentence) {
      setConfirmLoading(true);
      const {language} = await translationApi.detectLanguage({
        sentence,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        setValue(`title.language`, language)
        setConfirmLoading(false);
        data.title = _.cloneDeep(watch().title) as any;
      }
    }
  }

  const handleOnChangeInputCommentLabel = async (sentence, index) => {
    if (sentence) {
      setConfirmLoading(true);
      const {language} = await translationApi.detectLanguage({
        sentence,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        setValue(`comment_labels.${index}.language`, language)
        setConfirmLoading(false);
        data.comment_labels = _.cloneDeep(watch().comment_labels) as any;
      }
    }
  }

  const debounceOnChangeInput = useCallback(debounce(handleOnChangeInput, 1000), []);

  const debounceOnChangeInputTitle = useCallback(debounce(handleOnChangeInputTitle, 1000), []);

  const debounceOnChangeInputCommentLabel = useCallback(debounce(handleOnChangeInputCommentLabel, 1000), []);

  const handleOnOpenModalLanguageChange = () => {
    setModalLanguageOpen(null)
  }

  const handleClickSuffix = async (e, fieldId, index) => {
    e.preventDefault();
    setModalLanguageOpen(fieldId)
    if (!getValues().messages[index].language) {
      const {language} = await translationApi.detectLanguage({
        sentence: watch().messages[index].value,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        messageUpdate(index, {...messageFields[index], language});
        setConfirmLoading(false);
        data.messages = _.cloneDeep(getValues().messages) as any;
      }
    }
  }

  const handleClickSuffixTitle = async (e) => {
    e.preventDefault();
    setModalLanguageOpen('title')
    if (!getValues().title.language) {
      const {language} = await translationApi.detectLanguage({
        sentence: watch().title.value,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        setValue('title.language', language)
        setConfirmLoading(false);
        data.title = _.cloneDeep(getValues().title) as any;
      }
    }
  }

  const handleClickSuffixCommentLabel = async (e, fieldId, index) => {
    e.preventDefault();
    setModalLanguageOpen(fieldId)
    if (!getValues().comment_labels[index].language) {
      const {language} = await translationApi.detectLanguage({
        sentence: watch().comment_labels[index].value,
        api_keys: [localStorage.getItem('current-api-key')]
      });
      if (language) {
        messageUpdate(index, {...messageFields[index], language});
        setConfirmLoading(false);
        data.comment_labels = _.cloneDeep(getValues().comment_labels) as any;
      }
    }
  }

  const handleMouseDownSuffix = (e) => {
    e.preventDefault();
  };

  const handleClosePopover = (e) => {
    e.stopPropagation()
    setModalLanguageOpen(null)
  }

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const reorderedItems = Array.from(getValues().actions);
    const [movedItem] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, movedItem);
    actionReplace(reorderedItems)
  };
  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      {
        <div className="absolute left-0 text-[10px] text-neutral-content dark:!text-dark-neutral-content" style={{top: -20}}>{data.id}</div>
      }
      {
        status && status === STUDIO_STATUS.DEV && (
          <NodeTooltip data={data} selected={data.selected}/>
        )
      }
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <Handle
          type="target"
          position={reversed ? Position.Right : Position.Left}
          id={`${data.id}_target`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black',
          }}
        />
        {data.actions && data.actions.length ? (
          <div>
            {data.actions.map((action, index) => {
              if (index === 0) {
                return (
                  <StyledHandleSourceAnchor
                    key={index}
                    $bgColor={action.style == "primary" ? '#007a5a' : action.style == "danger" ? '#e01e5a' : action.style == "default" ? '#fff' : '#fff'}
                    $color={action.style == "primary" ? 'white' : action.style == "danger" ? '#e01e5a' : action.style == "default" ? 'black' : 'black'}
                    $data={data.actions}
                    onDoubleClick={handleOpenModal}
                  >
                    {action.text.text ? action.text.text : "Configure"}
                  </StyledHandleSourceAnchor>
                );
              }
              if (index > 0) {
                return (
                  <StyledHandleSourceAnchor
                    key={index}
                    $bgColor={action.style == "primary" ? '#007a5a' : action.style == "danger" ? '#e01e5a' : action.style == "default" ? '#fff' : '#fff'}
                    $color={action.style == "primary" ? 'white' : action.style == "danger" ? 'white' : action.style == "default" ? 'black' : 'black'}
                    $data={data.actions}
                    onDoubleClick={handleOpenModal}
                  >
                    {action.text.text ? action.text.text : "Configure"}
                  </StyledHandleSourceAnchor>
                );
              }
            })}
          </div>
        ) : (
          <StyledHandleSourceAnchor
            $bgColor={data.node_color}
            $color={'white'}
            $data={data.actions}
            onDoubleClick={handleOpenModal}
          >
            Configure
          </StyledHandleSourceAnchor>
        )}
        <div>
          {data.actions &&
            data.actions.length > 0 && !data.goToBlockSource &&
            data.actions.map((_, index) => (
              <Handle
                key={`${data.id}_source#${index + 1}`}
                type="source"
                position={Position.Right}
                id={`${data.id}_source#${index + 1}`}
                style={{
                  height: 8,
                  width: 8,
                  top: `${59 + 37 * index}px`,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            ))}

          {data.actions &&
            data.actions.length > 0 && data.goToBlockSource && data.goToBlockSource.length > 0 &&
            data.actions.map((_, index) => {
              const key = `${data.id}_source#${index + 1}`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  key={key}
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    top: `${59 + 37 * index}px`,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: theme === 'dark' ? 'black' : 'white',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              }

              return <Handle
                key={key}
                type="source"
                position={Position.Right}
                id={key}
                style={{
                  height: 8,
                  width: 8,
                  top: `${59 + 37 * index}px`,
                  backgroundColor: theme === 'dark' ? 'white' : 'black',
                }}
              />
            })}
        </div>
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => {
          if (!confirmLoading) setModalOpen(false)
        }}
        onCancel={() => {
          if (!confirmLoading) setModalOpen(false)
        }}
        footer={null}
        closable={false}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <Spin spinning={confirmLoading}>
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
            <div className="flex flex-col space-y-2">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Send to <span style={{color: 'red'}}>*</span></Typography.Text>
              <SelectField name={'send_to'} disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                           mode={'tags'} options={[]} style={{width: '100%'}}/>
            </div>

            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Title <span style={{color: 'red'}}>*</span></Typography.Text>
              <div className="flex items-center justify-between gap-4 mt-2">
                <Popover
                  color={theme === 'light' ? '#EBEBEF' : '#1E232E'}
                  content={
                    <div className="flex flex-col space-y-4 mt-6">
                      <SelectField
                        name={`title.language`}
                        control={control}
                        options={lang}
                      />

                      <div
                        className="w-full flex justify-end items-center space-x-4"
                        style={{marginTop: 24}}
                      >
                        <StyledButtonModal
                          type="button"
                          key="cancel"
                          $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                          $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                          $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                          onClick={handleClosePopover}
                        >
                          Cancel
                        </StyledButtonModal>
                        <StyledButtonModal
                          $bgColor={"#7F75CF"}
                          $color={"white"}
                          type={undefined}
                          onClick={onSubmitLanguage}
                        >
                          Save
                        </StyledButtonModal>
                      </div>
                    </div>
                  }
                  title="Localization"
                  placement="left"
                  getPopupContainer={() => inputWithPopoverRef.current['title']}
                  open={modalLanguageOpen === 'title'}
                  onOpenChange={handleOnOpenModalLanguageChange}>
                  <InputFieldSuffix ref={(el) => inputWithPopoverRef.current['title'] = el} style={{flexGrow: 1}}
                                    type={'textarea'}
                                    rows={2}
                                    name={`title.value`}
                                    suffix={<Icon iconName={'RiTranslate2'} size={16} style={{color: '#6F767E'}}/>}
                                    onMouseDownSuffix={(e) => handleMouseDownSuffix(e)}
                                    onClickSuffix={(e) => handleClickSuffixTitle(e)}
                                    onChangeInput={(e, name) => debounceOnChangeInputTitle(e)}
                                    onClickInput={(e) => handleClickInput()}
                                    onFocusInput={(e, name) => handleOnFocusInput()}
                                    disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                                    setValue={setValue}/>
                </Popover>
              </div>
            </div>

            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Messages <span style={{color: 'red'}}>*</span>
              </Typography.Text>
              {
                messageFields.map((field, index) => (
                  <div key={field.id} className="flex items-center justify-between gap-4 mt-2">
                    <Popover
                      color={theme === 'light' ? '#EBEBEF' : '#1E232E'}
                      content={
                        <div className="flex flex-col space-y-4 mt-6">
                          <SelectField
                            name={`messages[${index}].language`}
                            control={control}
                            options={lang}
                          />

                          <div
                            className="w-full flex justify-end items-center space-x-4"
                            style={{marginTop: 24}}
                          >
                            <StyledButtonModal
                              type="button"
                              key="cancel"
                              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                              onClick={handleClosePopover}
                            >
                              Cancel
                            </StyledButtonModal>
                            <StyledButtonModal
                              $bgColor={"#7F75CF"}
                              $color={"white"}
                              type={undefined}
                              onClick={onSubmitLanguage}
                            >
                              Save
                            </StyledButtonModal>
                          </div>
                        </div>
                      }
                      title="Localization"
                      placement="left"
                      getPopupContainer={() => inputWithPopoverRef.current[field.id]}
                      open={modalLanguageOpen === field.id}
                      onOpenChange={handleOnOpenModalLanguageChange}>
                      <InputFieldSuffix ref={(el) => inputWithPopoverRef.current[field.id] = el} style={{flexGrow: 1}}
                                        type={'textarea'}
                                        name={`messages.${index}.value`}
                                        suffix={<Icon iconName={'RiTranslate2'} size={16} style={{color: '#6F767E'}}/>}
                                        onMouseDownSuffix={(e) => handleMouseDownSuffix(e)}
                                        onClickSuffix={(e) => handleClickSuffix(e, field.id, index)}
                                        onChangeInput={(e, name) => debounceOnChangeInput(e, index)}
                                        onClickInput={(e) => handleClickInput()}
                                        onFocusInput={(e, name) => handleOnFocusInput()}
                                        disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                                        setValue={setValue}/>
                    </Popover>
                    {
                      status && status === STUDIO_STATUS.DEV && (
                        <div className="hover:cursor-pointer" onClick={(e) => handleDeleteMessage(index, field.id)}>
                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                        </div>
                      )
                    }
                  </div>
                ))
              }
            </div>

            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddMessage}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add multiple message for randomizing</div>
                </div>
              )
            }

            <div className="w-full flex space-x-2">
              <CheckboxField
                name={`comments`}
                control={control}
                disabled={status && status === STUDIO_STATUS.LIVE}
              />
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Comments
              </Typography.Text>
            </div>

            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
                Comment label
              </Typography.Text>
              {
                commentLabelFields.map((field, index) => (
                  <div key={field.id} className="flex items-center justify-between gap-4 mt-2">
                    <Popover
                      color={theme === 'light' ? '#EBEBEF' : '#1E232E'}
                      content={
                        <div className="flex flex-col space-y-4 mt-6">
                          <SelectField
                            name={`comment_labels[${index}].language`}
                            control={control}
                            options={lang}
                          />

                          <div
                            className="w-full flex justify-end items-center space-x-4"
                            style={{marginTop: 24}}
                          >
                            <StyledButtonModal
                              type="button"
                              key="cancel"
                              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                              onClick={handleClosePopover}
                            >
                              Cancel
                            </StyledButtonModal>
                            <StyledButtonModal
                              $bgColor={"#7F75CF"}
                              $color={"white"}
                              type={undefined}
                              onClick={onSubmitLanguage}
                            >
                              Save
                            </StyledButtonModal>
                          </div>
                        </div>
                      }
                      title="Localization"
                      placement="left"
                      getPopupContainer={() => inputWithPopoverRef.current[field.id]}
                      open={modalLanguageOpen === field.id}
                      onOpenChange={handleOnOpenModalLanguageChange}>
                      <InputFieldSuffix ref={(el) => inputWithPopoverRef.current[field.id] = el} style={{flexGrow: 1}}
                                        type={'textarea'}
                                        rows={2}
                                        name={`comment_labels[${index}].value`}
                                        suffix={<Icon iconName={'RiTranslate2'} size={16} style={{color: '#6F767E'}}/>}
                                        onMouseDownSuffix={(e) => handleMouseDownSuffix(e)}
                                        onClickSuffix={(e) => handleClickSuffixCommentLabel(e, field.id, index)}
                                        onChangeInput={(e, name) => debounceOnChangeInputCommentLabel(e, index)}
                                        onClickInput={(e) => handleClickInput()}
                                        onFocusInput={(e, name) => handleOnFocusInput()}
                                        disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                                        setValue={setValue}/>
                    </Popover>
                    {
                      status && status === STUDIO_STATUS.DEV && (
                        <div className="hover:cursor-pointer" onClick={(e) => handleDeleteCommentLabel(index, field.id)}>
                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                        </div>
                      )
                    }
                  </div>
                ))
              }
            </div>

            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddCommentLabel}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add comment label</div>
                </div>
              )
            }

            <div className="flex flex-col">
              <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Actions</Typography.Text>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="droppable-1" direction="vertical">
                  {
                    (provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="flex flex-col mt-2 space-y-3"
                      >
                        {
                          actionFields.map((field, index) => (
                            <Draggable key={field.id} draggableId={field.id} index={index}>
                              {
                                (provided) => (
                                  <div ref={provided.innerRef}
                                       {...provided.draggableProps}
                                       {...provided.dragHandleProps}
                                       style={{
                                         userSelect: "none",
                                         ...provided.draggableProps.style
                                       }}
                                       className="p-2 rounded-lg flex items-center justify-center border border-gray-700">
                                    <div className="cursor-move">
                                      <Icon iconName={'RiDraggable'} size={20} style={{color: '#6F767E'}}/>
                                    </div>
                                    <div className="flex-grow flex flex-col space-y-2">
                                      <div className="grid grid-cols-5 gap-6 items-center">
                                        <Typography.Text
                                          className="col-span-1 !text-neutral-content dark:!text-dark-neutral-content text-right">Label <span style={{color: 'red'}}>*</span></Typography.Text>
                                        <div className="col-span-4 w-full">
                                          <InputField
                                            type={"input"}
                                            name={`actions[${index}].text.text`}
                                            control={control}
                                            disabled={status && status === STUDIO_STATUS.LIVE}
                                            setValue={setValue}
                                          />
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-5 gap-6 items-center">
                                        <Typography.Text
                                          className="!text-neutral-content dark:!text-dark-neutral-content col-span-1 text-right">Style <span style={{color: 'red'}}>*</span></Typography.Text>
                                        <div className="col-span-4 w-full">
                                          <SelectField style={{width: '100%'}}
                                                       name={`actions[${index}].style`}
                                                       disabled={status && status === STUDIO_STATUS.LIVE}
                                                       control={control}
                                                       options={[{key: "default", value: "default", label: "Default"}, {
                                                         key: "primary",
                                                         value: "primary",
                                                         label: "Primary"
                                                       }, {key: "danger", value: "danger", label: "Danger"}].map(v => ({
                                                         ...v,
                                                         key: v.key,
                                                         value: v.value,
                                                         label: v.label
                                                       }))}/>
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-5 gap-6 items-center">
                                        <Typography.Text
                                          className="!text-neutral-content dark:!text-dark-neutral-content col-span-1 text-right">Event ID <span style={{color: 'red'}}>*</span></Typography.Text>
                                        <div className="col-span-4 w-full">
                                          <InputField
                                            type={"input"}
                                            name={`actions[${index}].value`}
                                            control={control}
                                            disabled={status && status === STUDIO_STATUS.LIVE}
                                            setValue={setValue}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                    {
                                      status && status === STUDIO_STATUS.DEV && (
                                        <div className="ml-3 hover:cursor-pointer"
                                             onClick={(e) => handleDeleteAction(index, field.id)}>
                                          <Icon iconName={'RiDeleteBinLine'} size={24} style={{color: '#6F767E'}}/>
                                        </div>
                                      )
                                    }
                                  </div>
                                )
                              }
                            </Draggable>
                          ))
                        }
                        {provided.placeholder}
                      </div>
                    )
                  }
                </Droppable>
              </DragDropContext>
            </div>

            {
              status && status === STUDIO_STATUS.DEV && (
                <div className="flex gap-3 items-center hover:cursor-pointer" onClick={handleAddAction}>
                  <Icon iconName={'RiAddCircleFill'} size={18} style={{color: data.node_color}}/>
                  <div style={{color: data.node_color}}>Add action</div>
                </div>
              )
            }

            <Divider style={{borderColor: "white"}}/>

            <div className="w-full flex justify-end">
              <div className="flex justify-end items-center space-x-2">
                <Typography.Text className="flex-shrink-0 !text-neutral-content dark:!text-dark-neutral-content">Store response in:</Typography.Text>
                <InputField
                  type={"input"}
                  name={`output_key`}
                  control={control}
                  setValue={setValue}
                />
              </div>
            </div>

            <div className="w-full flex justify-end items-center space-x-4" style={{marginTop: 24}}>
              <StyledButtonModal
                type="button"
                key="cancel"
                $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
                $color={theme === 'light' ? '#282826' : '#F8FAFF'}
                $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
                onClick={() => {
                  if (!confirmLoading) setModalOpen(false)
                }}
              >
                Cancel
              </StyledButtonModal>
              <StyledButtonModal
                type="submit"
                key="save"
                $bgColor={"#7F75CF"}
                $color={"white"}
                disabled={status && status === STUDIO_STATUS.LIVE}
              >
                Save
              </StyledButtonModal>
            </div>
          </form>
        </Spin>
      </Modal>
    </StyledNodeFlow>
  );
};

export default EQuickRepliesNode;
