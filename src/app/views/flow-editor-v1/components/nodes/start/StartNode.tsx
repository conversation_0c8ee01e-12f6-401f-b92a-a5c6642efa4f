// @ts-nocheck
import { flowDev<PERSON><PERSON>, intent<PERSON>pi, intentDev<PERSON>pi } from "@flow-editor-v1/api";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import { StyledButtonModal, StyledNodeFlow } from "@flow-editor-v1/components/styled";
import { STUDIO_STATUS } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { Event, IntentDev, LayoutState, StartNodeData } from "@flow-editor-v1/model";
import { ChatFlow, ChatFlowDev, Intent } from "@flow-editor-v1/model/bo";
import { useBuildFlowState, useFlowInstanceState, useLayoutState, useStudioState } from "@flow-editor-v1/store";
import { isValidConnection } from "@flow-editor-v1/utils/flow";
import { hexToRgb } from "@flow-editor-v1/utils/util";
import { yupResolver } from "@hookform/resolvers/yup";
import { FLOW_TRIGGER_TYPE } from "@shared/app.constant";
import { Modal, Typography } from "antd";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Handle, Position } from "reactflow";
import styled from "styled-components";
import * as yup from "yup";
import { eventApi } from "../../../api/eventApi";
import NodeHeader from "../NodeHeader";

const StyledHandleSourceAnchor = styled.div<{ $bgColor; $intent }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: ${(props) => (props.$intent ? "white" : `rgba(255, 255, 255, 0.5)`)};
  font-size: 10px;
  border-radius: 8px;
  font-style: italic;
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const startNodeDataFormSchema = yup.object().shape({
  intent_id: yup.number(),
  event_id: yup.string(),
});

const StartNode = ({data}: { data: StartNodeData }) => {
  const ref = useRef(null);
  const [intents, setIntents] = useState<Array<Intent | IntentDev>>([]);
  const [events, setEvents] = useState<Array<Event>>([]);
  const [intent, setIntent] = useState<Intent | IntentDev>(null);
  const [event, setEvent] = useState<Event>(null);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {flow, setFlow} = useBuildFlowState((state) => state);
  const {flowInstance} = useFlowInstanceState((state) => state);
  const {status} = useStudioState((state) => state);
  const {resetStyle, unSelectNode} = useFlowInstance();
  const [nodeSelected, setNodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState<LayoutState>(state => state);

  const {control, setValue, handleSubmit, reset} = useForm({
    resolver: yupResolver(startNodeDataFormSchema),
  });

  const resetForm = () => {
    reset();
  };

  const onSubmit = async (formValue) => {
    if (flow && flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT) {
      data.intent_id = formValue?.intent_id;
      const intentSelected: IntentDev = intents.find(
        (i) => i.id === data.intent_id
      ) as IntentDev;
      data.intent = intentSelected;
      setFlow({
        ...flow,
        intent_id: data.intent_id,
      });
      const body: ChatFlowDev = {
        ...flow,
        intent_dev_id: data.intent_id,
        flow_data: JSON.stringify({
          nodes: flowInstance.getNodes(),
          edges: flowInstance.getEdges(),
        }),
      };
      setFlow(body);
      await flowDevApi.saveFlowDev(body);
      resetForm();
      setNodeSelected(false);
      resetStyle();
      setModalOpen(false);
    }
    if (flow && flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT) {
      data.event_id = formValue?.event_id;
      const eventSelected = events.find(
        (i) => i.id === data.event_id
      );
      data.event = eventSelected;
      setFlow({
        ...flow,
        event_id: data.event_id,
      });
      const body: ChatFlowDev = {
        ...flow,
        event_id: data.event_id,
        flow_data: JSON.stringify({
          nodes: flowInstance.getNodes(),
          edges: flowInstance.getEdges(),
        }),
      };
      setFlow(body);
      await flowDevApi.saveFlowDev(body);
      resetForm();
      setModalOpen(false);
    }
  };

  useEffect(() => {
    resetForm();
    if (flow && status === STUDIO_STATUS.DEV) {
      if (flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT) {
        setValue("intent_id", (flow as ChatFlowDev).intent_dev_id);
        data.intent = intents.find(
          (i) => i.id === (flow as ChatFlowDev).intent_dev_id
        ) as IntentDev;
        setIntent(data.intent);
      }
      if (flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT) {
        setValue("event_id", (flow as ChatFlowDev).event_id);
        data.event = events.find(
          (i) => i.id === (flow as ChatFlowDev).event_id
        ) as Event;
        setEvent(data.event);
      }
    }
    if (flow && status === STUDIO_STATUS.LIVE) {
      if (flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT) {
        setValue("intent_id", (flow as ChatFlow).intent_id);
        data.intent = intents.find(
          (i) => i.id === (flow as ChatFlow).intent_id
        ) as Intent;
        setIntent(data.intent);
      }
      if (flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT) {
        setValue("event_id", (flow as ChatFlow).event_id);
        data.event = events.find(
          (i) => i.id === (flow as ChatFlow).event_id
        ) as Event;
        setEvent(data.event);
      }
    }
  }, [JSON.stringify(data), intents, status]);

  useEffect(() => {
    const fetchIntentDev = async () => {
      const res: IntentDev[] = await intentDevApi.getIntentDev();
      if (flow && res && res.length > 0) {
        const filteredIntents = res.filter(item => !item.flow_dev);
        setIntents(filteredIntents);
      }
    };

    const fetchIntentLive = async () => {
      const res: Intent[] = await intentApi.getIntent();
      if (flow && res && res.length > 0) {
        const filteredIntents = res.filter(item => !item.flow);
        setIntents(filteredIntents);
      }
    };

    const fetchEvent = async () => {
      const res: Event[] = await eventApi.getEvent();
      if (flow && res && res.length > 0) {
        const filteredIntents = res.filter(item => !item.flow_dev);
        setEvents(res);
      }
    }

    if (flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT) {
      if (status === STUDIO_STATUS.DEV) {
        void fetchIntentDev();
      }

      if (status === STUDIO_STATUS.LIVE) {
        void fetchIntentLive();
      }
    }

    if (flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT) {
      void fetchEvent();
    }
  }, [JSON.stringify(flow), status]);

  const handleClearIntentChange = useCallback(async (name: string) => {
    data.intent_id = null;
    data.intent = null
    setIntent(null);
    setFlow({
      ...flow,
      intent_id: null,
    });
    const body: ChatFlowDev = {
      ...flow,
      intent_dev_id: null,
      flow_data: JSON.stringify({
        nodes: flowInstance.getNodes(),
        edges: flowInstance.getEdges(),
      }),
    };
    setFlow(body);
    await flowDevApi.saveFlowDev(body);
    resetForm();
    setNodeSelected(false);
    resetStyle();
    setModalOpen(false);
  }, [])

  const handleClearEventChange = useCallback(async (name: string) => {
    data.event_id = null;
    data.event = null
    setEvent(null);
    setFlow({
      ...flow,
      event_id: null,
    });
    const body: ChatFlowDev = {
      ...flow,
      event_id: null,
      flow_data: JSON.stringify({
        nodes: flowInstance.getNodes(),
        edges: flowInstance.getEdges(),
      }),
    };
    setFlow(body);
    await flowDevApi.saveFlowDev(body);
    resetForm();
    setNodeSelected(false);
    resetStyle();
    setModalOpen(false);
  }, [])

  const handleOpenModal = useCallback(() => {
    unSelectNode(data.id)
    setNodeSelected(true)
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $debug={data.debug} $theme={theme}>
      <NodeHeader data={data} iconName={data.icon}/>
      <div ref={ref}>
        <StyledHandleSourceAnchor
          $bgColor={data.node_color}
          $intent={!!data.intent_id}
          onDoubleClick={handleOpenModal}
        >
          {(() => {
            if (flow && flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT) {
              return intent ? intent.name : 'Configure'
            }
            if (flow && flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT) {
              return event ? event.name : 'Configure'
            }
          })()}
        </StyledHandleSourceAnchor>
        <Handle
          type="source"
          position={Position.Right}
          id={`${data.id}_source#1`}
          isValidConnection={(connection) =>
            isValidConnection(connection, flowInstance)
          }
          style={{
            height: 8,
            width: 8,
            backgroundColor: theme === 'dark' ? 'white' : 'black'
          }}
        />
      </div>

      <Modal
        title={data.label}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
        afterClose={() => {setNodeSelected(false); resetStyle()}}
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col space-y-4 mt-6"
        >
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">
              {(() => {
                if (flow && flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT) {
                  return 'Intent'
                }
                if (flow && flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT) {
                  return 'Event'
                }
              })()}
            </Typography.Text>
            {(() => {
              if (flow && flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT) {
                return <SelectField
                  name={"intent_id"}
                  control={control}
                  onClearSelect={handleClearIntentChange}
                  disabled={
                    (status && status === STUDIO_STATUS.LIVE) ||
                    (flow &&
                      (flow.name === "Greeting" ||
                        flow.name === "Farewell" ||
                        flow.name === "Feedback" ||
                        flow.name === "Fallback"))
                  }
                  options={intents.map((v) => ({
                    ...v,
                    key: v.id,
                    value: v.id,
                    label: v.name,
                    disabled: !!(v as IntentDev)?.flow_dev,
                  }))}
                />
              }
              if (flow && flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT) {
                return <SelectField
                  name={"event_id"}
                  control={control}
                  onClearSelect={handleClearEventChange}
                  disabled={status && status === STUDIO_STATUS.LIVE}
                  options={events.map((v) => ({
                    ...v,
                    key: v.id,
                    value: v.id,
                    label: v.name
                  }))}
                />
              }
            })()}
          </div>

          <div
            className="w-full flex justify-end items-center space-x-4"
            style={{marginTop: 24}}
          >
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={
                (status && status === STUDIO_STATUS.LIVE) ||
                (flow &&
                  (flow.name === "Greeting" ||
                    flow.name === "Farewell" ||
                    flow.name === "Feedback" ||
                    flow.name === "Fallback"))
              }
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>
    </StyledNodeFlow>
  );
};

export default StartNode;
