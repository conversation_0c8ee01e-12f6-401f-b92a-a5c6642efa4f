// @ts-nocheck
import { FOREIGN_OBJECT_SIZE, STUDIO_STATUS } from "@flow-editor-v1/constant";
import { useFlowInstance } from "@flow-editor-v1/hook";
import { BuildFlowState } from "@flow-editor-v1/model";
import { useBuildFlowState, useStudioState } from "@flow-editor-v1/store";
import React from "react";
import { EdgeText, getSmoothStepPath } from "reactflow";
import styled from "styled-components";

const StyledDeleteBtn = styled.button<{ $opacity: number }>`
  width: 16px;
  height: 16px;
  background: #eee;
  border: 1px solid #fff;
  cursor: pointer;
  border-radius: 50%;
  font-size: 12px;
  line-height: 0;
  opacity: ${(props) => props.$opacity ? props.$opacity : props.$opacity === 0 ? 0 : 0.4};

  &:hover {
    background: red;
    color: #eee;
    box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.08);
  }
`;

const CsEdge = (props) => {
  const {
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    style,
    data,
    markerEnd,
  } = props;

  const [edgePath, edgeCenterX, edgeCenterY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const {setDirty} = useBuildFlowState<BuildFlowState>((state) => state);
  const {status} = useStudioState(state => state);

  const {deleteEdge} = useFlowInstance();

  const onEdgeClick = (evt, id) => {
    if (!status || (status && status === STUDIO_STATUS.LIVE)) return;
    if (data?.isGoToBlock) return;
    evt.stopPropagation();
    deleteEdge(id);
  };

  return (
    <>
      <path
        id={id}
        style={style}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
      />
      {data && data.label && (
        <EdgeText
          x={sourceX - 20}
          y={sourceY - 20}
          label={data.label}
          labelStyle={{fill: "black"}}
          labelBgStyle={{fill: "transparent"}}
          labelBgPadding={[2, 4]}
          labelBgBorderRadius={2}
        />
      )}
      <foreignObject
        width={FOREIGN_OBJECT_SIZE}
        height={FOREIGN_OBJECT_SIZE}
        x={edgeCenterX - FOREIGN_OBJECT_SIZE / 4.6}
        y={edgeCenterY - FOREIGN_OBJECT_SIZE / 3.6}
        className="edgebutton-foreignobject"
        requiredExtensions="http://www.w3.org/1999/xhtml"
      >
        {
          status && status === STUDIO_STATUS.DEV && (
            <div>
              <StyledDeleteBtn $opacity={style ? style.opacity : 1} onClick={(event) => onEdgeClick(event, id)}>
                ×
              </StyledDeleteBtn>
            </div>
          )
        }
      </foreignObject>
    </>
  );
};

export default CsEdge;
