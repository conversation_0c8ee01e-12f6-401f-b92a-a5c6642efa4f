// @ts-nocheck
import { RiBracesFill } from "@remixicon/react";
import React from "react";
import styled from "styled-components";

const StyledVariableButton = styled.button<{ $collapsed }>`
  position: absolute;
  top: 20px;
  right: ${props => props.$collapsed}px;
  z-index: 2;
  width: 48px;
  height: 48px;
  background: white;
  box-shadow: 0 4px 70px 0 rgba(102, 77, 255, 0.3);
  border-radius: 100px;
  border: 2px solid #7F75CF;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;

  &:hover {
    cursor: pointer;
  }
`;

const VariableButton = (props) => {
  const { collapsed, onClick } = props;

  return (
    <StyledVariableButton onClick={onClick} $collapsed={collapsed ? -52 : -64}>
      <RiBracesFill size={22} style={{color:'#7F75CF'}} />
    </StyledVariableButton>
  );
};

export default VariableButton;
