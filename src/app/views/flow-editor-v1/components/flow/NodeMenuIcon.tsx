// @ts-nocheck
import { useLayoutState } from "@flow-editor-v1/store";
import * as icons from "@remixicon/react";
import React from "react";
import styled from "styled-components";

const StyledNodeMenuIcon = styled.div`
  color: ${({$theme}) => $theme === "light" ? "black" : "white"};
`

const NodeMenuIcon = ({ iconName }) => {
  const {theme} = useLayoutState(state => state);
  const iconComp = iconName && icons[iconName] ? icons[iconName] : icons.RiProhibited2Fill;
  return (
    <StyledNodeMenuIcon $theme={theme}>
      {React.createElement(iconComp, { size: 24 })}
    </StyledNodeMenuIcon>
  )
};

export default NodeMenuIcon
