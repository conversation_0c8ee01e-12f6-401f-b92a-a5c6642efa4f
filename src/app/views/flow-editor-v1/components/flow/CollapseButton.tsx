// @ts-nocheck
import { useLayoutState } from "@flow-editor-v1/store";
import { RiArrowLeftSLine, RiArrowRightSLine } from "@remixicon/react";
import React from "react";
import styled from "styled-components";

const StyledCollapseButton = styled.button<{ $collapsed: any, $theme: string }>`
  position: absolute;
  top: 40%;
  right: ${props => props.$collapsed}px;
  z-index: 3;
  border: none;
  width: 32px;
  height: 72px;
  background: ${(props) => (props.$theme === 'dark' ? 'linear-gradient(90deg, #0D121E 0%, #7F75CF 16%)' : 'linear-gradient(90deg, #E2E3E8 0%, #7F75CF 16%)')};
  box-shadow: 0 4px 100px 0 rgba(102, 77, 255, 0.2);
  border-radius: 0 10px 10px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  cursor: pointer;
`;

const CollapseButton = (props) => {
  const { collapsed, onClick } = props;
  const { theme } = useLayoutState(state => state);

  return (
    <StyledCollapseButton onClick={onClick} $collapsed={collapsed ? 9 : -32} $theme={theme}>
      {collapsed ? (
        <RiArrowRightSLine size={18} />
      ) : (
        <RiArrowLeftSLine size={18} />
      )}
    </StyledCollapseButton>
  );
};

export default CollapseButton;
