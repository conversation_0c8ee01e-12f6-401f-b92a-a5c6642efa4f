// @ts-nocheck
import { nodeApi } from "@flow-editor-v1/api";
import { NodeDefine } from "@flow-editor-v1/model/bo";
import { useBuildFlowState, useLayoutState } from "@flow-editor-v1/store";
import { RiArrowDownSFill } from "@remixicon/react";
import type { MenuProps } from "antd";
import { Dropdown, Space } from "antd";
import React, { memo, useEffect, useState } from "react";
import styled from "styled-components";
import NodeMenuIcon from "./NodeMenuIcon";

const StyledMenuPrompts = styled.div`
  padding: 16px;

  .dropdown-trigger {
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
  }

  .list-node {
    position: absolute;
    top: 48px;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
    }

    &::-webkit-scrollbar-thumb {
      border: 2px solid transparent;
      border-radius: 16px;
      background-clip: content-box;
    }

    & > :not(:last-child) {
      margin-block-start: 12px;
      margin-block-end: 12px;
    };
  }
`;

const StyledNodeItem = styled.div`
  cursor: move;
  border: 1px solid transparent;
  border-radius: 12px;
  display: flex;
  align-items: start;
  gap: 12px;
  padding: 8px 12px;
  font-weight: normal;
  margin-right: 16px;

  .node-name {
    margin-bottom: 3px;
    color: ${({theme}) => theme === "light" ? "#282826" : "#F8FAFF"};
    font-size: 15px;
  }

  .node-des {
    color: ${({theme}) => theme === "light" ? "#6F767E" : "#ADAFB9"};
    font-size: 13px;
  }

  &:hover {
    border-radius: 12px;
    border: 1px solid #7F75CF;
    background: ${({theme}) => theme === "light" ? "#F5F5F7" : "#343942"};
  }
`;

const items: MenuProps["items"] = [
  {
    label: "Prompts",
    key: "Prompt",
  },
  {
    label: "Message",
    key: "Message",
  },
  {
    label: "Actions",
    key: "Action",
  },
  {
    label: "Logic",
    key: "Logic",
  },
];

const itemsMapping = [
  {
    label: "Prompts",
    key: "Prompt",
  },
  {
    label: "Message",
    key: "Message",
  },
  {
    label: "Actions",
    key: "Action",
  },
  {
    label: "Logic",
    key: "Logic",
  }
];

const MenuPrompts = () => {
  const [item, setItem] = useState(itemsMapping[0]);
  const [nodes, setNodes] = useState<Array<NodeDefine>>([]);
  const {flow} = useBuildFlowState(state => state);
  const {theme} = useLayoutState(state => state);

  useEffect(() => {
    if (flow) {
      const fetchNodes = async () => {
        const res: Array<NodeDefine> = await nodeApi.getListNode(flow.trigger_type);
        setNodes(res);
      }

      fetchNodes()
        .catch(console.error);
    }
  }, [flow])


  const handleSelected = ({key}) => {
    setItem(itemsMapping.find((item) => item.key === key));
  };

  const handleDragStart = (event: React.DragEvent<HTMLDivElement>, nodeData: string) => {
    event.dataTransfer.setData("application/reactflow", nodeData);
    event.dataTransfer.effectAllowed = "move";
  };

  return (
    <StyledMenuPrompts $theme={theme}>
      <Dropdown
        menu={{
          items,
          selectable: true,
          onSelect: handleSelected,
          defaultSelectedKeys: ["prompt"],
        }}
        trigger={["click"]}
      >
        <div className="dropdown-trigger">
          <Space>
            {item.label}
            <RiArrowDownSFill size={14}/>
          </Space>
        </div>
      </Dropdown>
      <div className="list-node">
        {item && item.key && (() => {
          const filteredNodes = nodes.filter(v => v.type === item.key);
          return filteredNodes.map((node, index) => (
            <StyledNodeItem
              key={node.name}
              theme={theme}
              onDragStart={(event) => handleDragStart(event, node.data)}
              draggable
            >
              <NodeMenuIcon iconName={node.icon}/>
              <div>
                <div className="node-name">{node.name}</div>
                <div className="node-des">{node.description}</div>
              </div>
            </StyledNodeItem>
          ));
        })()}

      </div>
    </StyledMenuPrompts>
  );
};

export default memo(MenuPrompts);
