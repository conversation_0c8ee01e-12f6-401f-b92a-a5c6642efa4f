// @ts-nocheck
import React, { useEffect, useState } from 'react';

// Theme-aware header component for EditorFlowView
const ThemedEditorHeader = ({
  flow,
  previewOpened,
  debug,
  isDirty,
  onChangeDebugMode,
  togglePreview,
  handlePublishFlow,
  handleRevertFlow,
  handleSave
}) => {
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light');

  // Initialize theme and subscribe to theme changes
  useEffect(() => {
    // Get initial theme
    if (window.themeService) {
      const initialTheme = window.themeService.getCurrentThemeValue();
      setCurrentTheme(initialTheme);

      // Subscribe to theme changes
      const subscription = window.themeService.getTheme().subscribe((theme) => {
        setCurrentTheme(theme);
      });

      return () => subscription.unsubscribe();
    } else {
      // Fallback to checking document class if themeService is not available
      const isDarkMode = document.documentElement.classList.contains('dark');
      setCurrentTheme(isDarkMode ? 'dark' : 'light');
    }
  }, []);

  return (
    <div className={`flex items-center justify-between ${currentTheme === 'light' ? 'text-light-text' : 'text-dark-text'}`}>
      <div className="flex items-end justify-start space-x-3">
        <h1 className={`text-2xl font-bold ${currentTheme === 'light' ? 'text-light-text' : 'text-dark-text'}`}>Flows</h1>
        {flow && <div className={`text-2xl ${currentTheme === 'light' ? 'text-light-text' : 'text-dark-text'}`}>/</div>}
        {flow && (
          <div className={`text-xl ${currentTheme === 'light' ? 'text-light-text' : 'text-dark-text'} underline hover:cursor-pointer hover:text-light-primary dark:hover:text-dark-primary`}>
            {flow.name}
          </div>
        )}
      </div>
      <div className="flex items-center justify-end grow space-x-3">
        {previewOpened ? (
          <div className="flex items-center justify-end space-x-3">
            <div className={`${currentTheme === 'light' ? 'bg-light-background' : 'bg-dark-background'} p-1 rounded`}>
              <button
                type="button"
                style={{backgroundColor: debug ? 'orange' : '#6b7280'}}
                className={`px-3 py-1 rounded-lg text-white ${debug ? 'font-bold' : ''}`}
                onClick={() => onChangeDebugMode(!debug)}
              >
                {debug ? 'Debug Mode' : 'Normal Mode'}
              </button>
            </div>
            <div className="h-100 flex items-center">
              <button
                type="button"
                style={{height: 48, width: 128}}
                className={`${currentTheme === 'light' ? 'bg-light-gray' : 'bg-dark-gray'} rounded-lg flex items-center justify-center text-white button-disabled`}
                onClick={togglePreview}
              >
                <span>Hide preview</span>
              </button>
            </div>
          </div>
        ) : (
          <div className="h-100 flex items-center">
            <button
              type="button"
              style={{
                height: 48,
                width: 128,
              }}
              className="bg-light-turquoise dark:bg-dark-turquoise rounded-lg flex items-center justify-center text-white"
              onClick={togglePreview}
            >
              <span>Preview</span>
            </button>
          </div>
        )}
        <div className="h-100 flex items-center">
          <button
            type="button"
            style={{height: 48, width: 128}}
            className="bg-light-primary dark:bg-dark-primary rounded-lg flex items-center justify-center text-white"
            onClick={handlePublishFlow}
          >
            <span>Publish</span>
          </button>
        </div>
        <div className="h-100 flex items-center">
          <button
            type="button"
            style={{height: 48, width: 128}}
            className="bg-light-orange dark:bg-dark-orange rounded-lg flex items-center justify-center text-white"
            onClick={handleRevertFlow}
          >
            <span>Revert</span>
          </button>
        </div>
        <div className="h-100 flex items-center">
          <button
            type="button"
            style={{height: 48, width: 128}}
            className="bg-light-primary dark:bg-dark-primary rounded-lg flex items-center justify-center text-white button-disabled"
            onClick={() => handleSave()}
          >
            <span>
              Save&ensp;{isDirty !== 0 && <span className="text-red-600">*</span>}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ThemedEditorHeader;
