// @ts-nocheck
import { flowDevApi, variableDefDevApi } from "@flow-editor-v1/api";
import InputField from "@flow-editor-v1/components/form/InputField";
import SelectField from "@flow-editor-v1/components/form/SelectField";
import { StyledButtonModal } from "@flow-editor-v1/components/styled";
import { STUDIO_STATUS, VARIABLE_TYPE, VARIABLE_TYPE_LIST } from "@flow-editor-v1/constant";
import { startNodeEvent, startNodeIntent } from "@flow-editor-v1/init";
import {
  BuildFlowState,
  ChatFlowDev,
  FlowInstanceState,
  VariableDef,
  VariableDefDev,
  VariableDefState
} from "@flow-editor-v1/model";
import { useBuildFlowState, useFlowInstanceState, useLayoutState, useStudioState, useVariableDefState } from "@flow-editor-v1/store";
import { yupResolver } from "@hookform/resolvers/yup";
import { Ri<PERSON>ddLine, RiDeleteBinLine, RiEditLine, RiMore2Fill } from "@remixicon/react";
import { FLOW_TRIGGER_TYPE } from "@shared/app.constant";
import { Dropdown, Input, MenuProps, message, Modal, Table, TableProps, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";

const {Column} = Table;

const variableFormSchema = yup.object().shape({
  id: yup.number().nullable().default(null),
  ai_id: yup.string().default(''),
  flow_id: yup.number().nullable().default(null),
  var_name: yup.string().required('Variable name required').test('no-whitespace', 'Field cannot be empty or whitespace only', value => value.trim().length > 0).default(''),
  type: yup.string().required('Data type required').default(VARIABLE_TYPE.STRING),
  init_value: yup.string().nullable().default(null)
});

const VariableList = () => {
  const items: MenuProps['items'] = [
    {
      key: 'edit',
      label: 'Edit',
      icon: <RiEditLine size={18}/>
    },
    {
      key: 'delete',
      label: 'Delete',
      icon: <RiDeleteBinLine size={18}/>
    },
  ]

  const onClick: MenuProps['onClick'] = ({key}) => {
    if (key === 'edit') {
      if (status && status === STUDIO_STATUS.DEV) {
        resetForm();
        if (variableSelected) {
          setValue('id', variableSelected.id)
          setValue('ai_id', variableSelected.ai_id)
          setValue('var_name', variableSelected.var_name)
          setValue('type', variableSelected.type)
          setValue('init_value', variableSelected.init_value)

          if (status && status === STUDIO_STATUS.DEV) {
            setValue('flow_id', (variableSelected as VariableDefDev).flow_dev_id)
          }
          if (status && status === STUDIO_STATUS.LIVE) {
            setValue('flow_id', (variableSelected as VariableDef).flow_id)
          }
        }
        setModalOpen(true)
      }
    }
    if (key === 'delete') {
      if (status && status === STUDIO_STATUS.DEV) {
        setModalConfirmOpen(true)
      }
    }
  };

  const menuProps = {
    items,
    onClick
  };

  const columns: TableProps<VariableDef | VariableDefDev>['columns'] = [
    {
      title: 'Variable name',
      dataIndex: 'var_name',
      key: 'var_name'
    },
    {
      title: 'Data type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Init value',
      dataIndex: 'init_value',
      key: 'init_value',
      ellipsis: false, // Disable default ellipsis
      render: (text) => (
        <div
          style={{
            display: '-webkit-box',
            WebkitBoxOrient: 'vertical',
            WebkitLineClamp: 3,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'normal',
            maxHeight: '4.5em',
            wordBreak: 'break-word',
          }}
          title={text}
        >
          {text}
        </div>
      )
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => (
        <Dropdown menu={menuProps} trigger={['click']}>
          <div onClick={(e) => {
            if (!status || (status && status === STUDIO_STATUS.LIVE)) {
              void messageApi.warning("You cannot edit or delete in production mode.")
              return;
            }
            e.preventDefault();
            setVariableSelected(record)
          }}>
            <RiMore2Fill size={20} className="!text-neutural-content dark:!text-dark-neutural-content"/>
          </div>
        </Dropdown>
      ),
    },
  ];

  const {flowInstance} = useFlowInstanceState<FlowInstanceState>((state) => state);
  const {
    variablesDev,
    variables,
    variableSelected,
    setVariableDefDev,
    setVariableSelected
  } = useVariableDefState<VariableDefState>(state => state);
  const {flow, setFlow} = useBuildFlowState<BuildFlowState>(state => state)
  const {status} = useStudioState(state => state)
  const [dataSource, setDataSource] = useState([])
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalConfirmOpen, setModalConfirmOpen] = useState<boolean>(false);
  const [messageApi, contextHolder] = message.useMessage();
  const {theme} = useLayoutState(state => state);

  const {
    control,
    setValue,
    handleSubmit,
    reset,
    formState: {isValid}
  } = useForm({
    resolver: yupResolver(variableFormSchema)
  })

  const resetForm = useCallback(() => {
    reset()
    setValue('type', VARIABLE_TYPE.STRING)
  }, [])

  const handleAddVariable = useCallback(() => {
    resetForm()
    setModalOpen(true);
  }, [])

  const onSubmit = async (formValue) => {
    if (flow.id) {
      const data = {
        ...formValue,
        id: formValue.id === 0 ? NaN : formValue.id,
        flow_dev_id: flow.id,
      }
      setModalOpen(false)
      try {
        if (formValue.id) {
          await variableDefDevApi.updateVariableDev(data)
          const resVariableDev: VariableDefDev[] = await variableDefDevApi.getVariableDev({key_word: ''})
          setVariableDefDev(resVariableDev)
          const resFlowDev: ChatFlowDev = await flowDevApi.getFlowDev(Number(flow.id));
          flowInstance.setNodes(
            JSON.parse(resFlowDev.flow_data)
              ? JSON.parse(resFlowDev.flow_data).nodes
              : resFlowDev.trigger_type === FLOW_TRIGGER_TYPE.INTENT ? [startNodeIntent] : resFlowDev.trigger_type === FLOW_TRIGGER_TYPE.EVENT ? [startNodeEvent] : []
          );
          flowInstance.setEdges(
            JSON.parse(resFlowDev.flow_data) ? JSON.parse(resFlowDev.flow_data).edges : []
          );
          messageApi.success('Update successfully')
        } else {
          await variableDefDevApi.createVariableDev(data)
          const res: VariableDefDev[] = await variableDefDevApi.getVariableDev({key_word: ''})
          setVariableDefDev(res)
          messageApi.success('Create successfully')
        }
      } catch (error) {
        messageApi.error("Create failed, please try again")
      }
      resetForm()
    }
  }

  const handleDeleteVariable = async () => {
    if (variableSelected && status && status === STUDIO_STATUS.DEV) {
      try {
        await variableDefDevApi.deleteVariableDev(variableSelected.id);
        messageApi.success('Delete successfully')
        setModalConfirmOpen(false)
        const res: VariableDefDev[] = await variableDefDevApi.getVariableDev({
          key_word: "",
        });
        setVariableDefDev(res);
      } catch (error) {
        messageApi.error(error.response.data.detail)
        setModalConfirmOpen(false)
      }
    }
  }

  useEffect(() => {
    if (status && status === STUDIO_STATUS.DEV) {
      setDataSource(variablesDev)
    }
    if (status && status === STUDIO_STATUS.LIVE) {
      setDataSource(variables)
    }
  }, [status, variablesDev, variables])

  const handleSearchVariable = (e) => {
    const value = e.target.value
    if (value) {
      const query = value.toLowerCase()
      const filteredVariables = variablesDev.filter(variable =>
        variable.var_name.toLowerCase().includes(query)
      );
      setDataSource(filteredVariables)
    } else {
      setDataSource(variablesDev);
    }
  }

  return (
    <>
      {contextHolder}
      <div className="w-full flex flex-col items-stretch mt-4 text-base-content dark:text-dark-base-content">
        <div className="text-[24px] font-semibold">Variables</div>
        <div className="w-full flex justify-between items-center space-x-3 mt-4">
          <Input className="flex-1 h-10 !rounded-[12px] !mr-3 !bg-base-400 dark:!bg-dark-base-400 !border-primary-border dark:!border-dark-primary-border focus:!border-primary dark:focus:!border-dark-primary focus:!outline-4 focus:!outline-[#d2ceee] dark:focus:!outline-[#343942] placeholder-neutral-content dark:!placeholder-dark-neutral-content !text-base-content dark:!text-dark-base-content"
                 placeholder={'Search variable'}
                 onChange={(e) => handleSearchVariable(e)}
          />
          <button
            type="button"
            disabled={status && status === STUDIO_STATUS.LIVE}
            className="rounded-[12px] h-10 px-6 bg-primary text-primary-content relative flex items-center justify-center button-disabled cursor-pointer"
            onClick={handleAddVariable}
          >
            <span>Add variable</span>
          </button>
        </div>
        <div className="w-full mt-4">
          <Table rowKey="id" className="!rounded-[12px]"
                 columns={columns}
                 dataSource={dataSource}
                 pagination={false}></Table>
        </div>
      </div>

      <Modal
        title={'Add variable'}
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Variable name <span
              style={{color: 'red'}}>*</span></Typography.Text>
            <InputField type={'input'} name={`var_name`} control={control} setValue={setValue}/>
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Data type <span style={{color: 'red'}}>*</span></Typography.Text>
            <SelectField options={VARIABLE_TYPE_LIST} name={'type'} control={control}/>
          </div>

          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Init value</Typography.Text>
            <InputField type={'textarea'} name={`init_value`} control={control} setValue={setValue}/>
          </div>

          <div className="w-full flex justify-end items-center space-x-4 mt-3">
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="submit"
              key="save"
              $bgColor={"#7F75CF"}
              $color={"white"}
              disabled={(status && status === STUDIO_STATUS.LIVE) || (status && status === STUDIO_STATUS.DEV && !isValid)}
            >
              Save
            </StyledButtonModal>
          </div>
        </form>
      </Modal>

      <Modal
        title={'Delete variable'}
        centered
        open={modalConfirmOpen}
        onOk={() => setModalConfirmOpen(false)}
        onCancel={() => setModalConfirmOpen(false)}
        footer={null}
        width={300}
      >
        <div className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Are you sure you want to delete this variable?</Typography.Text>
          </div>

          <div className="w-full flex justify-end items-center space-x-4 mt-3">
            <StyledButtonModal
              type="button"
              key="cancel"
              $bgColor={theme === 'light' ? '#F5F5F7' : '#343942'}
              $color={theme === 'light' ? '#282826' : '#F8FAFF'}
              $outlineColor={theme === 'light' ? '#D2D6DD' : '#292E39'}
              onClick={() => setModalConfirmOpen(false)}
            >
              Cancel
            </StyledButtonModal>
            <StyledButtonModal
              type="button"
              key="save"
              $bgColor={"#E07272"}
              $color={"white"}
              onClick={() => handleDeleteVariable()}
            >
              Yes
            </StyledButtonModal>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default VariableList;
