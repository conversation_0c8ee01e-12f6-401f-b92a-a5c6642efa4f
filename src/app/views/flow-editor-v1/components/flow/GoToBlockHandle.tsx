// @ts-nocheck
import <PERSON>Field from "@flow-editor-v1/components/form/SelectField";
import { StyledButtonModal } from "@flow-editor-v1/components/styled";
import { STUDIO_STATUS } from "@flow-editor-v1/constant";
import { useBuildFlowState, useLayoutState, useStudioState } from "@flow-editor-v1/store";
import { yupResolver } from "@hookform/resolvers/yup";
import { Modal, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Handle, Position, useReactFlow } from "reactflow";
import * as yup from "yup";

const goToBlockSourceSchema = yup.object().shape({
  blockId: yup.string()
})

interface GoToBlockHandleProps {
  sourceHandle: string;
  style: React.CSSProperties;
  data: any;  // Replace with the correct type for your data
  onMouseEnter?: () => void;  // Optional onMouseEnter prop
  onMouseLeave?: () => void;  // Optional onMouseLeave prop
}

const GoToBlockHandle: React.FC<GoToBlockHandleProps> = ({ sourceHandle, style, data, onMouseEnter, onMouseLeave }) => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const {status} = useStudioState(state => state);
  const {getNodes, setViewport  } = useReactFlow();
  const {setDirty} = useBuildFlowState(state => state);
  const {getEdges, setEdges} = useReactFlow();
  const {theme} = useLayoutState(state => state)

  const {
    control,
    setValue,
    handleSubmit,
    reset,
    getValues
  } = useForm({
    resolver: yupResolver(goToBlockSourceSchema)
  })

  const resetForm = () => {
    reset()
  }

  const onSubmit = (formValue) => {
    const indexBlock = data.goToBlockSource.findIndex(v => v.sourceHandle === sourceHandle);
    if (indexBlock >= 0) {
      data.goToBlockSource[indexBlock] = {
        sourceHandle,
        blockId: formValue?.blockId,
        blockName: getNodes().find(v => v.id === formValue?.blockId).data.label
      }
    }
    resetForm();
    setModalOpen(false);

    const newNodeTargetHandle = formValue?.blockId + "_target";
    const nodeAlreadyConnect = getEdges().find(v => v.target === formValue?.blockId && v.targetHandle === newNodeTargetHandle);

    const connection = {
      source: data.id,
      sourceHandle,
      target: formValue?.blockId,
      targetHandle: newNodeTargetHandle,
    }
    const newEdge = {
      ...connection,
      type: "cs",
      id: `${connection.source}-${connection.sourceHandle}-${connection.target}-${connection.targetHandle}`,
      style: {
        opacity: 0
      },
      data: {
        isGoToBlock: true
      }
    };
    setEdges((edges) => {
      if (nodeAlreadyConnect) {
        return edges.filter(edge => edge.sourceHandle !== sourceHandle).concat(newEdge).filter((edge) => edge.id !== nodeAlreadyConnect.id).map((edge) => {
          if (edge.data?.isGoToBlock) {
            edge.style = {
              ...edge.style,
              opacity: 0
            }
          }
          return edge;
        })
      }
      return edges.filter(edge => edge.sourceHandle !== sourceHandle).concat(newEdge).map((edge) => {
        if (edge.data?.isGoToBlock) {
          edge.style = {
            ...edge.style,
            opacity: 0
          }
        }
        return edge;
      })
    })
    setDirty();
    setDirty();
  }

  const goToBlockPosition = (formValue) => {
    const blockId = formValue?.blockId
    if (blockId) {
      const node = getNodes().find((n) => n.id === blockId);
      if (node) {
        const { x, y } = node.position;
        const { zoom } = node.data;
        setViewport({ x: -x*1.5 + 500 , y: -y*1.5 + 300, zoom:1.5 });
        onSubmit(formValue);
      }
      setModalOpen(false);
    }
  };

  const handleDelete = () => {
    data.goToBlockSource = data.goToBlockSource.filter(v => v.sourceHandle !== sourceHandle);

    const connectionId = `${data.id}-${sourceHandle}`;
    setEdges((edges) => edges.filter((edge) => edge.source !== data.id || edge.sourceHandle !== sourceHandle));
    setDirty();
    setModalOpen(false);
  }

  useEffect(() => {
    resetForm();
    data.goToBlockSource.find(v => v.sourceHandle === sourceHandle) && setValue('blockId', data.goToBlockSource.find(v => v.sourceHandle === sourceHandle).blockId);
  }, [JSON.stringify(data)]);

  const handleOpenModal = useCallback(() => {
    if (!data.debug) setModalOpen(true);
  }, [data.debug]);

  return (
    <>
      <Handle
        type="source"
        position={Position.Right}
        isConnectable={false}
        id={sourceHandle}
        style={style}
        onDoubleClick={handleOpenModal}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        className="go-to-block"

      >
        <div style={{
          pointerEvents: 'auto',
          width: '100%',
          textAlign: 'center',
          fontSize: 8
        }}>{data.goToBlockSource.find(v => v.sourceHandle === sourceHandle) && data.goToBlockSource.find(v => v.sourceHandle === sourceHandle).blockId && data.goToBlockSource.find(v => v.sourceHandle === sourceHandle).blockName ? data.goToBlockSource.find(v => v.sourceHandle === sourceHandle).blockName : 'Go to Block'}</div>
      </Handle>

      <Modal
        title="Go to block"
        centered
        open={modalOpen}
        onOk={() => setModalOpen(false)}
        onCancel={() => setModalOpen(false)}
        footer={null}
        width={'50vw'}
        styles={{body: {maxHeight: '85vh', overflowY: 'auto'}}}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col space-y-4 mt-6">
          <div className="flex flex-col space-y-2">
            <Typography.Text className="!text-neutral-content dark:!text-dark-neutral-content">Select node</Typography.Text>
            <SelectField name={'blockId'} disabled={status && status === STUDIO_STATUS.LIVE} control={control}
                         options={[...getNodes().filter(v1 => !v1.id.includes('start') && v1.id !== data.id)].map(v => ({
                           ...v,
                           key: v.id,
                           value: v.id,
                           label: v.data.label
                         }))}
            />
          </div>
          <div className="w-full flex justify-between" style={{marginTop: 24}}>
            <StyledButtonModal type="button"
                               key="delete"
                               $bgColor={theme === "dark" ? "white" : "#ececec"}
                               $color={"black"}
                               onClick={() => goToBlockPosition(getValues())}
                               disabled={!data.goToBlockSource.find(v => v.sourceHandle === sourceHandle)?.blockId}>
              Go to block position
            </StyledButtonModal>
            <div className="flex justify-end items-center space-x-4" >
              <StyledButtonModal type="button"
                                 key="delete"
                                 $bgColor={"red"}
                                 $color={"white"}
                                 onClick={handleDelete}>
                Delete
              </StyledButtonModal>
              <StyledButtonModal type="button"
                                 key="cancel"
                                 $bgColor={theme === "dark" ? "white" : "#ececec"}
                                 $color={"black"}
                                 onClick={() => setModalOpen(false)}>
                Cancel
              </StyledButtonModal>
              <StyledButtonModal
                type="submit"
                key="save"
                $bgColor={"#7F75CF"}
                $color={"white"}
                disabled={status && status === STUDIO_STATUS.LIVE}
              >
                Save
              </StyledButtonModal>
            </div>
          </div>


        </form>
      </Modal>
    </>
  );
}

export default GoToBlockHandle;
