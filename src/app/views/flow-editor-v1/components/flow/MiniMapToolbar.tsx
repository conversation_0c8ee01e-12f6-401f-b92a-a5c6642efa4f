// @ts-nocheck
import { MAX_ZOOM, MIN_ZOOM, OFFSET_ZOOM } from "@flow-editor-v1/constant";
import { FlowInstanceState, LayoutState } from "@flow-editor-v1/model";
import { useFlowInstanceState, useLayoutState } from "@flow-editor-v1/store";
import {
  RiAddLine,
  RiFullscreenLine,
  RiMap2Fill,
  RiSubtractLine,
} from "@remixicon/react";
import { Tooltip } from "antd";
import React, { memo, useState } from "react";
import { MiniMap, useReactFlow, useViewport } from "reactflow";
import styled from "styled-components";

const StyledMiniMapToolBar = styled.div`
  background: ${(props) => (props.$theme === 'dark' ? '#141414' : '#fff')};
  border-radius: 8px;
  bottom: 24px;
  box-shadow: 0 4px 100px 0 rgba(102, 77, 255, 0.3);
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 40px;
  z-index: 4;

  .react-flow__minimap-mask {
    stroke: #007ed4;
    stroke-linejoin: round;
  }
`;

const StyledMiniMap = styled.div`
  .react-flow__panel {
    position: static;
    margin: 0;
    border-radius: 8px;
  }

  .react-flow__minimap svg {
    background: ${(props) => (props.$theme === 'dark' ? '#010314' : '#94a2b8')};
    width: 100%;
    border-radius: 8px 4px 0 0;
  }

  .react-flow__minimap-mask {
    stroke: ${(props) => (props.$theme === 'dark' ? '#010314' : '#94a2b8')};
    stroke-width: 10;
    stroke-linejoin: round;
  }
`;

const StyledControls = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  justify-items: center;
  padding: 8px 12px;
`;

const StyledControlButton = styled.button`
  align-items: center;
  border-radius: 8px;
  color: #747b7f;
  cursor: pointer;
  display: flex;
  font-size: 20px;
  height: 36px;
  justify-content: center;
  width: 36px;

  &:disabled {
    cursor: default;
    opacity: 0.6;
  }

  ${(props) =>
    !props.disabled &&
    `&:hover {
      background: #7241ff;
      color: white;
    }`}
`;

const MiniMapToolBar = () => {

  const [toggle, setToggle] = useState(true);

  const { setViewport } = useReactFlow();
  const { x, y, zoom } = useViewport();
  const {flowInstance} = useFlowInstanceState<FlowInstanceState>((state) => state);
  const {theme} = useLayoutState<LayoutState>(state => state);

  const handleZoomIn = () =>
    setViewport({
      x: x,
      y: y,
      zoom: zoom >= MIN_ZOOM && zoom < MAX_ZOOM ? zoom + OFFSET_ZOOM : zoom,
    });

  const handleZoomOut = () =>
    setViewport({
      x: x,
      y: y,
      zoom: zoom > MIN_ZOOM && zoom <= MAX_ZOOM ? zoom - OFFSET_ZOOM : zoom,
    });

  const handleFitView = () => {
    flowInstance.fitView({
      padding: 0.1,
      includeHiddenNodes: true,
      duration: 800,
    });
  }

  const handleToggleMap = () => setToggle(!toggle);

  return (
    <>
      <StyledMiniMapToolBar $theme={theme}>
        {toggle && (
          <StyledMiniMap $theme={theme}>
            <MiniMap pannable zoomable />
          </StyledMiniMap>
        )}
        <StyledControls>
          <Tooltip
            placement="top"
            title={zoom >= MIN_ZOOM && zoom < MAX_ZOOM ? "Zoom In" : ""}
          >
            <StyledControlButton
              onClick={handleZoomIn}
              disabled={!(zoom >= MIN_ZOOM && zoom < MAX_ZOOM)}
            >
              <RiAddLine />
            </StyledControlButton>
          </Tooltip>

          <Tooltip
            placement="top"
            title={zoom > MIN_ZOOM && zoom <= MAX_ZOOM ? "Zoom Out" : ""}
          >
            <StyledControlButton
              onClick={handleZoomOut}
              disabled={!(zoom > MIN_ZOOM && zoom <= MAX_ZOOM)}
            >
              <RiSubtractLine />
            </StyledControlButton>
          </Tooltip>

          <Tooltip placement="top" title={"Fit View"}>
            <StyledControlButton onClick={handleFitView}>
              <RiFullscreenLine />
            </StyledControlButton>
          </Tooltip>

          <Tooltip
            placement="top"
            title={toggle ? "Hide Minimap" : "Show Minimap"}
          >
            <StyledControlButton onClick={handleToggleMap}>
              <RiMap2Fill />
            </StyledControlButton>
          </Tooltip>
        </StyledControls>
      </StyledMiniMapToolBar>
    </>
  );
};

export default memo(MiniMapToolBar);
