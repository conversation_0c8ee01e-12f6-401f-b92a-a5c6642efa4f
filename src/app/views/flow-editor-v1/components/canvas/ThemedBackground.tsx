// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Background } from 'reactflow';

// Theme-aware background component for ReactFlow
const ThemedBackground = ({ debug, selecting }) => {
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light');

  // Initialize theme and subscribe to theme changes
  useEffect(() => {
    // Get initial theme
    if (window.themeService) {
      const initialTheme = window.themeService.getCurrentThemeValue();
      setCurrentTheme(initialTheme);

      // Subscribe to theme changes
      const subscription = window.themeService.getTheme().subscribe((theme) => {
        setCurrentTheme(theme);
      });

      return () => subscription.unsubscribe();
    } else {
      // Fallback to checking document class if themeService is not available
      const isDarkMode = document.documentElement.classList.contains('dark');
      setCurrentTheme(isDarkMode ? 'dark' : 'light');
    }
  }, []);

  // Use different colors based on theme
  const bgColor = currentTheme === 'light' ? '#94a2b8' : '#3e3e3e';

  return (
    <Background
      color={bgColor}
      gap={16}
      style={{ opacity: (debug || selecting) ? 0.3 : 1 }}
    />
  );
};

export default ThemedBackground;
