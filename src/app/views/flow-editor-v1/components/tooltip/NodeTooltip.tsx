// @ts-nocheck
import { useFlowInstance } from "@flow-editor-v1/hook";
import { useLayoutState } from "@flow-editor-v1/store";
import {
  RiDeleteBinLine,
  RiFileCopy2Line,
  RiFileCopyLine,
  RiInformationLine,
} from "@remixicon/react";
import { Tooltip } from "antd";
import React from "react";
import styled from "styled-components";

const StyledNodeTooltip = styled.div<{ $selected, $color, $theme }>`
  position: absolute;
  top: 0px; /* Điều chỉnh giá trị này để di chuyển NodeTooltip lên phía trên */
  left: 0px;
  transform: translate(-100%, -1%);
  background: ${props => props.$theme === 'dark' ? '#010314' : 'white'};
  box-shadow: 0 0 40px 0 rgba(114, 65, 255, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  border-radius: 8px;
  border: 1px solid #94a2b8;
  padding: 2px;
  ${(props) => props.$selected && `border: 1px solid ${props.$color};`}
  z-index:10
`;

const StyledConvertBtn = styled.button`
  padding: 4px;
  border-radius: 8px;
  cursor: pointer;

  color: ${props => props.$theme === 'dark' ? 'white' : 'black'};

  &:hover {
    background: ${props => props.$theme === 'dark' ? '#1A1C2B' : '#ececec'};
    color: #ffa500;
  }
`;

const StyledDuplicateBtn = styled.button`
  padding: 4px;
  border-radius: 8px;
  cursor: pointer;

  color: ${props => props.$theme === 'dark' ? 'white' : 'black'};

  &:hover {
    background: ${props => props.$theme === 'dark' ? '#1A1C2B' : '#ececec'};
    color: #ffa500;
  }
`;

const StyledDeleteBtn = styled.button`
  padding: 4px;
  border-radius: 8px;
  cursor: pointer;

  color: ${props => props.$theme === 'dark' ? 'white' : 'black'};

  &:hover {
    background: ${props => props.$theme === 'dark' ? '#1A1C2B' : '#ececec'};
    color: red;
  }
`;

const StyledInfoBtn = styled.button`
  padding: 4px;
  border-radius: 8px;
  cursor: pointer;

  color: ${props => props.$theme === 'dark' ? 'white' : 'black'};

  &:hover {
    background: ${props => props.$theme === 'dark' ? '#1A1C2B' : '#ececec'};
    color: #00bfff;
  }
`;

const NodeTooltip = ({ data, selected }) => {
  const { deleteNode, duplicateNode, convertNode } = useFlowInstance();
  const {theme} = useLayoutState((state) => state);
  return (
    <div
      className="cs-node-tooltip">
      <StyledNodeTooltip $selected={selected} $color={data.node_color} $theme={theme}>
        {(data.node_type === "question" || data.node_type === "text") && (
          <Tooltip
            placement="right"
            title={data.node_type === "text" ? "Convert to question" : "Convert to text"}
          >
            <StyledConvertBtn
              $theme={theme}
              onClick={() => {
                convertNode(data.id);
              }}
            >
              <RiFileCopy2Line size={16} />
            </StyledConvertBtn>
          </Tooltip>
        )}

        <Tooltip placement="right" title={"Duplicate"}>
          <StyledDuplicateBtn
            $theme={theme}
            onClick={() => {
              duplicateNode(data.id);
            }}
          >
            <RiFileCopyLine size={16} />
          </StyledDuplicateBtn>
        </Tooltip>

        <Tooltip placement="right" title={"Delete"}>
          <StyledDeleteBtn
            $theme={theme}
            onClick={() => {
              deleteNode(data.id);
            }}
          >
            <RiDeleteBinLine size={16} />
          </StyledDeleteBtn>
        </Tooltip>

        <Tooltip placement="right" title={"Info"}>
          <StyledInfoBtn
            $theme={theme}
            onClick={() => {
              // setInfoDialogProps({ data });
              // setShowInfoDialog(true);
            }}
          >
            <RiInformationLine size={16} />
          </StyledInfoBtn>
        </Tooltip>
      </StyledNodeTooltip>
    </div>
  );
};

export default NodeTooltip;
