// @ts-nocheck
import { VariableDefState } from '@flow-editor-v1/model';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export const useVariableDefState = create<VariableDefState>()(
  devtools((set) => ({
    variables: [],
    variablesDev: [],
    variableSelected: null,
    setVariableDef: (variables) => set(() => ({ variables })),
    setVariableDefDev: (variablesDev) => set(() => ({ variablesDev })),
    setVariableSelected: (variableSelected) =>
      set(() => ({ variableSelected })),
  }))
);
