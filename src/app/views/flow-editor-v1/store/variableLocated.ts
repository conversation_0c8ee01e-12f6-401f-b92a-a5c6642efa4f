import { VariableLocatedState } from '@flow-editor-v1/model';
import { LocalStorageKey } from '@flow-editor-v1/utils/flow';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { VARIABLE_POSITION_TYPE } from '../constant/variable';

export const useVariableLocatedState = create<VariableLocatedState>()(
  devtools((set) => ({
    variableLocations: [],
    setInitVariableLocations: (locations) =>
      set(() => {
        localStorage.setItem(
          LocalStorageKey.variableLocation(),
          JSON.stringify(locations)
        );
        return { variableLocations: locations };
      }),

    setVariableLocations: (locations, type) =>
      set((state) => {
        let updatedLocations = state.variableLocations;

        if (type === VARIABLE_POSITION_TYPE.FLOW) {
          const positionTypeNotFlow = updatedLocations.filter(
            (locationState) =>
              locationState.position_type !== VARIABLE_POSITION_TYPE.FLOW
          );

          const locationsMapping = [...locations].map((v) => ({
            flow_dev_id: v.flow_dev_id,
            node_id: v.node_id,
          }));

          updatedLocations = updatedLocations.filter(
            (locationState) =>
              locationState.position_type === VARIABLE_POSITION_TYPE.FLOW &&
              !locationsMapping.some(
                (mappedLocation) =>
                  mappedLocation.flow_dev_id === locationState.flow_dev_id &&
                  mappedLocation.node_id === locationState.node_id
              )
          );

          updatedLocations = Array.from(
            new Map(
              [...positionTypeNotFlow, ...updatedLocations, ...locations].map(
                (item) => [
                  `${item.var_def_dev_id || ''}|${
                    item.var_def_dev_name || ''
                  }|${item.node_id}|${item.flow_dev_id}`,
                  item,
                ]
              )
            ).values()
          );
        }

        localStorage.setItem(
          LocalStorageKey.variableLocation(),
          JSON.stringify(updatedLocations)
        );
        return { variableLocations: updatedLocations };
      }),

    setVariableLocation: (location) =>
      set((state) => {
        let updatedLocations = state.variableLocations;

        if (location.position_type === VARIABLE_POSITION_TYPE.FLOW) {
          const positionTypeNotFlow = updatedLocations.filter(
            (locationState) =>
              locationState.position_type !== VARIABLE_POSITION_TYPE.FLOW
          );

          updatedLocations = updatedLocations.filter(
            (locationState) =>
              locationState.position_type === VARIABLE_POSITION_TYPE.FLOW &&
              !(
                location.flow_dev_id === locationState.flow_dev_id &&
                location.node_id === locationState.node_id
              )
          );

          updatedLocations = Array.from(
            new Map(
              [...positionTypeNotFlow, ...updatedLocations, location].map(
                (item) => [
                  `${item.var_def_dev_id || ''}|${
                    item.var_def_dev_name || ''
                  }|${item.node_id}|${item.flow_dev_id}`,
                  item,
                ]
              )
            ).values()
          );
        }

        localStorage.setItem(
          LocalStorageKey.variableLocation(),
          JSON.stringify(updatedLocations)
        );
        return { variableLocations: updatedLocations };
      }),
  }))
);
