export const COMPARISONS_TYPE = {
  EQUAL_TO: 'equal to',
  GREATER_THAN: 'greater than',
  GREATER_THAN_OR_EQUAL_TO: 'greater than or equal to',
  LESS_THAN: 'less than',
  LESS_THAN_OR_EQUAL_TO: 'less than or equal to',
  NOT_EQUAL_TO: 'not equal to',
  // IS_TRUE: 'is True',
  // IS_FALSE: 'is False'
}

export const COMPARISONS_TYPE_LIST = [
  {
    key: COMPARISONS_TYPE.EQUAL_TO,
    value: COMPARISONS_TYPE.EQUAL_TO,
    label: COMPARISONS_TYPE.EQUAL_TO,
    def: "=="
  },
  {
    key: COMPARISONS_TYPE.GREATER_THAN,
    value: COMPARISONS_TYPE.GREATER_THAN,
    label: COMPARISONS_TYPE.GREATER_THAN,
    def: ">"
  },
  {
    key: COMPARISONS_TYPE.GREATER_THAN_OR_EQUAL_TO,
    value: COMPARISONS_TYPE.GREATER_THAN_OR_EQUAL_TO,
    label: COMPARISONS_TYPE.GREATER_THAN_OR_EQUAL_TO,
    def: ">="
  },
  {
    key: COMPARISONS_TYPE.LESS_THAN,
    value: COMPARISONS_TYPE.LESS_THAN,
    label: COMPARISONS_TYPE.LESS_THAN,
    def: "<"
  },
  {
    key: COMPARISONS_TYPE.LESS_THAN_OR_EQUAL_TO,
    value: COMPARISONS_TYPE.LESS_THAN_OR_EQUAL_TO,
    label: COMPARISONS_TYPE.LESS_THAN_OR_EQUAL_TO,
    def: "<="
  },
  {
    key: COMPARISONS_TYPE.NOT_EQUAL_TO,
    value: COMPARISONS_TYPE.NOT_EQUAL_TO,
    label: COMPARISONS_TYPE.NOT_EQUAL_TO,
    def: "!="
  },
  // {
  //   key: COMPARISONS_TYPE.IS_TRUE,
  //   value: COMPARISONS_TYPE.IS_TRUE,
  //   label: COMPARISONS_TYPE.IS_TRUE,
  //   def: "is True"
  // },
  // {
  //   key: COMPARISONS_TYPE.IS_FALSE,
  //   value: COMPARISONS_TYPE.IS_FALSE,
  //   label: COMPARISONS_TYPE.IS_FALSE,
  //   def: "is False"
  // }
]
