export const VA<PERSON>ABLE_TYPE = {
  STRING: "STRING",
  NUMBER: "NUMBER",
  ARRAY: "ARRAY",
  OBJECT: "OBJECT",
  BOOLEAN: "BOOLEAN",
}

export const VARIABLE_TYPE_LIST = [
  {
    label: 'String',
    key: VARIABLE_TYPE.STRING,
    value: VARIABLE_TYPE.STRING
  },
  {
    label: 'Number',
    key: VARIABLE_TYPE.NUMBER,
    value: VARIABLE_TYPE.NUMBER
  },
  {
    label: 'Array',
    key: VARIABLE_TYPE.ARRAY,
    value: VARIABLE_TYPE.ARRAY
  },
  {
    label: 'Object',
    key: VARIABLE_TYPE.OBJECT,
    value: VARIABLE_TYPE.OBJECT
  },
  {
    label: 'Boolean',
    key: VARIABLE_TYPE.BOOLEAN,
    value: VARIABLE_TYPE.BOOLEAN
  }
]

export enum VARIABLE_POSITION_TYPE {
  FLOW = "FLOW",
  API = "API",
  FUNCTION = "FUNCTION"
}
