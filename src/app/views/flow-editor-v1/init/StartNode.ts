import { NodeFlow } from '@flow-editor-v1/model';

export const startIntent: NodeFlow = {
  id: 'start_0',
  position: { x: 0, y: 0 },
  type: 'start',
  data: {
    node_type: 'start',
    node_color: '#7241ff',
    label: 'Start',
    name: 'start',
    type: 'start',
    icon: 'RiUserVoiceFill',
    description: 'Start this flow',
    intent: null,
  },
};

export const startNodeIntent = {
  id: 'start_0',
  position: { x: 50, y: 250 },
  type: 'start',
  data: startIntent.data,
};

export const startEvent: NodeFlow = {
  id: 'start_0',
  position: { x: 0, y: 0 },
  type: 'start',
  data: {
    node_type: 'start',
    node_color: '#7241ff',
    label: 'Start',
    name: 'start',
    type: 'start',
    icon: 'RiUserVoiceFill',
    description: 'Start this flow',
    event: null,
  },
};

export const startNodeEvent = {
  id: 'start_0',
  position: { x: 50, y: 250 },
  type: 'start',
  data: startEvent.data,
};
