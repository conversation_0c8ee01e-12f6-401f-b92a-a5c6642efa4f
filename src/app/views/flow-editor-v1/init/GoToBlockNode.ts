export const goToBlockNode = {
  name: "Go to Block",
  description: null,
  icon: "RiGitRepositoryCommitsFill",
  type: "Others",
  id: null,
  position: { x: 0, y: 0 },
  parentId: null,
  extent: 'parent',
  draggable: false,
  status: 'Supporting',
  data: JSON.stringify({
    node_type: 'goToBlock',
    node_color: 'white',
    label: "Go to Block",
    name: "gotoblock",
    type: 'Others',
    icon: 'RiGitRepositoryCommitsFill',
    description: null,
    selected: false,
    debug: false,
    parentId: null,
    extent: 'parent',
    draggable: false
  })
}
