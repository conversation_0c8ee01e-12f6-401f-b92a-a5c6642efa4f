import {Component, inject, signal} from '@angular/core';
import {SvgIconComponent} from '@shared/components';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField, DxInput,
  DxLabel,
  DxLoadingButton,
  DxSnackBar
} from '@dx-ui/ui';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {IFolder} from '@shared/models';
import {FileFolderService} from '@shared/services';

@Component({
  selector: 'app-add-or-edit-folder',
  imports: [
    SvgIconComponent,
    DxButton,
    DxLoadingButton,
    ReactiveFormsModule,
    DxError,
    DxLabel,
    DxFormField,
    DxInput
  ],
  templateUrl: './add-or-edit-folder.component.html',
  styleUrl: './add-or-edit-folder.component.css'
})
export class AddOrEditFolderComponent {
  isCreatingFolder = signal(false);

  dialogRef = inject(DxDialogRef<AddOrEditFolderComponent>);
  formGroup: FormGroup = inject(FormBuilder).group({
    id: [null],
    ai_id: [''],
    parent_id: [null],
    name: [null, [Validators.required]],
  });
  data: {
    isCreate: boolean;
    folder?: IFolder;
    parent_id: number;
  } = inject(DIALOG_DATA);
  snackBar = inject(DxSnackBar);
  private fileFolderService = inject(FileFolderService);

  ngOnInit() {
    if (!this.data.isCreate && this.data.folder) {
      this.formGroup.patchValue(this.data.folder);
    } else {
      this.formGroup.patchValue({
        parent_id: this.data.parent_id,
      });
    }
  }
  onKeyDownFolderDialog(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.saveCreatingFolder();
    }
  }
  saveCreatingFolder() {
    if (this.formGroup.valid) {
      this.isCreatingFolder.set(true);
      const formValue = this.formGroup.value;

      if (formValue.id) {
        // Update existing folder
        this.fileFolderService.updateFolder(formValue).subscribe({
          next: (res) => {
            this.isCreatingFolder.set(false);
            this.dialogRef.close(true);
            this.showSnackBar('Folder updated successfully', 'success');
          },
          error: (error) => {
            this.isCreatingFolder.set(false);
            console.error('Error updating folder:', error);
          }
        });
      } else {
        // Create new folder
        this.fileFolderService.createFolder(formValue).subscribe({
          next: (res) => {
            this.isCreatingFolder.set(false);
            this.dialogRef.close(true);
            this.showSnackBar('Folder created successfully', 'success');
          },
          error: (error) => {
            this.isCreatingFolder.set(false);
            console.error('Error creating folder:', error);
          }
        });
      }
    }
  }
  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
