import {Component, inject, signal} from '@angular/core';
import {FormsModule} from "@angular/forms";
import {SvgIconComponent} from '@shared/components';
import {
  DIALOG_DATA,
  DxButton, DxDialog,
  DxDialogRef,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxSnackBar
} from '@dx-ui/ui';
import {IFile} from '@shared/models';
import {KnowledgeBaseService} from '@shared/services';
import {DatePipe} from '@angular/common';
import {
  ViewOrEditContentFileComponent
} from '@views/knowledge-base/pages/knowledge-base-v2/view-or-edit-content-file/view-or-edit-content-file.component';
import {ClipboardService} from 'ngx-clipboard';

@Component({
  selector: 'app-add-or-edit-file',
  imports: [
    FormsModule,
    SvgIconComponent,
    DxButton,
    DxLoadingButton,
    DxInput,
    Dx<PERSON><PERSON><PERSON>,
    DxForm<PERSON>ield,
    DatePipe,
  ],
  templateUrl: './add-or-edit-file.component.html',
  styleUrl: './add-or-edit-file.component.css'
})
export class AddOrEditFileComponent {
  isEditing = signal(false);
  fileName = signal('');
  fileRename: any = {
    file_id: null,
    old_name: '',
  };
  dialogRef = inject(DxDialogRef<AddOrEditFileComponent>);
  data: {
    file: IFile;
    isView: boolean;
  }  = inject(DIALOG_DATA);
  dialog = inject(DxDialog);
  private knowledgeBaseService = inject(KnowledgeBaseService);
  private snackBar = inject(DxSnackBar);
  private clipboardService = inject(ClipboardService);

  ngOnInit() {
    this.fileRename.file_id = this.data.file.id;
    this.fileRename.old_name = this.data.file.name;
    this.fileName.set(this.data.file.name);
  }
  saveRenameFile() {
    if (this.isEditing()) {
      this.isEditing.set(false);
    }
    if (this.fileRename.old_name === this.fileName()) {
      this.dialogRef.close(true);
      return;
    }
    const body = {
      file_id: this.fileRename.file_id,
      new_name: this.fileName(),
    };
    this.knowledgeBaseService.renameFile(body).subscribe({
       next: () => {
         this.isEditing.set(false);
         this.dialogRef.close(this.fileRename.file_id);
         this.showSnackBar('Rename successfully', 'success');
      },
      error: () => {
        this.isEditing.set(false);
        this.showSnackBar('Rename fail', 'error');
      }
    });
  }
  viewContentFile(file: IFile) {
    this.knowledgeBaseService.getContentFile(file.id!).subscribe({
      next: (res) => {
        // this.fileContent.set(res.content || '');
        this.dialog.open(ViewOrEditContentFileComponent, {
          data: { file: file, content: res.data},
          width: '80vw',
          height: '80vh',
          minWidth: '340px',
        });
      },
      error: (error) => {
        console.error('Error loading file content:', error);
        // this.fileContent.set('Error loading file content');
        this.dialog.open(ViewOrEditContentFileComponent, {
          data: { file: file },
          width: '80vw',
          height: '80vh',
          minWidth: '340px',
        });
      }
    });
  }

  showSnackBar(message: string, type: 'success' | 'error' | 'info') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }

  copyToClipboard(s: string) {
    if (!s || s.trim().length === 0) return;
    this.clipboardService.copy(s);
    this.showSnackBar('Copied!', 'success');
  }
}
