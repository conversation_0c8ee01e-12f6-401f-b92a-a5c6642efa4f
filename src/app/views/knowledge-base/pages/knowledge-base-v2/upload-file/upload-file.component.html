<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Upload file
      <p class="text-sm opacity-70">(.pdf, .csv, .txt, .md, .docx)</p>
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>
  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <form [formGroup]="formGroup" enctype="multipart/form-data" class="px-6 pt-6 pb-[3px] flex flex-col space-x-4">
      <div class="flex items-center mb-4">
        <button type="button"
                class="h-[40px] min-w-[120px] mr-3 bg-blue-500 hover:bg-blue-600 px-3 rounded-lg cursor-pointer border-0 text-white"
                (click)="fileUploadRef.click()">
          Select file
        </button>
        <input #fileUploadRef type="file" class="hidden"
               accept=".csv,.pdf,.txt,.md,.docx"
               (change)="uploadFile($event)"/>
        <label class="ml-2 text-sm text-neutral-content dark:text-dark-neutral-content">
          {{ documentName() ? documentName() : "No file selected" }}
        </label>
      </div>

      @if (checkFileCsv(documentName()) && fileUpload()) {
        <dx-form-field>
          <dx-label>Metadata Columns (CSV only)</dx-label>
          <textarea dxInput
                    autoResize
                    [ngModel]="metadata()"
                    (ngModelChange)="metadata.set($event)"
                    rows="3"
                    placeholder="Enter column names separated by commas (optional)"></textarea>
        </dx-form-field>
        <p class="text-sm text-neutral-content dark:text-dark-neutral-content mt-1">
          Specify which columns contain metadata for better processing
        </p>
      }
    </form>
  </div>


  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Cancel</button>
    <button
      dxLoadingButton="filled"
      [loading]="isImportingFile()"
      [disabled]="!fileUpload() || isImportingFile()"
      (click)="saveFileUpload()"
    >
      Upload{{ isImportingFile() ? 'ing...' : '' }}
    </button>
  </div>
</div>
