/* Host component styling */
:host {
  display: block;
  height: 100%;
  overflow: hidden;
}

/* Virtual scroll viewport styling */
cdk-virtual-scroll-viewport {
  display: block;
  position: relative;
  overflow: auto !important;
  contain: none !important;
  transform: translateZ(0);
  will-change: scroll-position;
  -webkit-overflow-scrolling: touch;
  height: 100% !important;
  width: 100% !important;
  flex: 1 1 auto !important;
  min-height: 200px;
}

.cdk-virtual-scroll-content-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  contain: none !important;
  width: 100% !important;
  min-height: 100px;
}

.cdk-virtual-scroll-spacer {
  position: absolute;
  top: 0;
  left: 0;
  height: 1px;
  width: 1px;
  transform-origin: 0 0;
}

/* List layout styling */
.list-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 120px 60px;
  gap: 16px;
  padding:  0 24px 12px 0;
  background-color: transparent;
  border-bottom: 1px solid var(--color-primary-border);
  position: sticky;
  top: 0;
  z-index: 10;
  font-weight: 600 !important;
  font-size: 14px !important;
}
.dark .list-header {
  background-color: var(--color-dark-background);
  border-bottom-color: var(--color-dark-primary-border);
}

.list-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 120px 60px;
  gap: 16px;
  padding:  0 24px 0 0;
  min-height: 50px;
  align-items: center;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--color-primary-border);
}

.dark .list-row {
  border-bottom-color: var(--color-dark-primary-border);
}

/*.list-row:hover {
  background-color: var(--color-light-hover);
  transform: translateX(2px);
}

.dark .list-row:hover {
  background-color: var(--color-dark-hover);
}

.list-row.selected {
  background-color: var(--color-light-primary);
}*/

.list-cell {
  display: flex;
  align-items: center;
  overflow: hidden;
  font-weight: 400;
}

.list-header .list-cell {
  font-weight: 600;
  font-size: 14px;
}

.list-cell.name {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.list-cell.name p {
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

.list-cell.date {
  justify-content: start;
  font-size: 14px;
  font-weight: 400;
}


.list-cell.status {
  justify-content: start;
}

.list-cell.actions {
  justify-content: center;
}

/* Status chip styling */
.status-chip {
  font-size: 12px !important;
  font-weight: 500 !important;
  height: 24px !important;
  border-radius: 12px !important;
  padding: 0 8px !important;
}

.status-chip.green {
  background-color: rgba(16, 185, 129, 0.1) !important;
  color: #10b981 !important;
}

.status-chip.blue {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.status-chip.red {
  background-color: rgba(239, 68, 68, 0.1) !important;
  color: #ef4444 !important;
}

.status-chip.yellow {
  background-color: rgba(245, 158, 11, 0.1) !important;
  color: #f59e0b !important;
}

/* Menu trigger styling */
.menu-trigger {
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.menu-trigger:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .menu-trigger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Folder and file specific styling */
.folder-icon,
.folder-name {
  cursor: pointer;
}

.folder-icon:hover,
.folder-name:hover {
  opacity: 0.8;
}

/* Sort button styling */
.sort-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;
  cursor: pointer;
}

.sort-button:hover {
  background-color: var(--color-light-hover);
}

.dark .sort-button:hover {
  background-color: var(--color-dark-hover);
}

/* File icon styling */
.file-icon {
  flex-shrink: 0;
  margin-right: 12px;
}

/* Loading spinner in status */
::ng-deep .status-chip mat-spinner {
  width: 16px !important;
  height: 16px !important;
}

::ng-deep .status-chip mat-spinner svg {
  width: 16px !important;
  height: 16px !important;
}

/* Responsive design */
@media (max-width: 1024px) {

  .list-header,
  .list-row {
    grid-template-columns: 1fr 0.5fr 0.5fr 100px 50px 50px;
    gap: 12px;
  }

  .list-cell.date:first-of-type {
    display: none;
  }
}

@media (max-width: 768px) {

  .list-header,
  .list-row {
    grid-template-columns: 1fr 0.5fr 40px;
    gap: 8px;
    padding: 8px 12px;
  }

  .list-cell.date {
    display: none;
  }
}

@media (max-width: 640px) {

  .list-header,
  .list-row {
    grid-template-columns: 1fr 0.4fr 24px;
    gap: 8px;
    padding: 8px 12px;
  }

  .list-cell.date {
    display: none;
  }
}

/* Smooth transitions */
* {
  transition: all 0.2s ease;
}

/* Focus states for accessibility */
.list-row:focus {
  outline: 2px solid var(--color-light-primary);
  outline-offset: 2px;
}

.dark .list-row:focus {
  outline-color: var(--color-dark-primary);
}

/* Selection styling */
.list-row.folder-selected {
  background-color: rgba(114, 65, 255, 0.08) !important;
  border: 1px solid var(--color-light-primary) !important;
  border-left: 3px solid var(--color-light-primary) !important;
  box-shadow: 0 0 0 1px rgba(114, 65, 255, 0.1) !important;
}

.dark .list-row.folder-selected {
  background-color: rgba(114, 65, 255, 0.15) !important;
  border: 1px solid var(--color-dark-primary) !important;
  border-left: 3px solid var(--color-dark-primary) !important;
  box-shadow: 0 0 0 1px rgba(114, 65, 255, 0.2) !important;
}

.list-row.file-selected {
  background-color: rgba(114, 65, 255, 0.08) !important;
  border: 1px solid var(--color-light-primary) !important;
  border-left: 3px solid var(--color-light-primary) !important;
  box-shadow: 0 0 0 1px rgba(114, 65, 255, 0.1) !important;
}

.dark .list-row.file-selected {
  background-color: rgba(114, 65, 255, 0.15) !important;
  border: 1px solid var(--color-dark-primary) !important;
  border-left: 3px solid var(--color-dark-primary) !important;
  box-shadow: 0 0 0 1px rgba(114, 65, 255, 0.2) !important;
}

/* Header sort indicators */
.list-header .sort-indicator {
  margin-left: 4px;
  opacity: 0.7;
}

.list-header .sort-indicator:hover {
  opacity: 1;
}
