import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'abbreviation',
})
export class AbbreviationPipe implements PipeTransform {
  transform(value: string): string {
    if (!value) return '';
    const words = value.split(/[\s_]+/);
    const abbreviation = words
      .filter((w) => w.length > 0)
      .slice(0, 2)
      .map((word) => word[0].toUpperCase())
      .join('');

    return abbreviation;
  }
}
