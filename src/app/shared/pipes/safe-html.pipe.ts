import { Pipe, PipeTransform, inject } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'safeHtml',
  standalone: true,
})
export class SafeHtmlPipe implements PipeTransform {
  private sanitizer = inject(DomSanitizer);

  transform(value: any): SafeHtml {
    if (!value || typeof value !== 'string') return '';
    let processed = value
      .trim()
      // Tiêu đề (h1 đến h6, xử lý \n#)
      .replace(/#+/g, '')
      // Xử lý xuống dòng
      .replace(/\\n/g, '<br>') // Thay \n có dấu gạch chéo
      .replace(/\n/g, '<br>') // Thay \n thông thường

      // Bold (**text**)
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

      // Italic (*text*)
      .replace(/\*(.*?)\*/g, '<em>$1</em>')

      // Inline code (`code`)
      .replace(/`([^`]+)`/g, '<code>$1</code>')

      // Code block (```code```)
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')

      // Images (![alt text](url))
      .replace(
        /!\[([^\]]*)\]\((https?:\/\/[^\s)]+)\)/g,
        '<img src="$2" alt="$1" id="imgCheck" class="rounded-md zoom" style="max-width: 100%; height: auto;" />'
      )

      // Links ([text](url))
      .replace(
        /\[([^\]]+)]\((https?:\/\/[^\s)]+)\)/g,
        '<a href="$2" target="_blank">$1</a>'
      )

      // Danh sách có thứ tự (1. text)
      .replace(/\n\d+\.\s+(.*)/g, '\n<li>$1</li>')

      // Danh sách không thứ tự (- text hoặc * text)
      .replace(/\n[-*]\s+(.*)/g, '\n<li>$1</li>');

    const tableRegex = /^\|(.+)\|$/g; // Matches Markdown table rows
    const lines = processed.split('<br>'); // Split the content by <br> (line breaks)
    let tableHtml = '';
    let isTable = false;
    let tableRows = [];
    let finalProcessed = '';

    // Iterate through lines to detect and process table structure
    for (const line of lines) {
      if (line.match(tableRegex)) {
        if (line.match(/^[-|]{3,}$/)) {
          continue;
        }
        // If a table row is detected, add it to the current table rows
        if (!isTable) {
          isTable = true;
          tableHtml = '<table border="1" style="border-collapse: collapse;">'; // Add border and border-collapse for styling
        }

        // Process each cell in the row (excluding the first and last column which are empty)
        const cells = line
          .split('|')
          .slice(1, -1)
          .map(
            (cell) =>
              `<td style="padding: 8px; border: 1px solid #ddd; min-width: 100px;">${cell.trim()}</td>`
          )
          .join('');
        tableRows.push(cells);
      } else {
        if (isTable) {
          // If we exit the table, close the table body and finish the table
          tableHtml +=
            '<thead><tr>' +
            tableRows.map((row) => `<tr>${row}</tr>`).join('') +
            '</tr></thead><tbody>';
          tableHtml += '</tbody></table>';
          finalProcessed += tableHtml; // Add the current table to the final processed content
          tableHtml = ''; // Reset tableHtml for the next table
          isTable = false;
          tableRows = [];
        } else {
          // Add regular content to finalProcessed
          finalProcessed += line + '<br>';
        }
      }
    }

    // Check if the table was left unclosed and finalize it
    if (isTable) {
      tableHtml += tableRows.map((row) => `<tr>${row}</tr>`).join('');
      tableHtml += '</tbody></table>';
      finalProcessed += tableHtml; // Add the last table to the final processed content
    }

    // Replace the table Markdown with the actual HTML table
    processed = finalProcessed;

    return this.sanitizer.bypassSecurityTrustHtml(processed);
  }
}
