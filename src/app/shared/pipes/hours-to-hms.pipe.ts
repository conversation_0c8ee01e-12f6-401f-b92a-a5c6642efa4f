import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'hoursToHMS',
  standalone: true
})
export class HoursToHMSPipe implements PipeTransform {
  transform(hours: number | null | undefined): string {
    if (hours === null || hours === undefined) {
      return 'N/A';
    }

    if (hours === 0) {
      return '0 seconds';
    }

    // Convert hours to total seconds for precise calculation
    const totalSeconds = Math.floor(hours * 3600);

    // Calculate years, months, weeks, days, hours, minutes, seconds
    const years = Math.floor(totalSeconds / (365 * 24 * 3600));
    let remaining = totalSeconds % (365 * 24 * 3600);

    const months = Math.floor(remaining / (30 * 24 * 3600)); // Approximate 30 days per month
    remaining = remaining % (30 * 24 * 3600);

    const weeks = Math.floor(remaining / (7 * 24 * 3600));
    remaining = remaining % (7 * 24 * 3600);

    const days = Math.floor(remaining / (24 * 3600));
    remaining = remaining % (24 * 3600);

    const h = Math.floor(remaining / 3600);
    remaining = remaining % 3600;

    const minutes = Math.floor(remaining / 60);
    const seconds = remaining % 60;

    // Create array of time units with their values and labels
    const timeUnits = [
      { value: years, singular: 'year', plural: 'years' },
      { value: months, singular: 'month', plural: 'months' },
      { value: weeks, singular: 'week', plural: 'weeks' },
      { value: days, singular: 'day', plural: 'days' },
      { value: h, singular: 'hour', plural: 'hours' },
      { value: minutes, singular: 'minute', plural: 'minutes' },
      { value: seconds, singular: 'second', plural: 'seconds' }
    ];

    // Filter out zero values and take only the first 2 significant units
    const significantUnits = timeUnits
      .filter(unit => unit.value > 0)
      .slice(0, 2);

    // Build display string
    if (significantUnits.length === 0) {
      return '0 seconds';
    }

    return significantUnits
      .map(unit => {
        const label = unit.value === 1 ? unit.singular : unit.plural;
        return `${unit.value} ${label}`;
      })
      .join(' ');
  }
}
