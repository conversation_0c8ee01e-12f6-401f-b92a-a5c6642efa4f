import { Directive, ElementRef, HostListener, Input, inject } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[appTrimString]',
  standalone: true
})
export class TrimStringDirective {
  private el = inject(ElementRef);
  private control = inject(NgControl, { optional: true });

  @Input() trim: 'blur' | 'change' = 'blur';

  @HostListener('blur')
  onBlur() {
    if (this.trim === 'blur') {
      this.trimValue();
    }
  }

  @HostListener('change')
  onChange() {
    if (this.trim === 'change') {
      this.trimValue();
    }
  }

  private trimValue() {
    if (this.control && this.control.value) {
      const value = this.control.value.toString().trim();
      this.control.control?.setValue(value);
    } else {
      // If no NgControl is available, try to get the value directly from the element
      const inputElement = this.el.nativeElement as HTMLInputElement;
      if (inputElement && inputElement.value) {
        inputElement.value = inputElement.value.trim();
      }
    }
  }
}
