import { Directive, Input, OnInit, TemplateRef, ViewContainerRef, inject } from '@angular/core';

@Directive({
    selector: '[hasPermission]',
    standalone: true
})
export class HasPermissionDirective implements OnInit {
  private templateRef = inject<TemplateRef<any>>(TemplateRef);
  private viewContainer = inject(ViewContainerRef);

  private permissions: number = 0;
  private user_permissions: number = 0;
  private isHidden = true;

  ngOnInit(): void {
    this.updateView();
  }

  @Input()
  set hasPermission(val: { permissions: number, user_permissions: number}) {
    this.permissions = val.permissions
    this.user_permissions = val.user_permissions
    this.updateView();
  }

  private updateView(): void {
    if (this.checkPermission()) {
      if (this.isHidden) {
        this.viewContainer.createEmbeddedView(this.templateRef);
        this.isHidden = false;
      }
    } else {
      if (!this.isHidden) {
        this.viewContainer.clear();
        this.isHidden = true;
      }
    }
  }

  private checkPermission(): boolean {
    let hasPermission = false;

    if (this.permissions && this.user_permissions) {
      hasPermission = this.user_permissions >= this.permissions
    }
    return hasPermission;
  }
}
