import { Directive, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

@Directive({
  selector: '[appFormControlAccessor]',
  standalone: true
})
export class FormControlAccessorDirective implements OnInit {
  @Input('appFormControlAccessor') formControlName: string = '';
  @Input() formGroup: FormGroup | null = null;

  private _control: FormControl | null = null;

  ngOnInit() {
    this.initializeControl();
  }

  private initializeControl() {
    try {
      if (!this.formGroup || !this.formControlName) {
        return;
      }

      // Convert formControlName to string if it's not already
      const controlName = typeof this.formControlName === 'string'
        ? this.formControlName
        : String(this.formControlName);

      // Get the control
      this._control = this.formGroup.get(controlName) as FormControl;
    } catch (error) {
      console.error(error);
    }
  }

  get control(): FormControl | null {
    return this._control;
  }

  get isInvalid(): boolean {
    try {
      if (!this._control) return false;
      return this._control.invalid && (this._control.touched || this._control.dirty) || false;
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  get isValid(): boolean {
    try {
      if (!this._control) return false;
      return this._control.valid && (this._control.touched || this._control.dirty) || false;
    } catch (error) {
      console.error(error);
      return false;
    }
  }
}
