import { Directive, ElementRef, HostListener, inject } from '@angular/core';

@Directive({
  selector: '[appNonNegative]',
  standalone: true
})
export class NonNegativeDirective {
  private el = inject(ElementRef);


  @HostListener('input', ['$event'])
  onInput(event: Event) {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    
    // Allow empty input
    if (value === '') {
      return;
    }
    
    // Parse the value as a number
    const numValue = parseFloat(value);
    
    // If the value is negative, set it to 0
    if (numValue < 0) {
      input.value = '0';
    }
  }
}
