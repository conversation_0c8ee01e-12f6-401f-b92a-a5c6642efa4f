import { Directive, ElementRef, HostListener, inject } from '@angular/core';

@Directive({
  selector: 'textarea[autoResize]'
})
export class AutosizeDirective {
  private element = inject<ElementRef<HTMLTextAreaElement>>(ElementRef);

  // XÓA hoàn toàn @Input() set ngModel(...)!!!

  @HostListener('input')
  onInput(): void {
    this.resize();
  }

  ngAfterViewInit(): void {
    setTimeout(() => this.resize());
  }

  private resize() {
    const ta = this.element.nativeElement;
    ta.style.height = 'auto';
    ta.style.height = `${ta.scrollHeight}px`;
  }
}
