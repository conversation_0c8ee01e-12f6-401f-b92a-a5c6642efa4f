import { Overlay } from '@angular/cdk/overlay';
import { Directive, ElementRef, HostListener, Input, ViewContainerRef, inject } from '@angular/core';
import { MatMenuPanel, MatMenuTrigger } from '@angular/material/menu';
import { fromEvent, merge } from 'rxjs';

@Directive({
  selector: `[matContextMenuTriggerFor]`,
  host: {
    class: 'mat-menu-trigger',
  },
  exportAs: 'matContextMenuTrigger',
})
export class MatContextMenuTrigger extends MatMenuTrigger {
  @Input('matContextMenuTriggerFor')
  get menu_again(): MatMenuPanel | null {
    return this.menu;
  }
  set menu_again(menu: MatMenuPanel | null) {
    this.menu = menu;
  }

  constructor() {
    const overlay = inject(Overlay);
    const elementRef = inject<ElementRef<HTMLElement>>(ElementRef);
    const viewContainerRef = inject(ViewContainerRef);

    super(
      overlay,
      elementRef,
      viewContainerRef,
      undefined,
      undefined,
      undefined
    );
  }

  @Input('matMenuTriggerFor')
  set ignoredMenu(value: any) {}

  override _handleMousedown(event: MouseEvent): void {
    return super._handleMousedown(
      new MouseEvent(
        event.type,
        Object.assign({}, event, {
          button:
            event.button === 0 ? 2 : event.button === 2 ? 0 : event.button,
        })
      )
    );
  }

  override _handleClick(event: MouseEvent): void {}

  private hostElement: EventTarget | null = null;

  @HostListener('contextmenu', ['$event'])
  _handleContextMenu(event: MouseEvent): void {
    this.hostElement = event.target;
    if (event.shiftKey) return;
    event.preventDefault();
    this.openMenuAt(event.clientX, event.clientY);
  }

  private openMenuAt(x: number, y: number): void {
    if (this.menu) {
      this.menu.xPosition = 'after';
      this.menu.yPosition = 'below';
      this.openMenu();
      setTimeout(() => {
        const overlay = document.querySelector(
          '.cdk-overlay-connected-position-bounding-box .cdk-overlay-pane:not(.mat-mdc-tooltip-panel-left):not(.mat-mdc-tooltip-panel-top):not(.mat-mdc-tooltip-panel-below):not(.mat-mdc-tooltip-panel-right)'
        ) as HTMLElement;
        if (overlay) {
          overlay.style.position = 'absolute';
          overlay.style.top = `${y}px`;
          overlay.style.left = `${x}px`;
        }
      }, 50);
    }
  }

  private contextListenerSub = merge(
    fromEvent(document, 'contextmenu'),
    fromEvent(document, 'click')
  ).subscribe((event) => {
    if (this.menuOpen) {
      if (event.target) {
        const target = event.target as HTMLElement;
        if (target.classList.contains('cdk-overlay-backdrop')) {
          event.preventDefault();
          this.closeMenu();
        } else {
          let inOverlay = false;
          document.querySelectorAll('.cdk-overlay-container').forEach((e) => {
            if (e.contains(target)) inOverlay = true;
          });
          if (inOverlay) {
            if (event.type === 'contextmenu') {
              event.preventDefault();
              event.target?.dispatchEvent(new MouseEvent('click', event));
            }
          } else if (target !== this.hostElement) this.closeMenu();
        }
      }
    }
  });

  override ngOnDestroy() {
    this.contextListenerSub.unsubscribe();
    this.hostElement = null;
    return super.ngOnDestroy();
  }
}
