import { Directive, Input, inject } from '@angular/core';
import { NgControl } from '@angular/forms';
import { ControlValueAccessor } from '@angular/forms';

@Directive({
  selector: '[appControlValueAccessor]',
  standalone: true
})
export class ControlValueAccessorDirective {
  private ngControl = inject(NgControl, { optional: true, self: true });

  @Input('appControlValueAccessor') valueAccessor: ControlValueAccessor | null = null;

  constructor() {
    if (this.ngControl && this.valueAccessor) {
      this.ngControl.valueAccessor = this.valueAccessor;
    }
  }
}
