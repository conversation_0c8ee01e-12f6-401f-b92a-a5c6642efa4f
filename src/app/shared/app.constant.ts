export const DATE_FORMAT = 'DD/MM/YYYY';
export const DATE_TIME_FORMAT = 'DD/MM/YYYY HH:mm:ss';
export const EMAIL_SUPER_ADMIN = '<EMAIL>';

export interface DATE_COUNT {
  years: number;
  months: number;
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  milliseconds: number;
}

export enum ACTION {
  CANCEL = 'CANCEL',
  SAVE = 'SAVE',
  DELETE = 'DELETE',
}

export function configSnackBar(type: string) {
  return {
    panelClass:
      type === 'success'
        ? 'bg-lime-500'
        : type === 'warning'
        ? 'bg-yellow-500'
        : 'bg-red-500',
  };
}

export function closeOneCdkBackdropReverse() {
  const backdrops = document.querySelectorAll('.cdk-overlay-backdrop');
  (<HTMLElement>backdrops[backdrops.length - 1]).click();
}

export interface CustomChartConfig {
  chartType: string;
  suffix: string;
  formatType: string;
  name: string;
  value: string;
  uniqueValues: [];
  piePositionX: number;
  piePositionY: number;
  sizePie: number;
  innerSize: number;
  pieIsSum: false;
  drilldownName?: '';
  drilldown?: [];
  dataLabel: boolean;
  isStack: boolean;
  inputText: string;
}

export enum ROLE {
  OWNER = 'OWNER',
  ADMIN = 'ADMIN',
  EDITOR = 'EDITOR',
  SUPPORT = 'SUPPORT',
}

export enum TYPE_INTEGRATION {
  EMBED = 'embed',
  FRESHCHAT = 'freshchat',
  MESSENGER = 'facebook',
  WHATSAPP = 'whatsapp',
  SLACK = 'slack',
  TELEGRAM = 'telegram',
  DISCORD = 'discord',
  ZALO = 'zalo',
}

export enum ROLE_ACCOUNT {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  USER = 'user',
  PARTNER = 'partner',
}

export enum TYPE_PLAN {
  FREE = 'Free',
  START = 'Starter',
  START_MONTH = 'Starter Monthly',
  START_YEAR = 'Starter Yearly',
  ESSENTIAL = 'Essential',
  ESSENTIAL_MONTH = 'Essential Monthly',
  ESSENTIAL_YEAR = 'Essential Yearly',
  BUSINESS = 'Business',
  BUSINESS_MONTH = 'Business Monthly',
  BUSINESS_YEAR = 'Business Yearly',
}

export enum STATUS {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum PAYMENT_METHOD {
  STRIPE = 'Stripe',
  MB = 'MB',
  ONEPAY = 'Onepay',
}

export enum PAYMENT_STATUS {
  PENDING = 'pending',
  DONE = 'done',
  CANCEL = 'cancel',
}

export const DEFAULT_WHITELIST = 'dxgpt.ai';

export enum FLOW_STATUS {
  INACTIVE = 'INACTIVE',
  ACTIVE = 'ACTIVE',
}

export enum FLOW_TRIGGER_TYPE {
  INTENT = 'INTENT',
  EVENT = 'EVENT',
}

export enum REST_API_METHOD {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

export const STUDIO_STATUS = {
  LIVE: 'LIVE',
  DEV: 'DEVELOPMENT',
};

export type STUDIO_STATUS_TYPE =
  (typeof STUDIO_STATUS)[keyof typeof STUDIO_STATUS];

export enum COMPLEX_MESSAGE_TYPE {
  INTERACTIVE = 'interactive',
  RICH_CARD = 'rich_card',
  MEDIA = 'media',
  FILE = 'file',
}

export enum MEDIA_MESSAGE_TYPE {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
}
