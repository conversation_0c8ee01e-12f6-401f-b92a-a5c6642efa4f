import { IPageRequest } from './page.model';

export interface IAgentDev {
  id?: number;
  ai_id: string;
  agent_id?: number;
  name: string;
  description: string;
  llm_type: string;
  instruction: string;
  rule: string;
  model_config: string;
  created_at?: string;
  updated_at?: string;
  is_default?: boolean;
  role?: string;
  is_knowledge?: boolean;
}

export interface IAgentDevFilter extends IPageRequest {
  keyword?: string;
}
