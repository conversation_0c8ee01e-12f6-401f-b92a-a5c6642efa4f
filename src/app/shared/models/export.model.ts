export enum ExportConfigType {
  SFTP = "sftp",
  S3 = "s3"
}

export interface IExportConfigBase {
  id?: number;
  ai_id?: string;
  config?: string;
  type_config?: ExportConfigType;
}

export interface IExportConfig extends IExportConfigBase {
  type?: ExportConfigType;

  server?: string;
  username?: string;
  password?: string;
  remote_path?: string;
  port?: number;
  aws_access_key_id?: string;
  aws_secret_access_key?: string;
  region_name?: string;
  bucket_name?: string;
}

export interface IExportMessageBody {
  id: number;
  from_date: string;
  to_date: string;
}
