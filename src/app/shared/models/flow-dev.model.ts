import { IFlowVersion } from './flow-version.model';

export interface IFlowDevBase {
  id?: number;
  flow_id?: number;
  category_id?: number;
  intent_dev_id?: number;
  event_id?: string;
  trigger_type?: string;
  ai_id: string;
  name: string;
  description: string;
  flow_data: string;
  is_start?: boolean;
}

export interface IFlowDev extends IFlowDevBase {
  flow_versions: Array<IFlowVersion>;
  isDisabled?: boolean;
  isActions?: boolean;
}

export interface IFlowDevFilter {
  key_word?: string;
  trigger_type?: string;
}
