export interface IFlowVersionBase {
  id?: number;
  flow_id: number;
  flow_dev_id: number;
  version_name: string;
  status: string;
}

export interface IFlowVersion extends IFlowVersionBase {
}

export interface IFlowVersionIn {
  id?: number;
  flow_dev_id: number;
  version_name: string;
  status: string;
}

export interface IFlowChangeVersionIn {
  id?: number;
  status: string;
}

export interface IFlowVersionFilter {
  key_word?: string;
  flow_id: number;
}
