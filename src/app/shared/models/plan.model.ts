export interface IPlan {
  id?: number;
  name: string;
  price: number;
  duration: string;
  currency: string;
  stripe_payment_link?: string;
  scope: string;
  features: IPlanFeature[];
  created_at?: string;
  updated_at?: string;
}

export interface IPlanFeature {
  id: number;
  feature: {
    id: number;
    name: string;
    type: string;
  };
  value: string;
}

export interface IFeature {
  id: number;
  name: string;
  type: string;
}

export interface IPlanResponse {
  items: IPlan[];
  total: number;
}

