export interface ILLMConfigBase {
  id?: number;
  ai_id?: string;
  temperatureRag: number;
  temperatureCF: number;
  temperatureDI: number;
  limit: number;
  threshold: number;
  modelRag: string;
  modelCF: string;
  modelDI: string;
}

export interface ILLMConfig extends ILLMConfigBase {}

export interface ILLMConfigFilter {
  ai_id?: string;
}

export interface ISearchSetting {
  enabled: boolean;
  preferSites: string[];
  excludeSites: string[];
  modelAnswer: string;
  temperatureAnswer: number;
  fullText: boolean;
}

export interface IAgentSetting {
  rag: {
    description: string;
    role: string;
    rule: string;
  };
  gather_information: IGatherInformation;
}
export interface IGatherInformation {
  enabled: boolean;
  ai_model: string;
  question_count: number;
  default_parameters: IDefaultParameter[];
  parameters: ICustomParameter[];
  webhook_set_user_data?: string;
}
export interface IDefaultParameter {
  key: string;
  label?: string;
  type?: string;
  description?: string;
  note?: string;
  is_required: boolean;
}

export interface ICustomParameter {
  key: string;
  type: string;
  description: string;
  note: string;
  is_required: boolean;
}
