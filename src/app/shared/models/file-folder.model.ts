export interface IFolderFilter {
  id: number | null;
  name: string;
  file_type: string | null;
  file_status: string | null;
  sort_by: string;
  order: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

export interface ISearchModel {
  name: string;
  collection_id: number;
  folder_id: number;
  file_type: string;
  file_status: string;
  order: 'ASC' | 'DESC';
  sort_by: string;
  page: number;
  per_page: number;
  id: number;
}

export interface IFolderBase {
  id?: number;
  ai_id: string;
  parent_id: number | null;
  name: string;
}

export interface IFolder extends IFolderBase {
  users?: Array<{ permission: number; [key: string]: any }>;
  isDeleting?: boolean;
  isFolder?: boolean;
}

export interface IFileBase {
  id?: number;
  folder_id: number | null;
  name: string;
  ext: string;
  metadata_columns: string;
  collection_id: number;
  size: number;
  text_content: string;
  url: string;
  file_path: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface IFile extends IFileBase {
  isRetraining?: boolean;
  isDeleting?: boolean;
  isFolder?: boolean;
}

export interface IViewFile {
  folders: IFolderBase[];
  files: IFile[];
  permissions: number;
  pagination?: {
    total_folders: number;
    total_files: number;
    current_page: number;
    total_pages: number;
    has_more: boolean;
  };
}

export interface IAssignPermission {
  user_id: number;
  permissions: string[];
}

export interface IFolderUserPermission {
  folder_ids: number[];
  assign_permissions: IAssignPermission[];
}
