import { IFlowVersion } from './flow-version.model';

export interface IFlowBase {
  id?: number;
  intent_id: number;
  event_id?: string;
  trigger_type?: string;
  category_id?: number;
  ai_id: string;
  name: string;
  description: string;
  flow_data: string;
  status: string;
  is_start?: boolean;
}

export interface IFlow extends IFlowBase {
  flow_version: IFlowVersion;
  isDisabled?: boolean;
  isActions?: boolean;
  isContextMenu?: boolean;
}

export interface IFlowFilter {
  key_word?: string;
  trigger_type?: string;
}
