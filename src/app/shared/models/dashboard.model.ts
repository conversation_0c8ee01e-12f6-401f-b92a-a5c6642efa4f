export interface IConversationStats {
  conversations?: number;
  conversations_by_day?: any;
  conversations_handoff?: number;
  percentage_conversations_handoff_previous_period: number;
  percentage_conversation_previous_period: number;
  conversations_percent_change?: number;
  conversations_handoff_percent_change?: number;
  message_numb?: number;
  avg_time_message_response?: number;
}

export interface IConverstionTime {
  average_time?: number;
  max_time?: number;
  min_time?: number;
}

export interface IconversationDomain {
  list_conv_embed?: any;
  list_conv_integration?: any;
}

export interface IstaticsAverageCostMessage {
  average_cost?: number;
  average_cost_this_period?: number;
}

export interface IStatisticDashboard {
  conversation_stats?: IConversationStats;
  start_date?: string;
  end_date?: string;
  messages_limit?: number;
  messages_this_period?: number;
  percentMessagesPeriod?: number;
  statistics_conversation_time?: IConverstionTime;
  statistics_conversation_domain?: IconversationDomain;
  message_numb?: number;
  statistics_average_cost_message?: IstaticsAverageCostMessage;
}
