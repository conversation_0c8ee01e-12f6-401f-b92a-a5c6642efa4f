import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

// Interface for debug message structure
export interface DebugMessage {
  conversation_id: string;
  node_id: string;
  edge: string | null;
  is_prompt: boolean;
  name: string;
  output_key: string;
  result: string;
  start_time: string;
}

@Injectable({
  providedIn: 'root'
})
export class DebugMessageService {
  private debugMessageSubject = new Subject<DebugMessage>();

  /**
   * Observable stream of debug messages
   */
  debugMessages$ = this.debugMessageSubject.asObservable();

  /**
   * Send a debug message to all subscribers
   * @param message The debug message to send
   */
  sendDebugMessage(message: DebugMessage): void {
    try {
      this.debugMessageSubject.next(message);
    } catch (error) {
      console.error('Error sending debug message:', error);
    }
  }

  /**
   * Complete the subject (called on service destruction)
   */
  complete(): void {
    this.debugMessageSubject.complete();
  }
}
