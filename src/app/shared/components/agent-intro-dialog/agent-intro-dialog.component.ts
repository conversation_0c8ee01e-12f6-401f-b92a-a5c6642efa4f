import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { StudioStore } from '@core/stores';
import { DxButton, DxDialogRef, DxSnackBar } from '@dx-ui/ui';
import { SettingsService } from '@shared/services';
import { switchMap } from 'rxjs';

@Component({
  selector: 'app-agent-intro-dialog',
  standalone: true,
  imports: [DxButton],
  templateUrl: './agent-intro-dialog.component.html',
  styleUrl: './agent-intro-dialog.component.scss',
})
export class AgentIntroDialogComponent {
  dialogRef = inject(DxDialogRef<AgentIntroDialogComponent>);
  router = inject(Router);
  settingsService = inject(SettingsService);
  snackBar = inject(DxSnackBar);
  studioStore = inject(StudioStore);

  onCreateAgent(): void {
    this.switchToAgentMode();
  }

  private switchToAgentMode(): void {
    this.settingsService
      .getDetailSetting()
      .pipe(
        switchMap((currentSettings) => {
          console.log(currentSettings);
          const agentModeEnabled = Boolean(
            currentSettings.settings?.basic?.use_agent?.enabled
          );

          if (agentModeEnabled) {
            this.navigateToAgentCreation();
            return [];
          }

          const updatedSettings = {
            ...currentSettings.settings,
            basic: {
              ...currentSettings?.basic,
              use_agent: {
                ...currentSettings?.basic?.use_agent,
                enabled: 1,
              },
            },
          };

          return this.settingsService.updateSetting(updatedSettings);
        })
      )
      .subscribe({
        next: (result) => {
          if (result) {
            this.studioStore.setMode('agent');
            this.snackBar.open('Successfully switched to Agent Mode', '', {
              panelClass: 'dx-snack-bar-success',
              duration: 3000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            });
          }
          this.navigateToAgentCreation();
        },
        error: (err) => {
          this.snackBar.open(
            'Error switching to agent mode: ' +
              (err.error?.detail || err.message),
            '',
            {
              panelClass: 'dx-snack-bar-error',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            }
          );
          this.navigateToAgentCreation();
        },
      });
  }

  private navigateToAgentCreation(): void {
    this.dialogRef.close();
    this.router.navigate(['/studio/builder/agent-tool']);
  }

  onLearnMore(): void {
    window.open(
      'https://docs.dxconnect.lifesup.ai/Diving%20Deeper/Studio/builder/Agent%20Flow%20Usage%20Guide/agent-flow-guide',
      '_blank'
    );
  }

  onSkip(): void {
    this.dialogRef.close();
  }
}
