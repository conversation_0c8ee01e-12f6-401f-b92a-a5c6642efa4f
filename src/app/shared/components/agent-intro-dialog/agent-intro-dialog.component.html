<div class="mx-auto p-6">
  <div class="text-center mb-6">
    <div
      class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"
    >
      <svg
        class="w-8 h-8 text-blue-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
        ></path>
      </svg>
    </div>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">
      AI-Powered Agent Creation
    </h2>
    <p class="text-gray-600 text-sm leading-relaxed">
      Supercharge your conversational AI with intelligent agents that can handle
      complex tasks, integrate with your tools, and provide personalized
      experiences for your users.
    </p>
  </div>

  <div class="space-y-4 mb-6">
    <div class="flex items-start space-x-3">
      <div
        class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5"
      >
        <svg
          class="w-3 h-3 text-green-600"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          ></path>
        </svg>
      </div>
      <div>
        <h4 class="font-medium text-gray-900">Smart Automation</h4>
        <p class="text-sm text-gray-600">
          Automate complex workflows and handle multi-step processes
        </p>
      </div>
    </div>

    <div class="flex items-start space-x-3">
      <div
        class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5"
      >
        <svg
          class="w-3 h-3 text-green-600"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          ></path>
        </svg>
      </div>
      <div>
        <h4 class="font-medium text-gray-900">Custom Knowledge</h4>
        <p class="text-sm text-gray-600">
          Train agents with your specific data and business logic
        </p>
      </div>
    </div>

    <div class="flex items-start space-x-3">
      <div
        class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5"
      >
        <svg
          class="w-3 h-3 text-green-600"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          ></path>
        </svg>
      </div>
      <div>
        <h4 class="font-medium text-gray-900">Seamless Integration</h4>
        <p class="text-sm text-gray-600">
          Connect with external APIs and third-party services
        </p>
      </div>
    </div>
  </div>

  <div class="flex flex-col space-y-3">
    <button
      dxButton="filled"
      type="button"
      class="w-full"
      (click)="onCreateAgent()"
    >
      Create Your First Agent
    </button>

    <div class="flex space-x-3">
      <button
        dxButton="elevated"
        type="button"
        class="flex-1"
        (click)="onLearnMore()"
      >
        Learn More
      </button>

      <button
        dxButton="elevated"
        type="button"
        class="flex-1"
        (click)="onSkip()"
      >
        Skip for Now
      </button>
    </div>
  </div>
</div>
