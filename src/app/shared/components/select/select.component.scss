.select-container {
  position: relative;
  width: 100%;
}

select {
  appearance: none; /* Remove default arrow */
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1em 1em;
  padding-right: 2.5rem;

  &:disabled {
    opacity: 0.7;
  }

  /* Remove IE specific arrow */
  &::-ms-expand {
    display: none;
  }

  /* Ensure text doesn't overlap with the arrow */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/* Searchable select styles */
.searchable-select {
  position: relative;
}

/* Dropdown styles */
.dropdown-menu {
  max-height: 250px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

/* Option item styles */
.option-item {
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &.selected {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

/* Checkbox styles */
.checkbox-primary {
  accent-color: var(--color-light-primary) !important;
  color: var(--color-light-primary) !important;
}

.checkbox-primary.rounded {
  border-radius: 4px;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23d1d5db' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-size: 1em 1em;
  }

  .dropdown-menu {
    &::-webkit-scrollbar-track {
      background: #374151;
    }

    &::-webkit-scrollbar-thumb {
      background: #6B7280;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #9CA3AF;
    }
  }

  .option-item {
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.selected {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }
}
