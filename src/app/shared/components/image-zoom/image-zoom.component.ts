
import { Component, effect, input, signal } from '@angular/core';

@Component({
  selector: 'app-image-zoom',
  standalone: true,
  imports: [],
  templateUrl: './image-zoom.component.html',
  styleUrls: ['./image-zoom.component.css'],
})
export class ImageZoomComponent {
  imageUrl = input.required<string>();
  zoomTrigger = input.required<boolean>();
  isZoomed = signal<boolean>(false);

  private effect = effect(() => {
    if (this.zoomTrigger()) {
      this.zoomImage();
    }
  });

  zoomImage() {
    this.isZoomed.set(true);
  }

  closeZoom() {
    this.isZoomed.set(false);
  }
}
