
import {
  AfterViewInit,
  Component,
  effect,
  ElementRef,
  input,
  signal,
  ViewChild,
} from '@angular/core';
import { ImageZoomComponent } from '../image-zoom/image-zoom.component';

@Component({
  selector: 'app-image-gallery',
  standalone: true,
  imports: [ImageZoomComponent],
  templateUrl: './image-gallery.component.html',
  styleUrls: ['./image-gallery.component.css'],
})
export class ImageGalleryComponent implements AfterViewInit {
  @ViewChild('thumbnailContainer') thumbnailContainer!: ElementRef;

  imageSrcs = input.required<any[]>();

  selectedImage = signal<any>(null);
  selectedImageZoom = signal<any>(null);
  shouldZoom = signal<boolean>(false);

  private effect = effect(() => {
    if (this.imageSrcs()) {
      this.selectedImage.set(this.imageSrcs()[0]);
      this.scrollToSelectedImage();
    }
  });

  ngAfterViewInit(): void {
    this.scrollToSelectedImage();
  }

  onSelect(image: any): void {
    this.selectedImage.set(image);
    this.scrollToSelectedImage();
  }

  onImageClick(imgSrc: string) {
    this.selectedImageZoom.set(imgSrc);
    this.shouldZoom.set(true);
    setTimeout(() => {
      this.shouldZoom.set(false);
    }, 100);
  }

  scrollLeft(): void {
    const currentIndex = this.imageSrcs().indexOf(this.selectedImage());
    if (currentIndex > 0) {
      this.selectedImage.set(this.imageSrcs()[currentIndex - 1]);
      this.scrollToSelectedImage();
    }
  }

  scrollRight(): void {
    const currentIndex = this.imageSrcs().indexOf(this.selectedImage());
    if (currentIndex < this.imageSrcs().length - 1) {
      this.selectedImage.set(this.imageSrcs()[currentIndex + 1]);
      this.scrollToSelectedImage();
    }
  }

  private scrollToSelectedImage(): void {
    if (this.thumbnailContainer) {
      const container = this.thumbnailContainer.nativeElement;
      setTimeout(() => {
        const selectedThumbnail = container.querySelector(
          '.thumbnail.selected'
        ) as HTMLElement;

        if (selectedThumbnail) {
          selectedThumbnail.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center',
          });
        }
      }, 100);
    }
  }
}
