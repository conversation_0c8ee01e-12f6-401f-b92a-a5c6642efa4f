.gallery {
  text-align: center;
}

.main-image {
  display: flex;
  justify-content: center;
  margin-bottom: 6px;
  position: relative; /* Th<PERSON><PERSON> thuộc t<PERSON>h này để đặt vị trí cho image-label */
}

.main-image img {
  width: 360px;
  height: auto;
  object-fit: cover; /* Ensure the image covers the set width and height */
  border-radius: 6px;
}

.image-label {
  position: absolute;
  top: var(--label-space);
  left: var(--label-space);
  background-color: rgba(0, 0, 0, 0.7); /* Nền màu đen với độ mờ */
  color: white; /* <PERSON><PERSON><PERSON> chữ trắng */
  padding: 5px 16px;
  border-radius: 16px;
  border-color: #6b6b6b;
  border-width: 1px;
}

.thumbnail-container-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: var(--max-width);
  margin: 0 auto;
  position: relative; /* Ensure that the scroll buttons are positioned relative to this container */
}

.scroll-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1; /* Ensure scroll buttons are above the thumbnails */
}

.scroll-button.left {
  left: -12px; /* Position the left button on the left edge */
}

.scroll-button.right {
  right: -12px; /* Position the right button on the right edge */
}

.thumbnail-container {
  display: flex;
  align-items: center;
  overflow-x: hidden; /* Ẩn thanh cuộn */
  width: 372px;
  scroll-behavior: smooth;
  padding: 10px 0;
  gap: var(--gap);
}

.thumbnail {
  flex: 0 0 auto;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border 0.3s ease;
}

.thumbnail img {
  width: var(--img-width);
  height:  var(--img-height);
  object-fit: cover; /* Ensure the image covers the set width and height */
  border-radius: 6px;
}

.thumbnail.selected {
  border: 2px solid #007bff;
  border-radius: 8px;
}
