<div class="gallery">
  <div class="main-image">

    @if (selectedImage()?.label) {
    <div class="image-label">{{ selectedImage().label }}</div>
    }

    <img [src]="selectedImage()?.src" [alt]="selectedImage()?.label" class="zoom">
  </div>

  @if (imageSrcs().length > 1) {
  <div class="thumbnail-container-wrapper">
    <button class="scroll-button left" (click)="scrollLeft()">&#10094;</button>
    <div class="thumbnail-container" #thumbnailContainer>

      @for (image of imageSrcs(); track $index) {
      <div class="thumbnail" (click)="onSelect(image)" [class.selected]="image === selectedImage()">
        <img [src]="image.src" [alt]="image.label">
      </div>
      }

    </div>
    <button class="scroll-button right" (click)="scrollRight()">&#10095;</button>
  </div>
  }

  <app-image-zoom [imageUrl]="selectedImageZoom()" [zoomTrigger]="shouldZoom()"></app-image-zoom>
</div>