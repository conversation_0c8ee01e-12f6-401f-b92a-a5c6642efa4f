<div class="flex items-center">
  <dx-form-field
    [ngClass]="uiStore.isHandset() ? 'w-30' : ''"
    [style.margin-bottom]="0"
    [style.--dx-form-field-label-offset-y]="0"
    [subscriptHidden]="true"
  >
    @if (studioStore.statusComputed() === STUDIO_STATUS.DEV) {
    <div
      dxPrefix
      class="aspect-square flex items-center justify-center w-3 h-3 bg-success ml-4 mr-2 rounded-full"
    ></div>
    } @if (studioStore.statusComputed() === STUDIO_STATUS.LIVE) {
    <div
      dxPrefix
      class="aspect-square flex items-center justify-center w-3 h-3 bg-info ml-4 mr-2 rounded-full"
    ></div>
    }
    <dx-select
      [formControl]="studioStatus"
      (valueChange)="selectFlowEnv($event)"
    >
      <dx-option [value]="STUDIO_STATUS.DEV">{{
        uiStore.isHandset() ? "Dev" : "Development"
      }}</dx-option>
      <dx-option [value]="STUDIO_STATUS.LIVE">Live</dx-option>
    </dx-select>
  </dx-form-field>
</div>
