<!--<div class="p-4">-->
<!--  <div class="header py-2 border-b relative">-->
<!--    <h1 class="text-xl font-bold">{{ data?.title || 'Delete record' }}</h1>-->
<!--    <button class="p-2 rounded-full flex absolute right-0 top-0 hover:bg-light-primary"-->
<!--            (click)="closeOneCdkBackdropReverse()">-->
<!--&lt;!&ndash;      <mat-icon class="!text-light-text dark:!text-dark-text">cancel</mat-icon>&ndash;&gt;-->
<!--    </button>-->
<!--  </div>-->

<!--  <div class="content my-4 text-light-white dark:text-light-black">-->
<!--    <div>{{ data?.content || 'specialText.delete_1' }}</div>-->
<!--    <div *ngIf="data?.subContent">{{ data?.subContent }}</div>-->
<!--  </div>-->

<!--  <div class="footer flex justify-end items-center gap-4">-->
<!--    <button class="bg-light-white px-4 py-1 rounded-full" (click)="closeOneCdkBackdropReverse()">{{ 'Cancel'}}</button>-->
<!--    <button-->
<!--      class="bg-light-primary px-4 py-1 rounded-full text-light-white button-disabled"-->
<!--      [ngClass]="{ 'bg-light-primary': !data?.isDelete, 'bg-light-red': data?.isDelete }"-->
<!--      (click)="dialogRef.close('success')"-->
<!--    >-->
<!--      {{ 'Yes' }}-->
<!--    </button>-->
<!--  </div>-->
<!--</div>-->
