@if (type() === COMPLEX_MESSAGE_TYPE.INTERACTIVE) {
<div
  class="w-full flex flex-col space-y-3 p-3 !rounded-xl overflow-x-auto bg-gray-100"
>
  <div
    class="overflow-wrap whitespace-pre-wrap !text-sm"
    [innerHTML]="content().text | safeHtml"
  ></div>
  <div class="flex items-stretch w-full flex-wrap gap-2">
    @for (action of content().buttons; track action) {
    <button
      [style.background-color]="action.bgColor"
      [style.color]="action.textColor"
      class="w-full px-3 py-1 rounded-[12px] text-left overflow-wrap whitespace-pre-wrap transition-colors !text-sm"
      (click)="actionOnClick(action)"
    >
      {{ action.label }}
    </button>
    }
  </div>
</div>
} @if (type() === COMPLEX_MESSAGE_TYPE.RICH_CARD) {
<div
  class="w-full flex flex-col space-y-3 px-3 py-1 !rounded-xl overflow-x-auto bg-gray-100"
>
  <div class="grid-container">
    <div class="grid-item">
      <div class="image-wrapper">
        <img
          [src]="content().image_url"
          [alt]="content().image_url"
          class="zoom n-margin"
        />
      </div>
    </div>
  </div>
  <div
    class="overflow-wrap whitespace-pre-wrap font-bold"
    [innerHTML]="content().title | safeHtml"
  ></div>
  <div
    class="overflow-wrap whitespace-pre-wrap"
    [innerHTML]="content().subtitle | safeHtml"
  ></div>
  <div class="flex items-stretch w-full flex-wrap gap-2">
    @for (action of content().buttons; track action) {
    <button
      [style.background-color]="action.bgColor"
      [style.color]="action.textColor"
      class="w-full px-3 py-1 rounded-[12px] text-left overflow-wrap whitespace-pre-wrap transition-colors"
      (click)="actionOnClick(action)"
    >
      {{ action.label }}
    </button>
    }
  </div>
</div>
}
