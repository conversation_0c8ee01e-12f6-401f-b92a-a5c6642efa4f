import { Component, input, output } from '@angular/core';
import { COMPLEX_MESSAGE_TYPE } from '@shared/app.constant';
import { SafeHtmlPipe } from '@shared/pipes';

@Component({
  selector: 'app-complex-message',
  standalone: true,
  imports: [SafeHtmlPipe],
  templateUrl: './complex-message.component.html',
  styleUrls: ['./complex-message.component.css'],
})
export class ComplexMessageComponent {
  type = input.required<string>();
  content = input.required<any>();

  onActionClick = output<any>();

  protected readonly COMPLEX_MESSAGE_TYPE = COMPLEX_MESSAGE_TYPE;

  actionOnClick(action: any) {
    this.onActionClick.emit(action);
  }
}
