:host {
  display: block;
  width: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(var(--col), 1fr); /* Sử dụng biến CSS cho số cột */
  gap: 10px;
}

.grid-item {
  display: flex;
  align-items: stretch; /* <PERSON><PERSON>m bảo các item trong grid có cùng chiều cao */
}

.image-wrapper {
  border-color: #e0e0e0;
  border-width: 0.5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Đ<PERSON>m bảo hình ảnh và label có không gian đúng */
  background-color: rgba(227, 227, 227, 0.1); /* Màu nền xám nhạt với độ mờ */
  width: 100%;
  height: 100%; /* Đảm bảo chiều cao của .image-wrapper chiếm toàn bộ chiều cao của .grid-item */
  border-radius: 12px;
}

img {
  width: 100%;
  height: 200px; /* <PERSON>iều chỉnh chiều cao của ảnh để có kích thước bằng nhau */
  object-fit: cover; /* <PERSON><PERSON><PERSON> bảo ảnh không bị biến dạng và đầy đủ không gian */
  border-radius: 8px;
}

