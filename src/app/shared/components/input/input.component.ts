import { CommonModule } from '@angular/common';
import { Component, forwardRef, input, OnInit } from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  FormGroup,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { TrimStringDirective } from '@shared/directives';

@Component({
  selector: 'app-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    TrimStringDirective,
  ],
  templateUrl: './input.component.html',
  styleUrl: './input.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputComponent),
      multi: true,
    },
  ],
})
export class InputComponent implements ControlValueAccessor, OnInit {
  type = input<string>('text');
  id = input<string>('');
  name = input<string>('');
  placeholder = input<string>('');
  required = input<boolean>(false);
  disabled = input<boolean>(false);
  readonly = input<boolean>(false);
  formControlName = input<string>('');
  formGroup = input<FormGroup | null>(null);
  trim = input<'blur' | 'change'>('blur');
  min = input<number | null>(null);
  max = input<number | null>(null);
  minlength = input<number | null>(null);
  maxlength = input<number | null>(null);
  pattern = input<string | null>(null);
  autocomplete = input<'on' | 'off'>('on');

  value: any = '';
  onChange: any = () => {};
  onTouched: any = () => {};
  isDisabled: boolean = false;

  ngOnInit() {
    // No initialization needed for now
    this.value = this.control?.value;
  }

  get control(): FormControl | null {
    try {
      if (!this.formGroup() || !this.formControlName()) {
        return null;
      }

      // Convert formControlName to string if it's not already
      const controlName = this.formControlName;
      // Get the control
      return this.formGroup()?.get(controlName()) as FormControl;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  get isInvalid(): boolean {
    try {
      const control = this.control;
      if (!control) return false;
      return (control.invalid && (control.touched || control.dirty)) || false;
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  get isValid(): boolean {
    try {
      const control = this.control;
      if (!control) return false;
      return (control.valid && (control.touched || control.dirty)) || false;
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.value = target.value;
    this.onChange(this.value);
  }

  onBlur(): void {
    this.onTouched();
    if (this.trim() === 'blur' && typeof this.value === 'string') {
      this.value = this.value.trim();
      this.onChange(this.value);
    }
  }
}
