import { Component, Input, OnChanges, SimpleChanges, inject } from '@angular/core';

import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { IconService } from '@shared/services';

@Component({
  selector: 'app-custom-icon',
  standalone: true,
  imports: [NgIconsModule],
  template: `
    <div class="flex items-center justify-center" [style.width]="size + 'px'" [style.height]="size + 'px'">
      @if (isFontAwesome) {
        <span
          class="inline-flex items-center justify-center w-full h-full"
          [style.color]="color"
          [innerHTML]="svgContent">
        </span>
      } @else {
        <ng-icon
          [name]="iconName"
          [size]="size.toString()"
          [color]="color"
          class="flex items-center justify-center">
        </ng-icon>
      }
    </div>
    `,
  providers: [
    { provide: NgIconsModule, useFactory: () => inject(IconService).provideIcons() }
  ],
  styles: [`
    :host {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    svg {
      width: 100%;
      height: 100%;
      display: block;
    }
  `]
})
export class CustomIconComponent implements OnChanges {
  @Input() iconName: string = '';
  @Input() size: number = 24;
  @Input() color: string = 'currentColor';

  isFontAwesome: boolean = false;
  svgContent: SafeHtml | null = null;

  private sanitizer = inject(DomSanitizer);
  private iconService = inject(IconService);

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['iconName']) {
      this.updateIcon();
    }
  }

  private updateIcon(): void {
    // Check if it's a Font Awesome icon (starts with 'fa')
    if (this.iconName.startsWith('fa')) {
      const faIcon = this.iconService.getFaIcon(this.iconName);
      if (faIcon) {
        this.isFontAwesome = true;
        this.svgContent = this.sanitizer.bypassSecurityTrustHtml(faIcon.data);
      } else {
        console.warn(`Font Awesome icon "${this.iconName}" not found`);
        this.isFontAwesome = false;
      }
    } else {
      this.isFontAwesome = false;
    }
  }
}
