<div
  class="fixed top-0 w-full bg-base-200 dark:bg-dark-base-200 flex items-center justify-between z-[900] h-14 px-4 py-3"
  [class.relative]="!fixed()"
  [class.border-b]="border()"
  [class.border-primary-border]="border()"
  [class.dark:border-dark-primary-border]="border()"
  [class.bg-transparent]="transparent() && !collapseHeader()"
  [class.bg-primary]="main() && !collapseHeader()"
>
  @if (!hideBack()) {
    <div class="flex-none mr-3 flex items-center justify-center">
      <ng-icon
        (click)="mBack()"
        name="heroChevronLeft"
        class="text-2xl !text-base-content dark:!text-dark-base-content"
      ></ng-icon>
      <div
        class="text-[20px] font-bold text-truncate text-base-content dark:text-dark-base-content"
        [class.text-white]="main()"
      >
        {{ title() }}
      </div>
    </div>
  }
  @if (left()) {
    <div class="grow flex items-center">
      <ng-content select="[mHeaderLeft]"></ng-content>
    </div>
  }
  @if (center()) {
    <div class="flex-grow flex items-center justify-center">
      <ng-content select="[mHeaderCenter]"></ng-content>
    </div>
  }
  @if (!title() || title().trim().length === 0) {
    <div class="flex-grow"></div>
  }
  @if (right()) {
    <div class="h-full flex items-center justify-end space-x-2">
      <ng-content select="[mHeaderRight]"></ng-content>
      @if (!hideMenu()) {
        <ng-icon
          (click)="isMenuOpened.set(true)"
          name="heroEllipsisVertical"
          class="text-2xl !text-base-content dark:!text-dark-base-content"
          cdkOverlayOrigin
          #trigger="cdkOverlayOrigin"
        ></ng-icon>
      }
    </div>
  } @else {
    @if (!left() && !center()) {
      <div class="flex-grow"></div>
    }
    @if (!hideMenu()) {
      <div class="flex-none flex items-center">
        <ng-icon
          (click)="isMenuOpened.set(true)"
          name="heroEllipsisVertical"
          class="text-2xl !text-base-content dark:!text-dark-base-content"
          cdkOverlayOrigin
          #trigger="cdkOverlayOrigin"
        ></ng-icon>
      </div>
    }
  }
</div>

<ng-template
  cdkConnectedOverlay
  cdkConnectedOverlayHasBackdrop
  (backdropClick)="isMenuOpened.set(false)"
  [cdkConnectedOverlayOrigin]="trigger()!"
  [cdkConnectedOverlayWidth]="'78dvw'"
  [cdkConnectedOverlayOpen]="isMenuOpened()"
  [cdkConnectedOverlayPush]="true"
  [cdkConnectedOverlayPositions]="[
    {
      originX: 'start',
      originY: 'center',
      overlayX: 'end',
      overlayY: 'center',
      offsetX: -8,
      offsetY: 4
    }
  ]"
>
  <ul
    class="w-full flex flex-col p-3 border border-primary-border dark:border-dark-primary-border rounded-xl bg-base-400 dark:bg-dark-base-400 z-50 shadow-lg"
  >
    <li>
      <div
        class="text-neutral-content dark:text-dark-neutral-content font-semibold !mb-[-16px]"
      >
        Select AI
      </div>
      <app-ai-select></app-ai-select>
    </li>
  </ul>
</ng-template>
