
import { Component, Input } from '@angular/core';
import { AbstractControl } from '@angular/forms';

@Component({
  selector: 'app-error-message',
  standalone: true,
  imports: [],
  template: `
    @if (control && control.invalid && (control.dirty || control.touched)) {
    <div class="text-red-500 text-sm mt-1">
      @if (control.errors?.['required']) {
      <div>{{ name }} is required.</div>
      } @if (control.errors?.['email']) {
      <div>Please enter a valid email address.</div>
      } @if (control.errors?.['minlength']) {
      <div>
        {{ name }} must be at least
        {{ control.errors?.['minlength'].requiredLength }} characters.
      </div>
      } @if (control.errors?.['maxlength']) {
      <div>
        {{ name }} cannot exceed
        {{ control.errors?.['maxlength'].requiredLength }} characters.
      </div>
      } @if (control.errors?.['pattern']) {
      <div>{{ name }} format is invalid.</div>
      } @if (control.errors?.['min']) {
      <div>{{ name }} must be at least {{ control.errors?.['min'].min }}.</div>
      } @if (control.errors?.['max']) {
      <div>{{ name }} cannot exceed {{ control.errors?.['max'].max }}.</div>
      }
    </div>
    }
  `,
  styles: [],
})
export class ErrorMessageComponent {
  @Input() control: AbstractControl | null = null;
  @Input() name: string = 'This field';
}
