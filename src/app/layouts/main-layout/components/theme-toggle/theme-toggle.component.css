
.theme-slider {
  position: relative;
  width: 56px;
  height: 28px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.98);
  }

  .slider-track {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e2e8f0;
    border-radius: 34px;
    transition: background-color 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .slider-thumb {
    position: absolute;
    height: 24px;
    width: 24px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  &.dark {
    .slider-track {
      background-color: #1A1D1F;
      border-color: rgba(255, 255, 255, 0.1);
    }

    .slider-thumb {
      left: calc(100% - 26px);
      background-color: #1e293b;
    }
  }

  .icon-light {
    color: #f59e0b;
    font-size: 16px;
  }

  .icon-dark {
    color: #94a3b8;
    font-size: 16px;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .theme-slider {
    width: 50px;
    height: 24px;

    .slider-thumb {
      height: 20px;
      width: 20px;
    }

    &.dark .slider-thumb {
      left: calc(100% - 22px);
    }

    .icon-light, .icon-dark {
      font-size: 14px;
    }
  }
}
