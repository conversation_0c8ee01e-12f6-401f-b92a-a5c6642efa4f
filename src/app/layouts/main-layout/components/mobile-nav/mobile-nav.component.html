<nav
  class="fixed z-[999] bottom-0 w-full h-18 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200 flex"
>
  <a
    *hasRoles="{
      userRoles: [
        ROLE_ACCOUNT.USER,
        ROLE_ACCOUNT.ADMIN,
        ROLE_ACCOUNT.PARTNER,
        ROLE_ACCOUNT.SUPER_ADMIN
      ],
      aiRoles: [ROLE_AI.SUPPORT, ROLE_AI.EDITOR, ROLE_AI.ADMIN, ROLE_AI.OWNER]
    }"
    routerLink="/dashboard"
    class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
    routerLinkActive="!text-primary font-semibold"
    [routerLinkActiveOptions]="{ exact: false }"
  >
    <app-svg-icon type="icDashboard" class="w-7 h-7"></app-svg-icon>
    <div class="text-[13px]">Dashboard</div>
  </a>

  <a
    *hasRoles="{
      userRoles: [
        ROLE_ACCOUNT.USER,
        ROLE_ACCOUNT.ADMIN,
        ROLE_ACCOUNT.PARTNER,
        ROLE_ACCOUNT.SUPER_ADMIN
      ],
      aiRoles: [ROLE_AI.SUPPORT, ROLE_AI.EDITOR, ROLE_AI.ADMIN, ROLE_AI.OWNER]
    }"
    routerLink="/inbox"
    class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
    routerLinkActive="!text-primary font-semibold"
    [routerLinkActiveOptions]="{ exact: false }"
  >
    <app-svg-icon type="icInbox" class="w-7 h-7"></app-svg-icon>
    <div class="text-[13px]">Inbox</div>
  </a>

  <a
    *hasRoles="{
      userRoles: [
        ROLE_ACCOUNT.USER,
        ROLE_ACCOUNT.ADMIN,
        ROLE_ACCOUNT.PARTNER,
        ROLE_ACCOUNT.SUPER_ADMIN
      ],
      aiRoles: [ROLE_AI.EDITOR, ROLE_AI.ADMIN, ROLE_AI.OWNER]
    }"
    routerLink="/preview"
    class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
    routerLinkActive="!text-primary !font-semibold"
    [routerLinkActiveOptions]="{ exact: false }"
  >
    <app-svg-icon type="icPreview" class="w-7 h-7"></app-svg-icon>
    <div class="text-[13px]">Preview</div>
  </a>

  <a
    routerLink="/menu"
    class="flex flex-col space-y-1 justify-center items-center flex-1 h-full px-1 overflow-hidden text-neutral-content dark:text-dark-neutral-content font-medium"
    routerLinkActive="!text-primary !font-semibold"
    [routerLinkActiveOptions]="{ exact: false }"
  >
    <app-svg-icon type="icIntegration" class="w-7 h-7"></app-svg-icon>
    <div class="text-[13px]">Menu</div>
  </a>
</nav>
