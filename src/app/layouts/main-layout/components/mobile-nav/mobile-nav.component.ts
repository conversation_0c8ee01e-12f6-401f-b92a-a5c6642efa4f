import { Component, signal } from '@angular/core';
import { RouterLink, RouterModule } from '@angular/router';
import { ROLE_ACCOUNT, ROLE_AI } from '@core/constants';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroChevronLeft } from '@ng-icons/heroicons/outline';
import {
  MobileDrawerComponent,
  MobileHeaderComponent,
  SvgIconComponent,
} from '@shared/components';
import { HasRoleDirective } from '@shared/directives';

@Component({
  selector: 'app-mobile-nav',
  standalone: true,
  imports: [
    RouterModule,
    RouterLink,
    SvgIconComponent,
    NgIconsModule,
    HasRoleDirective,
  ],
  templateUrl: './mobile-nav.component.html',
  providers: [provideIcons({ heroChevronLeft })],
})
export class MobileNavComponent {
  readonly ROLE_ACCOUNT = ROLE_ACCOUNT;
  readonly ROLE_AI = ROLE_AI;
}
