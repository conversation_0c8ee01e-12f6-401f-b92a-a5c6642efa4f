import { CdkConnectedOverlay, CdkOverlayOrigin } from '@angular/cdk/overlay';
import { NgClass } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Router } from '@angular/router';
import { APP_ROUTES, AUTH_PATH } from '@core/constants';
import { AuthService } from '@core/services';
import { UserAiStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxPrefix,
  DxSnackBar,
  DxSuffix,
  DxTooltip,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroEllipsisHorizontalMini,
  heroXMarkMini,
} from '@ng-icons/heroicons/mini';
import {
  heroEye,
  heroEyeSlash,
  heroLockClosed,
} from '@ng-icons/heroicons/outline';
import {
  heroQuestionMarkCircleSolid,
  heroUserSolid,
} from '@ng-icons/heroicons/solid';
import { TYPE_PLAN } from '@shared/app.constant';
import { ClickOutsideDirective } from '@shared/directives';
import { CustomValidators } from '@shared/validators';
import moment from 'moment/moment';

@Component({
  selector: 'app-user-info-config',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgIconsModule,
    MatIconModule,
    MatMenuModule,
    CdkConnectedOverlay,
    CdkOverlayOrigin,
    ClickOutsideDirective,
    DxButton,
    DxError,
    DxFormField,
    DxInput,
    DxLabel,
    DxPrefix,
    DxSuffix,
    NgClass,
    DxTooltip,
  ],
  templateUrl: './user-info-config.component.html',
  styleUrls: ['./user-info-config.component.css'],
  providers: [
    provideIcons({
      heroQuestionMarkCircleSolid,
      heroEllipsisHorizontalMini,
      heroUserSolid,
      heroXMarkMini,
      heroLockClosed,
      heroEyeSlash,
      heroEye,
    }),
  ],
})
export class UserInfoConfigComponent implements OnInit {
  showMenuUserInfo = signal<boolean>(false);
  showConfirmPassword = signal<boolean>(false);
  showOldPassword = signal<boolean>(false);
  showPassword = signal<boolean>(false);

  formGroupChangePass!: FormGroup;
  changePassDialogRef: any;

  userAiStore = inject(UserAiStore);
  private router = inject(Router);
  private authService = inject(AuthService);
  private fb = inject(FormBuilder);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);

  get formControlPassword(): FormArray {
    return this.formGroupChangePass.get('password') as FormArray;
  }

  get formControlConfirmPassword(): FormArray {
    return this.formGroupChangePass.get('confirmPassword') as FormArray;
  }

  ngOnInit(): void {
    this.formGroupChangePass = this.fb.group(
      {
        password: [
          null,
          [
            Validators.required,
            Validators.minLength(8),
            CustomValidators.patternValidator(/\d/, { hasNumber: true }),
            CustomValidators.patternValidator(
              /[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/,
              { hasCapitalCase: true }
            ),
            CustomValidators.patternValidator(
              /[a-zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/,
              { hasSmallCase: true }
            ),
            CustomValidators.patternValidator(
              /[ `!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?~]/,
              {
                hasSpecialCharacters: true,
              }
            ),
          ],
        ],
        confirmPassword: ['', [Validators.required]],
        oldPassword: ['', [Validators.required]],
      },
      {
        validators: [
          CustomValidators.mustMatch('password', 'confirmPassword'),
          CustomValidators.mustNotMatch('oldPassword', 'password'),
        ],
      }
    );
  }

  changePassword(template: any) {
    this.changePassDialogRef = this.dialog.open(template, {
      data: {},
      width: '30vw',
      minWidth: '340px',
    });
  }

  closeDialogChangePass() {
    this.showOldPassword.set(false);
    this.showPassword.set(false);
    this.showConfirmPassword.set(false);
    this.changePassDialogRef.close();
    this.formGroupChangePass.reset();
  }

  toggleVisibility(key: string) {
    if (key === 'password') {
      this.showPassword.set(!this.showPassword());
    } else if (key === 'confirmPassword') {
      this.showConfirmPassword.set(!this.showConfirmPassword());
    } else if (key === 'oldPassword') {
      this.showOldPassword.set(!this.showOldPassword());
    }
  }

  onSavePasswordChange(data: any) {
    const body = {
      old_password: data.oldPassword.trim(),
      new_password: data.password.trim(),
    };
    this.authService.changePassword(body).subscribe({
      next: (res) => {
        this.closeDialogChangePass();
        this.snackBar.open(res.detail, '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }

  signOut() {
    this.authService.logout();
    this.router.navigate([APP_ROUTES.AUTH + '/' + AUTH_PATH.LOGIN]);
  }

  openMenuUserInfo() {
    this.showMenuUserInfo.set(!this.showMenuUserInfo());
  }

  handleUserPlan(): string | undefined {
    {
      return '';
    }
  }

  handleTimePlanTooltip(type: string) {
    let user = this.userAiStore.currentUser();
    let date = '';
    if (type === 'start_date') {
      if (user?.start_date) {
        date = 'Start date: ' + moment(user?.start_date).format('DD/MM/YYYY');
      } else {
        date = 'Start date: ' + 'None';
      }
    } else if (type === 'end_date') {
      if (user?.end_date) {
        date = 'End date: ' + moment(user?.end_date).format('DD/MM/YYYY');
      } else {
        date = 'End date: ' + 'None';
      }
    }
    return date;
  }

  handleClassesPlan() {
    const userPlan = this.userAiStore.currentUser()?.plan ?? 'None';
    if (!userPlan || typeof userPlan !== 'string') {
      return '';
    }
    if (userPlan.includes(TYPE_PLAN.FREE)) {
      return 'border-light-plan-free ring-2 ring-offset-1 ring-light-plan-free text-light-plan-free hover:bg-light-plan-free hover:text-light-white';
    } else if (userPlan.includes(TYPE_PLAN.START)) {
      return 'border-light-plan-starter ring-2 ring-offset-1 ring-light-plan-starter text-light-plan-starter hover:bg-light-plan-starter hover:text-light-white';
    } else if (userPlan.includes(TYPE_PLAN.ESSENTIAL)) {
      return 'border-light-plan-essential ring-2 ring-offset-1 ring-light-plan-essential text-light-plan-essential hover:bg-light-plan-essential hover:text-light-white';
    } else if (userPlan.includes(TYPE_PLAN.BUSINESS)) {
      return 'border-light-plan-business ring-2 ring-offset-1 ring-light-plan-business text-light-plan-business hover:bg-light-plan-business hover:text-light-white';
    } else {
      return '';
    }
  }
}
