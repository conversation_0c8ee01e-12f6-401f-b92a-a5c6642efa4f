# 📘 DxConnect - Code & Git Conventions

Tài liệu này quy định các quy ước chung khi làm việc trong dự án, nhằm đảm bảo code sạch, d<PERSON> đọc, d<PERSON> bảo trì và thống nhất giữa các thành viên trong nhóm.

---

## 📂 1. Quy ước đặt tên nhánh (Branch Naming)

| Loại công việc      | Cấu trúc                    | Ví dụ                       |
| ------------------- | --------------------------- | --------------------------- |
| Tính năng mới       | `feature/<tên-tính-năng>`   | `feature/leads`             |
| Sửa lỗi             | `fixbug/<mô-tả-lỗi>`        | `fixbug/create-lead-dialog` |
| Cải tiến / Refactor | `refactor/<mô-tả-refactor>` | `refactor/leads-interface`  |

> ✅ Sử dụng **kebab-case** cho phần mô tả chi tiết.  
> 🚫 Tránh dùng khoảng trắng, dấu chấm hoặc ký tự đặc biệt.

---

## 📝 2. Quy ước commit message

### 🔹 Cấu trúc:

> <mô-tả-ngắn-gọn-thay-đổi>

### Ví dụ:

> refactor interface in leads feature  
> fix create-lead-dialog not showing  
> add validation to lead form

---

## 📝 3. Code conventions

### 🔹 Cấu trúc thư mục (Folder Structure):

<pre lang="md">
📁 dx-connect-ui/
├── 📁 src/
│ ├── 📁 app/
│ │ ├── 📁 core/               # Các thành phần lõi, dùng chung toàn app
│ │ │ ├── 📁 constants/        # Const toàn cục
│ │ │ ├── 📁 factory/
│ │ │ ├── 📁 guards/
│ │ │ ├── 📁 interceptors/
│ │ │ ├── 📁 models/           # Model lõi, không chứa model tính năng cụ thể
│ │ │ ├── 📁 resolvers/
│ │ │ ├── 📁 services/         # Service lõi, không chứa service tính năng cụ thể
│ │ │ └── 📁 stores/
│ │ │   ├── 📁 models/         # Model cho store
│ │ │   └── ...                # Store (ngrx/signalStore)
│ │ ├── 📁 layouts/            # Giao diện khung của app
│ │ ├── 📁 shared/             # Thành phần tái sử dụng toàn app
│ │ │ ├── 📁 components/
│ │ │ ├── 📁 directives/
│ │ │ ├── 📁 models/           # Model tính năng
│ │ │ ├── 📁 pipes/
│ │ │ ├── 📁 services/         # Service tính năng
│ │ │ ├── 📁 utils/
│ │ │ └── 📁 validators/       # Custom validator cho form
│ │ ├── 📁 views/              # Các trang chính
│ │ ├── 📄 app.component.*     # Gốc component
│ │ ├── 📄 app_config.ts       # Provider toàn app (router, interceptor,...)
│ │ └── 📄 app_routes.ts       # Router toàn app
│ ├── 📁 assets/
│ ├── 📁 environments/
│ ├── 📁 i18n/                 # File ngôn ngữ (json)
│ ├── 📁 styles/               # Style toàn cục
│ ├── 📄 index.html
│ └── 📄 main.ts
├── ...
├── 📄 .gitignore
├── 📄 package.json
├── 📄 CONVENTIONS.md
└── 📄 README.md
</pre>

### 🔹 Cấu trúc code TypeScript Component:

```javascript
// ✅ Đảm bảo cập nhật các file index.ts để tối ưu việc import:
//    - Giúp code ngắn gọn và rõ mục đích
//    - Hạn chế import trực tiếp vào file cụ thể
//
// ✅ import { UserService, AIService } from "@shared/services";
// 🚫 import UserService from "@shared/services/user.service.ts";
// 🚫 import AIService from "@shared/services/ai.service.ts";

import ...;

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [...],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
// ⚠️ Không sử dụng BaseComponent do có nhiều hạn chế
export class AppComponent implements ... {
  // 🔹 Định nghĩa input / output signals (nếu có)
  //     - Quy ước: Tên output phải có prefix là "on"
  name = input.required("DxConnect");
  onSearch = output<any>();

  // 🔹 Định nghĩa signal-queries (viewChild, contentChild, ...) (nếu có)
  //     - Quy ước: public ở trên, private ở dưới
  detailDialog = viewChild("detailDialog");
  private createDialog = viewChild("createDialog");

  // 🔹 Định nghĩa signals
  gender = signal("male");
  age = signal(18);

  // 🔹 Computed properties (nếu có)
  newAge = computed(() => {
    return this.age() + 1;
  });

  // 🔹 Effects (nếu có)
  private effect = effect(() => {
    console.log("Effect triggered");
  });

  // 🔹 Biến thông thường (không phải signal)
  //     - Quy ước: Public ở trên, private ở dưới
  user: IUser;
  private ai: IAI;

  // 🔹 Inject dependencies bằng inject()
  //     - Quy ước: Public ở trên, private ở dưới
  userService = inject(UserService);
  private aiService = inject(AIService);

  // ✅ Chỉ dùng constructor để tạo context cho effect hoặc inject đặc biệt
  // ⚠️ Không nên dùng constructor để inject hoặc viết logic
  constructor() {}

  // 🔹 Lifecycle hooks (nếu sử dụng) theo thứ tự ưu tiên
  ngOnInit() {}
  ngOnChanges() {}
  ngDoCheck() {}
  ngAfterContentInit() {}
  ngAfterContentChecked() {}
  ngAfterViewInit() {}
  ngAfterViewChecked() {}
  afterNextRender() {}
  afterEveryRender() {}
  ngOnDestroy() {}

  // 🔹 Public methods
  handleSaveUser() {}

  // 🔹 Private methods (luôn đặt sau các method public)
  private reformatUserInfo(user: IUser) {}
}
```

✅ Lưu ý:

> Đảm bảo cập nhật các file index.ts để tối ưu việc import.  
> Đặt tên biến / hàm rõ ràng theo mục đích, tránh tên chung chung hoặc quá dài.  
> Tất cả các hàm private phải nằm phía dưới các hàm public.  
> Ưu tiên sử dụng các API mới như signal, computed, effect, viewChild, input, output từ Angular Signals thay vì cú pháp cũ.  
> Hạn chế tối đa sử dụng constructor cho việc inject hay xử lý logic ban đầu — đã có inject() và ngOnInit().

### 🔹 Cấu trúc code HTML Component:

✅ Lưu ý:

> Ưu tiên sử dụng các cú pháp control-flow mới trong Angular 19 thay cho cú pháp cũ.  
> Đối với component chính có nhiều dialog, nên tách riêng dialog thành các component độc lập thay vì viết trực tiếp trong template. Điều này giúp tránh việc trộn lẫn giữa logic giao diện chính và logic của các dialog.

### 🔹 Lưu ý chung:

> Ưu tiên áp dụng API và cú pháp mới của Angular 19.  
> Hạn chế tối đa việc sử dụng class style tùy chỉnh hoặc override style quá mức cần thiết.  
> Xoá console.log và các comment dư thừa sau khi debug hoặc copy code từ nơi khác (trừ khi giải thích cho logic phức tạp).  
> Dọn dẹp code: loại bỏ các biến, hàm, dependency không còn sử dụng.

> Khi viết code, luôn đặt tiêu chí: dễ đọc – dễ hiểu – dễ bảo trì lên hàng đầu.  
> Ngay cả khi code được tạo bởi bot, cần kiểm tra lại để đảm bảo rõ ràng và dễ tiếp cận. Luôn giữ mindset hướng tới khả năng mở rộng tính năng về sau: tổ chức code, chia file, tách component và phân tách logic sao cho hợp lý và bền vững.
