/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "outDir": "./dist/out-tsc",
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "moduleResolution": "bundler",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "baseUrl": "./src",
    "paths": {
      "@core/*": ["app/core/*"],
      "@views/*": ["app/views/*"],
      "@layouts/*": ["app/layouts/*"],
      "@shared/*": ["app/shared/*"],
      "@env/*": ["environments/*"],
      "@flow-editor/api": ["app/views/flow-editor/api/index"],
      "@flow-editor/components/*": ["app/views/flow-editor/components/*"],
      "@flow-editor/constant": ["app/views/flow-editor/constant/index"],
      "@flow-editor/hook": ["app/views/flow-editor/hook/index"],
      "@flow-editor/init": ["app/views/flow-editor/init/index"],
      "@flow-editor/model": ["app/views/flow-editor/model/index"],
      "@flow-editor/model/bo": ["app/views/flow-editor/model/bo/index"],
      "@flow-editor/store": ["app/views/flow-editor/store/index"],
      "@flow-editor/ui/*": ["app/views/flow-editor/ui/*"],
      "@flow-editor/utils/*": ["app/views/flow-editor/utils/*"],
      //
      "@flow-editor-v1/api": ["app/views/flow-editor-v1/api/index"],
      "@flow-editor-v1/components/*": ["app/views/flow-editor-v1/components/*"],
      "@flow-editor-v1/constant": ["app/views/flow-editor-v1/constant/index"],
      "@flow-editor-v1/hook": ["app/views/flow-editor-v1/hook/index"],
      "@flow-editor-v1/init": ["app/views/flow-editor-v1/init/index"],
      "@flow-editor-v1/model": ["app/views/flow-editor-v1/model/index"],
      "@flow-editor-v1/model/bo": ["app/views/flow-editor-v1/model/bo/index"],
      "@flow-editor-v1/store": ["app/views/flow-editor-v1/store/index"],
      "@flow-editor-v1/views/*": ["app/views/flow-editor-v1/views/*"],
      "@flow-editor-v1/utils/*": ["app/views/flow-editor-v1/utils/*"]
    },
    "jsx": "react"
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
