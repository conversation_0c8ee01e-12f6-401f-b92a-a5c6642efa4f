stages:
  - install-dependencies-dev
  - build-dev
  - deploy-to-dev
  - install-dependencies-labs
  - build-labs
  - deploy-to-labs
  - install-dependencies-prod
  - build-prod
  - deploy-to-prod

variables:
  GIT_DEPTH: 1

default:
  before_script:
    - export NVM_DIR="$HOME/.nvm"
    - source "$NVM_DIR/nvm.sh"
    - nvm use 20


install-dependencies-dev-job:
  stage: install-dependencies-dev
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
    policy: push
  script:
    - npm install
  tags:
    - DEV
  only:
    refs:
      - development
    changes:
      - package-lock.json
      - package.json

install-dependencies-labs-job:
  stage: install-dependencies-labs
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
    policy: push
  script:
    - npm install
  tags:
    - LABS
  only:
    refs:
      - labs
    changes:
      - package-lock.json
      - package.json

install-dependencies-prod-job:
  stage: install-dependencies-prod
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
    policy: push
  script:
    - npm install
  tags:
    - DEV
  only:
    refs:
      - main
    changes:
      - package-lock.json
      - package.json

build-dev-job:
  stage: build-dev
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - ./node_modules
    policy: pull
  script:
    - ng build -c development
  tags:
    - DEV
  only:
    - development
  artifacts:
    expire_in: 10m
    paths:
      - dist/dx-connect-ui/

build-labs-job:
  stage: build-labs
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - ./node_modules
    policy: pull
  script:
    - ng build -c labs
  tags:
    - DEV
  only:
    - labs
  artifacts:
    expire_in: 10m
    paths:
      - dist/dx-connect-ui/

build-prod-job:
  stage: build-prod
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - ./node_modules
    policy: pull
  script:
    - ng build -c production
  tags:
    - DEV
  only:
    - main
  artifacts:
    expire_in: 10m
    paths:
      - dist/dx-connect-ui/

deploy-to-dev-job:
  stage: deploy-to-dev
  before_script:
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
  script:
    - ssh-add <(echo "$CI_DEV_SSH_KEY")
    - |
      while read IP; do
        ssh -v $CI_DEV_USER@$IP 'rm -rf /var/www/html/dx-connect-ui-dev/*' 2>&1
        rsync -ahrvz dist/dx-connect-ui/browser/* $CI_DEV_USER@$IP:$CI_DEV_SOURCE_PATH
        if [ $? -ne 0 ]; then
          exit 1
        fi
      done < $CI_DEV_IP_LIST
  tags:
    - DEV
  only:
    - development

deploy-to-labs-job:
  stage: deploy-to-labs
  before_script:
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
  script:
    - ssh-add <(echo "$CI_LABS_SSH_KEY")
    - |
      while read IP; do
        ssh -v $CI_LABS_USER@$IP 'rm -rf /var/www/html/dx-connect-ui-labs/*' 2>&1
        rsync -ahrvz dist/dx-connect-ui/browser/* $CI_LABS_USER@$IP:$CI_LABS_SOURCE_PATH
        if [ $? -ne 0 ]; then
          exit 1
        fi
      done < $CI_LABS_IP_LIST
  tags:
    - LABS
  only:
    - labs

deploy-to-prod-job:
  stage: deploy-to-prod
  before_script:
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
  script:
    - ssh-add <(echo "$CI_PROD_SSH_KEY")
    - |
      while read IP; do
        ssh -v $CI_PROD_USER@$IP 'rm -rf /var/www/html/dx-connect-ui-prod/*' 2>&1
        rsync -ahrvz dist/dx-connect-ui/browser/* $CI_PROD_USER@$IP:$CI_PROD_SOURCE_PATH
        if [ $? -ne 0 ]; then
          exit 1
        fi
      done < $CI_PROD_IP_LIST
  tags:
    - DEV
  only:
    - main